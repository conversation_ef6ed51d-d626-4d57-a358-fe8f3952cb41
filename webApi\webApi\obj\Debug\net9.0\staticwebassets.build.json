{"Version": 1, "Hash": "MZ0/GipLeP8yAHy8/UmUQx9JBYV/+B/ymtnO5WLphMQ=", "Source": "webApi", "BasePath": "_content/webApi", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "webApi\\wwwroot", "Source": "webApi", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "Pattern": "**"}], "Assets": [{"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\ph3rqhnt6w-knc7nr4hqg.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/341c09fc1cb5436caa484b2c0e1dd5ca#[.{fingerprint=knc7nr4hqg}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\341c09fc1cb5436caa484b2c0e1dd5ca.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "72uezgg7hx", "Integrity": "UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\341c09fc1cb5436caa484b2c0e1dd5ca.txt", "FileLength": 1927, "LastWriteTime": "2025-07-05T18:39:02+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\v25brddiup-knc7nr4hqg.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/8df256e6ebd646bea1759f1a8c0c042e#[.{fingerprint=knc7nr4hqg}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\8df256e6ebd646bea1759f1a8c0c042e.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "72uezgg7hx", "Integrity": "UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\8df256e6ebd646bea1759f1a8c0c042e.txt", "FileLength": 1927, "LastWriteTime": "2025-07-05T18:39:02+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\xoelc6sdnq-dz1rjozczi.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/attachments/b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2)#[.{fingerprint=dz1rjo<PERSON>cz<PERSON>}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "00icdbvmee", "Integrity": "II89AIAfepVyxMAEBSxXRGF+cpbNgb15nTDmDlReLDI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt", "FileLength": 7549, "LastWriteTime": "2025-07-05T18:39:02+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\4b6c9174-7b58-40fb-8ffb-0b6673929573_تقارير صوره نموذدج.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/attachments/4b6c9174-7b58-40fb-8ffb-0b6673929573_تقارير صوره نموذدج#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\attachments\\4b6c9174-7b58-40fb-8ffb-0b6673929573_تقارير صوره نموذدج.webp", "FileLength": 97050, "LastWriteTime": "2025-06-22T15:42:44+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\72450ef0-67b2-45be-b7c4-0775713fe5e8_ww.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/attachments/72450ef0-67b2-45be-b7c4-0775713fe5e8_ww#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pbhq5urrew", "Integrity": "Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\attachments\\72450ef0-67b2-45be-b7c4-0775713fe5e8_ww.pdf", "FileLength": 1607, "LastWriteTime": "2025-06-20T19:14:11+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\92016494-8eeb-4f26-bc61-0307b2cfdeca_eee.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/attachments/92016494-8eeb-4f26-bc61-0307b2cfdeca_eee#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "q5n9oh9rw1", "Integrity": "gtawLKU9MeLXPsHO2bX9QSQhTx2tl05g/IqIzJXrKJE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\attachments\\92016494-8eeb-4f26-bc61-0307b2cfdeca_eee.pdf", "FileLength": 1091955, "LastWriteTime": "2025-06-20T21:20:26+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/attachments/b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2)#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "dz1rjozczi", "Integrity": "NzpxOt9Y0vrfe1Ss2JIpNPRWsukHFzNVN/8OT8y2hXE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\attachments\\b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt", "FileLength": 28469, "LastWriteTime": "2025-06-20T19:16:26+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\22efc5c1e7a043d68729a9b15788b8d0.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/22efc5c1e7a043d68729a9b15788b8d0#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pbhq5urrew", "Integrity": "Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\22efc5c1e7a043d68729a9b15788b8d0.pdf", "FileLength": 1607, "LastWriteTime": "2025-07-03T01:41:42+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\341c09fc1cb5436caa484b2c0e1dd5ca.txt", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/341c09fc1cb5436caa484b2c0e1dd5ca#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "knc7nr4hqg", "Integrity": "MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\341c09fc1cb5436caa484b2c0e1dd5ca.txt", "FileLength": 7630, "LastWriteTime": "2025-06-24T14:41:24+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\3982df413cf14caf9da852e86398c3f2.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/3982df413cf14caf9da852e86398c3f2#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "yqs7wwjalp", "Integrity": "St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\3982df413cf14caf9da852e86398c3f2.pdf", "FileLength": 515576, "LastWriteTime": "2025-07-03T17:35:51+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\57cc983c754e400fa8630e31eae1bc49.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/57cc983c754e400fa8630e31eae1bc49#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "yqs7wwjalp", "Integrity": "St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\57cc983c754e400fa8630e31eae1bc49.pdf", "FileLength": 515576, "LastWriteTime": "2025-07-03T01:09:13+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\776d38afde4e4189ad9a86c615b5ab90.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/776d38afde4e4189ad9a86c615b5ab90#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6mabyag1xn", "Integrity": "gutJxmAixzCz0cRFOg/Rh+oSJmGOnIzJSsRszMHIgzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\776d38afde4e4189ad9a86c615b5ab90.pdf", "FileLength": 48194, "LastWriteTime": "2025-06-23T16:05:03+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\8375e14b17a341efb982c00a4344db2c.docx", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/8375e14b17a341efb982c00a4344db2c#[.{fingerprint}]?.docx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "v8f3tesalf", "Integrity": "7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\8375e14b17a341efb982c00a4344db2c.docx", "FileLength": 2149873, "LastWriteTime": "2025-07-03T01:46:36+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\8df256e6ebd646bea1759f1a8c0c042e.txt", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/8df256e6ebd646bea1759f1a8c0c042e#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "knc7nr4hqg", "Integrity": "MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\8df256e6ebd646bea1759f1a8c0c042e.txt", "FileLength": 7630, "LastWriteTime": "2025-06-23T16:18:46+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\b90557820bb340bd96d5fc46c4f97c02.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/b90557820bb340bd96d5fc46c4f97c02#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pbhq5urrew", "Integrity": "Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\b90557820bb340bd96d5fc46c4f97c02.pdf", "FileLength": 1607, "LastWriteTime": "2025-06-23T15:48:23+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\bff0abcb17524585a06eee3255b1f49a.docx", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/bff0abcb17524585a06eee3255b1f49a#[.{fingerprint}]?.docx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "v8f3tesalf", "Integrity": "7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\bff0abcb17524585a06eee3255b1f49a.docx", "FileLength": 2149873, "LastWriteTime": "2025-06-29T00:48:53+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\d6b0e189847a4d1293e9fbfe52e086d1.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/d6b0e189847a4d1293e9fbfe52e086d1#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "o5mrvqq7ms", "Integrity": "7C+EtpvaB+JITP/bPh6Hrmekms4V02jhufpz+3kKFkc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\d6b0e189847a4d1293e9fbfe52e086d1.pdf", "FileLength": 147799, "LastWriteTime": "2025-06-29T00:59:59+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\e1fa76fc737043cd963851ab3d4f50db.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/e1fa76fc737043cd963851ab3d4f50db#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6mabyag1xn", "Integrity": "gutJxmAixzCz0cRFOg/Rh+oSJmGOnIzJSsRszMHIgzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\e1fa76fc737043cd963851ab3d4f50db.pdf", "FileLength": 48194, "LastWriteTime": "2025-06-23T18:32:56+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\fa8afc1d2d664eb2a6c2057eb6a30012.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/fa8afc1d2d664eb2a6c2057eb6a30012#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jilall62b3", "Integrity": "axSfQ4GEfDpg+365QvDt55vNqdPCE6fwB6NRIU/0hOM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\fa8afc1d2d664eb2a6c2057eb6a30012.pdf", "FileLength": 63019, "LastWriteTime": "2025-06-24T14:04:39+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\1870288851874090b203bf27c9321fed.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/1870288851874090b203bf27c9321fed#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ia1swkvltk", "Integrity": "duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\1870288851874090b203bf27c9321fed.png", "FileLength": 885730, "LastWriteTime": "2025-06-24T13:55:30+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\312205ccd18f4650bef24653fbf9f64a.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/312205ccd18f4650bef24653fbf9f64a#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "eb9ax9ytkh", "Integrity": "Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\312205ccd18f4650bef24653fbf9f64a.png", "FileLength": 726022, "LastWriteTime": "2025-06-24T14:45:00+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\3a0ace1c93e946709f42cbfb2279e65f.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/3a0ace1c93e946709f42cbfb2279e65f#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jj02sd2q5h", "Integrity": "wQB9mCk9PpicYOKXGpuIQC0mLLuKirO2SQ+vB/qwMbU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\3a0ace1c93e946709f42cbfb2279e65f.png", "FileLength": 1280957, "LastWriteTime": "2025-06-23T17:18:40+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\4886e04ad10545cfa694a3d3f06d85ec.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/4886e04ad10545cfa694a3d3f06d85ec#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "qt5iiuu92c", "Integrity": "/pffW2qzqA9CBtmme640Xn8tkV+gYDbxpODuhXHgE+s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\4886e04ad10545cfa694a3d3f06d85ec.png", "FileLength": 14596, "LastWriteTime": "2025-06-23T15:16:46+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\4c2fcb8d51f0455fad91d5629bc65ef4.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/4c2fcb8d51f0455fad91d5629bc65ef4#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "160dqoac5z", "Integrity": "52Oa6P2DToTBO8LaH35EDZ/Ez/Hb2ZEjhbM/+dpRNYY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\4c2fcb8d51f0455fad91d5629bc65ef4.png", "FileLength": 853270, "LastWriteTime": "2025-06-23T17:13:46+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\4ca45322624947dfa4c15343a522edaa.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/4ca45322624947dfa4c15343a522edaa#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "eb9ax9ytkh", "Integrity": "Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\4ca45322624947dfa4c15343a522edaa.png", "FileLength": 726022, "LastWriteTime": "2025-06-23T17:05:46+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\53e82cd4346e4263bc415d38312d7b49.jpg", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/53e82cd4346e4263bc415d38312d7b49#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "csvf1bo9y0", "Integrity": "VEQKFnXTwXCWL0eSgISkz4zFS8E4pb7n1re/Jm6cR10=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\53e82cd4346e4263bc415d38312d7b49.jpg", "FileLength": 158471, "LastWriteTime": "2025-06-24T14:43:26+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\610efb0bb9a745baa5a60fbe8b50789a.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/610efb0bb9a745baa5a60fbe8b50789a#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\610efb0bb9a745baa5a60fbe8b50789a.webp", "FileLength": 97050, "LastWriteTime": "2025-06-29T01:16:18+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\631a190e6e0e4858a805e2bf75d6d33a.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/631a190e6e0e4858a805e2bf75d6d33a#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ia1swkvltk", "Integrity": "duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\631a190e6e0e4858a805e2bf75d6d33a.png", "FileLength": 885730, "LastWriteTime": "2025-06-24T15:23:15+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\6d796a2e432c48e3b57b61a7cc64e85c.PNG", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/6d796a2e432c48e3b57b61a7cc64e85c#[.{fingerprint}]?.PNG", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c69f109cb6", "Integrity": "zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\6d796a2e432c48e3b57b61a7cc64e85c.PNG", "FileLength": 1011297, "LastWriteTime": "2025-06-23T14:50:15+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\7054f84a3f8f4a409e0e9290c878fbcc.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/7054f84a3f8f4a409e0e9290c878fbcc#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1nfn82ehfv", "Integrity": "E+GMZQsbl/qQn7wQJyD/nl/muj/Q8tsck4DYoiixGnM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\7054f84a3f8f4a409e0e9290c878fbcc.png", "FileLength": 1345158, "LastWriteTime": "2025-06-24T14:30:26+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\713c55958e434619b8dbd17a5ef62b7e.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/713c55958e434619b8dbd17a5ef62b7e#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ia1swkvltk", "Integrity": "duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\713c55958e434619b8dbd17a5ef62b7e.png", "FileLength": 885730, "LastWriteTime": "2025-06-23T16:21:07+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\8422115944564f0d9b5336c152d37758.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/8422115944564f0d9b5336c152d37758#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pnn48fdjlm", "Integrity": "xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\8422115944564f0d9b5336c152d37758.png", "FileLength": 196383, "LastWriteTime": "2025-06-23T15:04:29+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\86f9084a89de49dcb333d63c0bf993b9.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/86f9084a89de49dcb333d63c0bf993b9#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "3oz33zfofh", "Integrity": "UxYff5Z/UZ8/0B2kpJToiiF7lnlcm5vqlH7G0bOO6Bw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\86f9084a89de49dcb333d63c0bf993b9.webp", "FileLength": 87614, "LastWriteTime": "2025-06-23T16:15:05+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\87959591ff1d4bb9b6d3cf9617456555.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/87959591ff1d4bb9b6d3cf9617456555#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "45mp2a8mvz", "Integrity": "qBmDm8T4QaftiVzlhIA0uT+wdWrw6rn1MEAx8WEsFfg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\87959591ff1d4bb9b6d3cf9617456555.png", "FileLength": 659876, "LastWriteTime": "2025-06-24T14:16:58+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\8e4b91c5d9284cea9b84c8b70c264c79.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/8e4b91c5d9284cea9b84c8b70c264c79#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "eb9ax9ytkh", "Integrity": "Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\8e4b91c5d9284cea9b84c8b70c264c79.png", "FileLength": 726022, "LastWriteTime": "2025-06-24T14:36:28+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\97b49fcfbb844432a4b311cb199e86f9.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/97b49fcfbb844432a4b311cb199e86f9#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "evylwqip3e", "Integrity": "NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\97b49fcfbb844432a4b311cb199e86f9.png", "FileLength": 86139, "LastWriteTime": "2025-07-03T01:54:38+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9854cf13659b4bb6a2141d0824af4666.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/9854cf13659b4bb6a2141d0824af4666#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "evylwqip3e", "Integrity": "NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\9854cf13659b4bb6a2141d0824af4666.png", "FileLength": 86139, "LastWriteTime": "2025-06-23T15:07:12+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9a342b7e0e404095a917be1fc40364ae.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/9a342b7e0e404095a917be1fc40364ae#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\9a342b7e0e404095a917be1fc40364ae.png", "FileLength": 195627, "LastWriteTime": "2025-06-23T02:50:41+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9df790c0db6d4d7e9f302edc88b5804c.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/9df790c0db6d4d7e9f302edc88b5804c#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "r7rmd0638a", "Integrity": "uX8tYvCPYY6gXw26SuClSH9rciWhnx9t0oz+T27ZDSI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\9df790c0db6d4d7e9f302edc88b5804c.png", "FileLength": 155911, "LastWriteTime": "2025-06-23T16:15:51+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\a64a54a1298e4b8c81bf46d705996980.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/a64a54a1298e4b8c81bf46d705996980#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "160dqoac5z", "Integrity": "52Oa6P2DToTBO8LaH35EDZ/Ez/Hb2ZEjhbM/+dpRNYY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\a64a54a1298e4b8c81bf46d705996980.png", "FileLength": 853270, "LastWriteTime": "2025-06-24T13:31:15+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\aa469b655153465e9cd1265d5a36a8dd.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/aa469b655153465e9cd1265d5a36a8dd#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\aa469b655153465e9cd1265d5a36a8dd.png", "FileLength": 195627, "LastWriteTime": "2025-06-23T02:41:59+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\aa812b7960134dd380c2c5fcc1286289.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/aa812b7960134dd380c2c5fcc1286289#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "eb9ax9ytkh", "Integrity": "Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\aa812b7960134dd380c2c5fcc1286289.png", "FileLength": 726022, "LastWriteTime": "2025-06-23T18:31:04+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\aee04329d556487dbb70eccf248638db.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/aee04329d556487dbb70eccf248638db#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pnn48fdjlm", "Integrity": "xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\aee04329d556487dbb70eccf248638db.png", "FileLength": 196383, "LastWriteTime": "2025-07-03T01:48:56+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\bd28a12d15e24fb48511eb051459a6bc.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/bd28a12d15e24fb48511eb051459a6bc#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ia1swkvltk", "Integrity": "duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\bd28a12d15e24fb48511eb051459a6bc.png", "FileLength": 885730, "LastWriteTime": "2025-06-23T16:07:29+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c12085ba2bf34fb09f79c7850e7a6e87.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/c12085ba2bf34fb09f79c7850e7a6e87#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "lb3ihrmur6", "Integrity": "r0HmmgXg80L5bGekrx0SHvXKeEOeDQvth5XrNkEGbDw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\c12085ba2bf34fb09f79c7850e7a6e87.png", "FileLength": 62981, "LastWriteTime": "2025-06-23T15:16:53+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c3cf257303cd47dfa6018f1b87bdae6c.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/c3cf257303cd47dfa6018f1b87bdae6c#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ljkl4o8aee", "Integrity": "v8CNdUHekPmKC+kXCvGU9mz/SMbNlbVn/2Cx0uizk9U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\c3cf257303cd47dfa6018f1b87bdae6c.png", "FileLength": 3582183, "LastWriteTime": "2025-06-23T18:33:51+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c4be6ded3cb74eeeb91a53dd8f79d074.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/c4be6ded3cb74eeeb91a53dd8f79d074#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\c4be6ded3cb74eeeb91a53dd8f79d074.webp", "FileLength": 97050, "LastWriteTime": "2025-06-23T02:38:51+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\ca0161389fa346e6be985efb101cf3f7.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/ca0161389fa346e6be985efb101cf3f7#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "45mp2a8mvz", "Integrity": "qBmDm8T4QaftiVzlhIA0uT+wdWrw6rn1MEAx8WEsFfg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\ca0161389fa346e6be985efb101cf3f7.png", "FileLength": 659876, "LastWriteTime": "2025-06-24T15:28:24+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\db5e4060478d44bdbd0eda3592b741c4.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/db5e4060478d44bdbd0eda3592b741c4#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\db5e4060478d44bdbd0eda3592b741c4.png", "FileLength": 195627, "LastWriteTime": "2025-06-29T01:16:11+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\e97afe0fea7d46fa90ae97941b717777.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/e97afe0fea7d46fa90ae97941b717777#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\e97afe0fea7d46fa90ae97941b717777.png", "FileLength": 195627, "LastWriteTime": "2025-06-23T14:51:09+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\eb79cd51dc3d43dbbda06c629027ec98.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/eb79cd51dc3d43dbbda06c629027ec98#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bm200vq19x", "Integrity": "Q8l8GUhamHBK0YbIi2QYsf3keVpACWQScC1PakHwt/o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\eb79cd51dc3d43dbbda06c629027ec98.png", "FileLength": 51973, "LastWriteTime": "2025-06-23T15:25:34+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\eec2598549b14c3cb6efa0a6ca5bfe8d.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/eec2598549b14c3cb6efa0a6ca5bfe8d#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jj02sd2q5h", "Integrity": "wQB9mCk9PpicYOKXGpuIQC0mLLuKirO2SQ+vB/qwMbU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\eec2598549b14c3cb6efa0a6ca5bfe8d.png", "FileLength": 1280957, "LastWriteTime": "2025-06-24T14:09:22+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\f706c5792702492a90e00628f2eb9ba6.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/f706c5792702492a90e00628f2eb9ba6#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\f706c5792702492a90e00628f2eb9ba6.webp", "FileLength": 97050, "LastWriteTime": "2025-06-23T02:38:42+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\f97829c0fe164622a2e1eae38e40117a.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/f97829c0fe164622a2e1eae38e40117a#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "t3dbuv7mfy", "Integrity": "GcH838F74sMYRZGDHUKT/emduBGDC+GW1yLAVMoCHIc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\f97829c0fe164622a2e1eae38e40117a.png", "FileLength": 216207, "LastWriteTime": "2025-06-23T15:48:04+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\fb22d922074c4743951c4e34232de868.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/fb22d922074c4743951c4e34232de868#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "eb9ax9ytkh", "Integrity": "Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\fb22d922074c4743951c4e34232de868.png", "FileLength": 726022, "LastWriteTime": "2025-06-23T17:58:32+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\fcbd8c0981174ebb8f826b95d2ac61c7.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/fcbd8c0981174ebb8f826b95d2ac61c7#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pnn48fdjlm", "Integrity": "xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\fcbd8c0981174ebb8f826b95d2ac61c7.png", "FileLength": 196383, "LastWriteTime": "2025-06-23T15:50:37+00:00"}], "Endpoints": [{"Route": "uploads/attachments/4b6c9174-7b58-40fb-8ffb-0b6673929573_تقارير صوره نموذدج.5za9gpxx61.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\4b6c9174-7b58-40fb-8ffb-0b6673929573_تقارير صوره نموذدج.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Sun, 22 Jun 2025 15:42:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5za9gpxx61"}, {"Name": "label", "Value": "uploads/attachments/4b6c9174-7b58-40fb-8ffb-0b6673929573_تقارير صوره نموذدج.webp"}, {"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/attachments/4b6c9174-7b58-40fb-8ffb-0b6673929573_تقارير صوره نموذدج.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\4b6c9174-7b58-40fb-8ffb-0b6673929573_تقارير صوره نموذدج.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Sun, 22 Jun 2025 15:42:44 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/attachments/72450ef0-67b2-45be-b7c4-0775713fe5e8_ww.pbhq5urrew.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\72450ef0-67b2-45be-b7c4-0775713fe5e8_ww.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1607"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4=\""}, {"Name": "Last-Modified", "Value": "Fri, 20 Jun 2025 19:14:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pbhq5urrew"}, {"Name": "label", "Value": "uploads/attachments/72450ef0-67b2-45be-b7c4-0775713fe5e8_ww.pdf"}, {"Name": "integrity", "Value": "sha256-Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4="}]}, {"Route": "uploads/attachments/72450ef0-67b2-45be-b7c4-0775713fe5e8_ww.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\72450ef0-67b2-45be-b7c4-0775713fe5e8_ww.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1607"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4=\""}, {"Name": "Last-Modified", "Value": "Fri, 20 Jun 2025 19:14:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4="}]}, {"Route": "uploads/attachments/92016494-8eeb-4f26-bc61-0307b2cfdeca_eee.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\92016494-8eeb-4f26-bc61-0307b2cfdeca_eee.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1091955"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"gtawLKU9MeLXPsHO2bX9QSQhTx2tl05g/IqIzJXrKJE=\""}, {"Name": "Last-Modified", "Value": "Fri, 20 Jun 2025 21:20:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gtawLKU9MeLXPsHO2bX9QSQhTx2tl05g/IqIzJXrKJE="}]}, {"Route": "uploads/attachments/92016494-8eeb-4f26-bc61-0307b2cfdeca_eee.q5n9oh9rw1.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\92016494-8eeb-4f26-bc61-0307b2cfdeca_eee.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1091955"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"gtawLKU9MeLXPsHO2bX9QSQhTx2tl05g/IqIzJXrKJE=\""}, {"Name": "Last-Modified", "Value": "Fri, 20 Jun 2025 21:20:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q5n9oh9rw1"}, {"Name": "label", "Value": "uploads/attachments/92016494-8eeb-4f26-bc61-0307b2cfdeca_eee.pdf"}, {"Name": "integrity", "Value": "sha256-gtawLKU9MeLXPsHO2bX9QSQhTx2tl05g/IqIzJXrKJE="}]}, {"Route": "uploads/attachments/b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).dz1rjozczi.txt", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\xoelc6sdnq-dz1rjozczi.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000132450331"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7549"}, {"Name": "ETag", "Value": "\"II89AIAfepVyxMAEBSxXRGF+cpbNgb15nTDmDlReLDI=\""}, {"Name": "Last-Modified", "Value": "Sat, 05 Jul 2025 18:39:02 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"NzpxOt9Y0vrfe1Ss2JIpNPRWsukHFzNVN/8OT8y2hXE=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dz1rjozczi"}, {"Name": "label", "Value": "uploads/attachments/b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt"}, {"Name": "integrity", "Value": "sha256-NzpxOt9Y0vrfe1Ss2JIpNPRWsukHFzNVN/8OT8y2hXE="}]}, {"Route": "uploads/attachments/b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).dz1rjozczi.txt", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28469"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"NzpxOt9Y0vrfe1Ss2JIpNPRWsukHFzNVN/8OT8y2hXE=\""}, {"Name": "Last-Modified", "Value": "Fri, 20 Jun 2025 19:16:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dz1rjozczi"}, {"Name": "label", "Value": "uploads/attachments/b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt"}, {"Name": "integrity", "Value": "sha256-NzpxOt9Y0vrfe1Ss2JIpNPRWsukHFzNVN/8OT8y2hXE="}]}, {"Route": "uploads/attachments/b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).dz1rjozczi.txt.gz", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\xoelc6sdnq-dz1rjozczi.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7549"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"II89AIAfepVyxMAEBSxXRGF+cpbNgb15nTDmDlReLDI=\""}, {"Name": "Last-Modified", "Value": "Sat, 05 Jul 2025 18:39:02 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dz1rjozczi"}, {"Name": "label", "Value": "uploads/attachments/b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt.gz"}, {"Name": "integrity", "Value": "sha256-II89AIAfepVyxMAEBSxXRGF+cpbNgb15nTDmDlReLDI="}]}, {"Route": "uploads/attachments/b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\xoelc6sdnq-dz1rjozczi.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000132450331"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7549"}, {"Name": "ETag", "Value": "\"II89AIAfepVyxMAEBSxXRGF+cpbNgb15nTDmDlReLDI=\""}, {"Name": "Last-Modified", "Value": "Sat, 05 Jul 2025 18:39:02 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"NzpxOt9Y0vrfe1Ss2JIpNPRWsukHFzNVN/8OT8y2hXE=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NzpxOt9Y0vrfe1Ss2JIpNPRWsukHFzNVN/8OT8y2hXE="}]}, {"Route": "uploads/attachments/b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28469"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"NzpxOt9Y0vrfe1Ss2JIpNPRWsukHFzNVN/8OT8y2hXE=\""}, {"Name": "Last-Modified", "Value": "Fri, 20 Jun 2025 19:16:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NzpxOt9Y0vrfe1Ss2JIpNPRWsukHFzNVN/8OT8y2hXE="}]}, {"Route": "uploads/attachments/b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt.gz", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\xoelc6sdnq-dz1rjozczi.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7549"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"II89AIAfepVyxMAEBSxXRGF+cpbNgb15nTDmDlReLDI=\""}, {"Name": "Last-Modified", "Value": "Sat, 05 Jul 2025 18:39:02 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-II89AIAfepVyxMAEBSxXRGF+cpbNgb15nTDmDlReLDI="}]}, {"Route": "uploads/documents/22efc5c1e7a043d68729a9b15788b8d0.pbhq5urrew.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\22efc5c1e7a043d68729a9b15788b8d0.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1607"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Jul 2025 01:41:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pbhq5urrew"}, {"Name": "label", "Value": "uploads/documents/22efc5c1e7a043d68729a9b15788b8d0.pdf"}, {"Name": "integrity", "Value": "sha256-Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4="}]}, {"Route": "uploads/documents/22efc5c1e7a043d68729a9b15788b8d0.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\22efc5c1e7a043d68729a9b15788b8d0.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1607"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Jul 2025 01:41:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4="}]}, {"Route": "uploads/documents/341c09fc1cb5436caa484b2c0e1dd5ca.knc7nr4hqg.txt", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\ph3rqhnt6w-knc7nr4hqg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000518672199"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1927"}, {"Name": "ETag", "Value": "\"UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4=\""}, {"Name": "Last-Modified", "Value": "Sat, 05 Jul 2025 18:39:02 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "knc7nr4hqg"}, {"Name": "label", "Value": "uploads/documents/341c09fc1cb5436caa484b2c0e1dd5ca.txt"}, {"Name": "integrity", "Value": "sha256-MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY="}]}, {"Route": "uploads/documents/341c09fc1cb5436caa484b2c0e1dd5ca.knc7nr4hqg.txt", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\341c09fc1cb5436caa484b2c0e1dd5ca.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7630"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:41:24 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "knc7nr4hqg"}, {"Name": "label", "Value": "uploads/documents/341c09fc1cb5436caa484b2c0e1dd5ca.txt"}, {"Name": "integrity", "Value": "sha256-MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY="}]}, {"Route": "uploads/documents/341c09fc1cb5436caa484b2c0e1dd5ca.knc7nr4hqg.txt.gz", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\ph3rqhnt6w-knc7nr4hqg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1927"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4=\""}, {"Name": "Last-Modified", "Value": "Sat, 05 Jul 2025 18:39:02 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "knc7nr4hqg"}, {"Name": "label", "Value": "uploads/documents/341c09fc1cb5436caa484b2c0e1dd5ca.txt.gz"}, {"Name": "integrity", "Value": "sha256-UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4="}]}, {"Route": "uploads/documents/341c09fc1cb5436caa484b2c0e1dd5ca.txt", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\ph3rqhnt6w-knc7nr4hqg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000518672199"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1927"}, {"Name": "ETag", "Value": "\"UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4=\""}, {"Name": "Last-Modified", "Value": "Sat, 05 Jul 2025 18:39:02 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY="}]}, {"Route": "uploads/documents/341c09fc1cb5436caa484b2c0e1dd5ca.txt", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\341c09fc1cb5436caa484b2c0e1dd5ca.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7630"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:41:24 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY="}]}, {"Route": "uploads/documents/341c09fc1cb5436caa484b2c0e1dd5ca.txt.gz", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\ph3rqhnt6w-knc7nr4hqg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1927"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4=\""}, {"Name": "Last-Modified", "Value": "Sat, 05 Jul 2025 18:39:02 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4="}]}, {"Route": "uploads/documents/3982df413cf14caf9da852e86398c3f2.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\3982df413cf14caf9da852e86398c3f2.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "515576"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Jul 2025 17:35:51 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo="}]}, {"Route": "uploads/documents/3982df413cf14caf9da852e86398c3f2.yqs7wwjalp.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\3982df413cf14caf9da852e86398c3f2.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "515576"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Jul 2025 17:35:51 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yqs7wwjalp"}, {"Name": "label", "Value": "uploads/documents/3982df413cf14caf9da852e86398c3f2.pdf"}, {"Name": "integrity", "Value": "sha256-St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo="}]}, {"Route": "uploads/documents/57cc983c754e400fa8630e31eae1bc49.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\57cc983c754e400fa8630e31eae1bc49.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "515576"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Jul 2025 01:09:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo="}]}, {"Route": "uploads/documents/57cc983c754e400fa8630e31eae1bc49.yqs7wwjalp.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\57cc983c754e400fa8630e31eae1bc49.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "515576"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Jul 2025 01:09:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yqs7wwjalp"}, {"Name": "label", "Value": "uploads/documents/57cc983c754e400fa8630e31eae1bc49.pdf"}, {"Name": "integrity", "Value": "sha256-St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo="}]}, {"Route": "uploads/documents/776d38afde4e4189ad9a86c615b5ab90.6mabyag1xn.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\776d38afde4e4189ad9a86c615b5ab90.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "48194"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"gutJxmAixzCz0cRFOg/Rh+oSJmGOnIzJSsRszMHIgzQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 16:05:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6mabyag1xn"}, {"Name": "label", "Value": "uploads/documents/776d38afde4e4189ad9a86c615b5ab90.pdf"}, {"Name": "integrity", "Value": "sha256-gutJxmAixzCz0cRFOg/Rh+oSJmGOnIzJSsRszMHIgzQ="}]}, {"Route": "uploads/documents/776d38afde4e4189ad9a86c615b5ab90.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\776d38afde4e4189ad9a86c615b5ab90.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "48194"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"gutJxmAixzCz0cRFOg/Rh+oSJmGOnIzJSsRszMHIgzQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 16:05:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gutJxmAixzCz0cRFOg/Rh+oSJmGOnIzJSsRszMHIgzQ="}]}, {"Route": "uploads/documents/8375e14b17a341efb982c00a4344db2c.docx", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\8375e14b17a341efb982c00a4344db2c.docx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2149873"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"}, {"Name": "ETag", "Value": "\"7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Jul 2025 01:46:36 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8="}]}, {"Route": "uploads/documents/8375e14b17a341efb982c00a4344db2c.v8f3tesalf.docx", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\8375e14b17a341efb982c00a4344db2c.docx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2149873"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"}, {"Name": "ETag", "Value": "\"7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Jul 2025 01:46:36 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v8f3tesalf"}, {"Name": "label", "Value": "uploads/documents/8375e14b17a341efb982c00a4344db2c.docx"}, {"Name": "integrity", "Value": "sha256-7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8="}]}, {"Route": "uploads/documents/8df256e6ebd646bea1759f1a8c0c042e.knc7nr4hqg.txt", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\v25brddiup-knc7nr4hqg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000518672199"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1927"}, {"Name": "ETag", "Value": "\"UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4=\""}, {"Name": "Last-Modified", "Value": "Sat, 05 Jul 2025 18:39:02 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "knc7nr4hqg"}, {"Name": "label", "Value": "uploads/documents/8df256e6ebd646bea1759f1a8c0c042e.txt"}, {"Name": "integrity", "Value": "sha256-MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY="}]}, {"Route": "uploads/documents/8df256e6ebd646bea1759f1a8c0c042e.knc7nr4hqg.txt", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\8df256e6ebd646bea1759f1a8c0c042e.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7630"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 16:18:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "knc7nr4hqg"}, {"Name": "label", "Value": "uploads/documents/8df256e6ebd646bea1759f1a8c0c042e.txt"}, {"Name": "integrity", "Value": "sha256-MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY="}]}, {"Route": "uploads/documents/8df256e6ebd646bea1759f1a8c0c042e.knc7nr4hqg.txt.gz", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\v25brddiup-knc7nr4hqg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1927"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4=\""}, {"Name": "Last-Modified", "Value": "Sat, 05 Jul 2025 18:39:02 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "knc7nr4hqg"}, {"Name": "label", "Value": "uploads/documents/8df256e6ebd646bea1759f1a8c0c042e.txt.gz"}, {"Name": "integrity", "Value": "sha256-UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4="}]}, {"Route": "uploads/documents/8df256e6ebd646bea1759f1a8c0c042e.txt", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\v25brddiup-knc7nr4hqg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000518672199"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1927"}, {"Name": "ETag", "Value": "\"UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4=\""}, {"Name": "Last-Modified", "Value": "Sat, 05 Jul 2025 18:39:02 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY="}]}, {"Route": "uploads/documents/8df256e6ebd646bea1759f1a8c0c042e.txt", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\8df256e6ebd646bea1759f1a8c0c042e.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7630"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 16:18:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY="}]}, {"Route": "uploads/documents/8df256e6ebd646bea1759f1a8c0c042e.txt.gz", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\v25brddiup-knc7nr4hqg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1927"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4=\""}, {"Name": "Last-Modified", "Value": "Sat, 05 Jul 2025 18:39:02 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4="}]}, {"Route": "uploads/documents/b90557820bb340bd96d5fc46c4f97c02.pbhq5urrew.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\b90557820bb340bd96d5fc46c4f97c02.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1607"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:48:23 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pbhq5urrew"}, {"Name": "label", "Value": "uploads/documents/b90557820bb340bd96d5fc46c4f97c02.pdf"}, {"Name": "integrity", "Value": "sha256-Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4="}]}, {"Route": "uploads/documents/b90557820bb340bd96d5fc46c4f97c02.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\b90557820bb340bd96d5fc46c4f97c02.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1607"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:48:23 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4="}]}, {"Route": "uploads/documents/bff0abcb17524585a06eee3255b1f49a.docx", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\bff0abcb17524585a06eee3255b1f49a.docx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2149873"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"}, {"Name": "ETag", "Value": "\"7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 00:48:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8="}]}, {"Route": "uploads/documents/bff0abcb17524585a06eee3255b1f49a.v8f3tesalf.docx", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\bff0abcb17524585a06eee3255b1f49a.docx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2149873"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"}, {"Name": "ETag", "Value": "\"7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 00:48:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v8f3tesalf"}, {"Name": "label", "Value": "uploads/documents/bff0abcb17524585a06eee3255b1f49a.docx"}, {"Name": "integrity", "Value": "sha256-7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8="}]}, {"Route": "uploads/documents/d6b0e189847a4d1293e9fbfe52e086d1.o5mrvqq7ms.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\d6b0e189847a4d1293e9fbfe52e086d1.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "147799"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"7C+EtpvaB+JITP/bPh6Hrmekms4V02jhufpz+3kKFkc=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 00:59:59 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o5mrvqq7ms"}, {"Name": "label", "Value": "uploads/documents/d6b0e189847a4d1293e9fbfe52e086d1.pdf"}, {"Name": "integrity", "Value": "sha256-7C+EtpvaB+JITP/bPh6Hrmekms4V02jhufpz+3kKFkc="}]}, {"Route": "uploads/documents/d6b0e189847a4d1293e9fbfe52e086d1.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\d6b0e189847a4d1293e9fbfe52e086d1.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "147799"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"7C+EtpvaB+JITP/bPh6Hrmekms4V02jhufpz+3kKFkc=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 00:59:59 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7C+EtpvaB+JITP/bPh6Hrmekms4V02jhufpz+3kKFkc="}]}, {"Route": "uploads/documents/e1fa76fc737043cd963851ab3d4f50db.6mabyag1xn.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\e1fa76fc737043cd963851ab3d4f50db.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "48194"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"gutJxmAixzCz0cRFOg/Rh+oSJmGOnIzJSsRszMHIgzQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 18:32:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6mabyag1xn"}, {"Name": "label", "Value": "uploads/documents/e1fa76fc737043cd963851ab3d4f50db.pdf"}, {"Name": "integrity", "Value": "sha256-gutJxmAixzCz0cRFOg/Rh+oSJmGOnIzJSsRszMHIgzQ="}]}, {"Route": "uploads/documents/e1fa76fc737043cd963851ab3d4f50db.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\e1fa76fc737043cd963851ab3d4f50db.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "48194"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"gutJxmAixzCz0cRFOg/Rh+oSJmGOnIzJSsRszMHIgzQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 18:32:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gutJxmAixzCz0cRFOg/Rh+oSJmGOnIzJSsRszMHIgzQ="}]}, {"Route": "uploads/documents/fa8afc1d2d664eb2a6c2057eb6a30012.jilall62b3.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\fa8afc1d2d664eb2a6c2057eb6a30012.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "63019"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"axSfQ4GEfDpg+365QvDt55vNqdPCE6fwB6NRIU/0hOM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:04:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jilall62b3"}, {"Name": "label", "Value": "uploads/documents/fa8afc1d2d664eb2a6c2057eb6a30012.pdf"}, {"Name": "integrity", "Value": "sha256-axSfQ4GEfDpg+365QvDt55vNqdPCE6fwB6NRIU/0hOM="}]}, {"Route": "uploads/documents/fa8afc1d2d664eb2a6c2057eb6a30012.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\fa8afc1d2d664eb2a6c2057eb6a30012.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "63019"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"axSfQ4GEfDpg+365QvDt55vNqdPCE6fwB6NRIU/0hOM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:04:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-axSfQ4GEfDpg+365QvDt55vNqdPCE6fwB6NRIU/0hOM="}]}, {"Route": "uploads/images/1870288851874090b203bf27c9321fed.ia1swkvltk.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\1870288851874090b203bf27c9321fed.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "885730"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 13:55:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ia1swkvltk"}, {"Name": "label", "Value": "uploads/images/1870288851874090b203bf27c9321fed.png"}, {"Name": "integrity", "Value": "sha256-duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8="}]}, {"Route": "uploads/images/1870288851874090b203bf27c9321fed.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\1870288851874090b203bf27c9321fed.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "885730"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 13:55:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8="}]}, {"Route": "uploads/images/312205ccd18f4650bef24653fbf9f64a.eb9ax9ytkh.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\312205ccd18f4650bef24653fbf9f64a.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "726022"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:45:00 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eb9ax9ytkh"}, {"Name": "label", "Value": "uploads/images/312205ccd18f4650bef24653fbf9f64a.png"}, {"Name": "integrity", "Value": "sha256-Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4="}]}, {"Route": "uploads/images/312205ccd18f4650bef24653fbf9f64a.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\312205ccd18f4650bef24653fbf9f64a.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "726022"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:45:00 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4="}]}, {"Route": "uploads/images/3a0ace1c93e946709f42cbfb2279e65f.jj02sd2q5h.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\3a0ace1c93e946709f42cbfb2279e65f.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1280957"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"wQB9mCk9PpicYOKXGpuIQC0mLLuKirO2SQ+vB/qwMbU=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 17:18:40 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jj02sd2q5h"}, {"Name": "label", "Value": "uploads/images/3a0ace1c93e946709f42cbfb2279e65f.png"}, {"Name": "integrity", "Value": "sha256-wQB9mCk9PpicYOKXGpuIQC0mLLuKirO2SQ+vB/qwMbU="}]}, {"Route": "uploads/images/3a0ace1c93e946709f42cbfb2279e65f.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\3a0ace1c93e946709f42cbfb2279e65f.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1280957"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"wQB9mCk9PpicYOKXGpuIQC0mLLuKirO2SQ+vB/qwMbU=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 17:18:40 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wQB9mCk9PpicYOKXGpuIQC0mLLuKirO2SQ+vB/qwMbU="}]}, {"Route": "uploads/images/4886e04ad10545cfa694a3d3f06d85ec.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\4886e04ad10545cfa694a3d3f06d85ec.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14596"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"/pffW2qzqA9CBtmme640Xn8tkV+gYDbxpODuhXHgE+s=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:16:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/pffW2qzqA9CBtmme640Xn8tkV+gYDbxpODuhXHgE+s="}]}, {"Route": "uploads/images/4886e04ad10545cfa694a3d3f06d85ec.qt5iiuu92c.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\4886e04ad10545cfa694a3d3f06d85ec.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14596"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"/pffW2qzqA9CBtmme640Xn8tkV+gYDbxpODuhXHgE+s=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:16:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qt5iiuu92c"}, {"Name": "label", "Value": "uploads/images/4886e04ad10545cfa694a3d3f06d85ec.png"}, {"Name": "integrity", "Value": "sha256-/pffW2qzqA9CBtmme640Xn8tkV+gYDbxpODuhXHgE+s="}]}, {"Route": "uploads/images/4c2fcb8d51f0455fad91d5629bc65ef4.160dqoac5z.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\4c2fcb8d51f0455fad91d5629bc65ef4.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "853270"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"52Oa6P2DToTBO8LaH35EDZ/Ez/Hb2ZEjhbM/+dpRNYY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 17:13:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "160dqoac5z"}, {"Name": "label", "Value": "uploads/images/4c2fcb8d51f0455fad91d5629bc65ef4.png"}, {"Name": "integrity", "Value": "sha256-52Oa6P2DToTBO8LaH35EDZ/Ez/Hb2ZEjhbM/+dpRNYY="}]}, {"Route": "uploads/images/4c2fcb8d51f0455fad91d5629bc65ef4.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\4c2fcb8d51f0455fad91d5629bc65ef4.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "853270"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"52Oa6P2DToTBO8LaH35EDZ/Ez/Hb2ZEjhbM/+dpRNYY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 17:13:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-52Oa6P2DToTBO8LaH35EDZ/Ez/Hb2ZEjhbM/+dpRNYY="}]}, {"Route": "uploads/images/4ca45322624947dfa4c15343a522edaa.eb9ax9ytkh.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\4ca45322624947dfa4c15343a522edaa.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "726022"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 17:05:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eb9ax9ytkh"}, {"Name": "label", "Value": "uploads/images/4ca45322624947dfa4c15343a522edaa.png"}, {"Name": "integrity", "Value": "sha256-Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4="}]}, {"Route": "uploads/images/4ca45322624947dfa4c15343a522edaa.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\4ca45322624947dfa4c15343a522edaa.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "726022"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 17:05:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4="}]}, {"Route": "uploads/images/53e82cd4346e4263bc415d38312d7b49.csvf1bo9y0.jpg", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\53e82cd4346e4263bc415d38312d7b49.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "158471"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"VEQKFnXTwXCWL0eSgISkz4zFS8E4pb7n1re/Jm6cR10=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:43:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "csvf1bo9y0"}, {"Name": "label", "Value": "uploads/images/53e82cd4346e4263bc415d38312d7b49.jpg"}, {"Name": "integrity", "Value": "sha256-VEQKFnXTwXCWL0eSgISkz4zFS8E4pb7n1re/Jm6cR10="}]}, {"Route": "uploads/images/53e82cd4346e4263bc415d38312d7b49.jpg", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\53e82cd4346e4263bc415d38312d7b49.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "158471"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"VEQKFnXTwXCWL0eSgISkz4zFS8E4pb7n1re/Jm6cR10=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:43:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VEQKFnXTwXCWL0eSgISkz4zFS8E4pb7n1re/Jm6cR10="}]}, {"Route": "uploads/images/610efb0bb9a745baa5a60fbe8b50789a.5za9gpxx61.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\610efb0bb9a745baa5a60fbe8b50789a.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 01:16:18 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5za9gpxx61"}, {"Name": "label", "Value": "uploads/images/610efb0bb9a745baa5a60fbe8b50789a.webp"}, {"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/610efb0bb9a745baa5a60fbe8b50789a.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\610efb0bb9a745baa5a60fbe8b50789a.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 01:16:18 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/631a190e6e0e4858a805e2bf75d6d33a.ia1swkvltk.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\631a190e6e0e4858a805e2bf75d6d33a.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "885730"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:23:15 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ia1swkvltk"}, {"Name": "label", "Value": "uploads/images/631a190e6e0e4858a805e2bf75d6d33a.png"}, {"Name": "integrity", "Value": "sha256-duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8="}]}, {"Route": "uploads/images/631a190e6e0e4858a805e2bf75d6d33a.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\631a190e6e0e4858a805e2bf75d6d33a.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "885730"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:23:15 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8="}]}, {"Route": "uploads/images/6d796a2e432c48e3b57b61a7cc64e85c.c69f109cb6.PNG", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\6d796a2e432c48e3b57b61a7cc64e85c.PNG", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1011297"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 14:50:15 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c69f109cb6"}, {"Name": "label", "Value": "uploads/images/6d796a2e432c48e3b57b61a7cc64e85c.PNG"}, {"Name": "integrity", "Value": "sha256-zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g="}]}, {"Route": "uploads/images/6d796a2e432c48e3b57b61a7cc64e85c.PNG", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\6d796a2e432c48e3b57b61a7cc64e85c.PNG", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1011297"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 14:50:15 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g="}]}, {"Route": "uploads/images/7054f84a3f8f4a409e0e9290c878fbcc.1nfn82ehfv.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\7054f84a3f8f4a409e0e9290c878fbcc.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1345158"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"E+GMZQsbl/qQn7wQJyD/nl/muj/Q8tsck4DYoiixGnM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:30:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1nfn82ehfv"}, {"Name": "label", "Value": "uploads/images/7054f84a3f8f4a409e0e9290c878fbcc.png"}, {"Name": "integrity", "Value": "sha256-E+GMZQsbl/qQn7wQJyD/nl/muj/Q8tsck4DYoiixGnM="}]}, {"Route": "uploads/images/7054f84a3f8f4a409e0e9290c878fbcc.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\7054f84a3f8f4a409e0e9290c878fbcc.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1345158"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"E+GMZQsbl/qQn7wQJyD/nl/muj/Q8tsck4DYoiixGnM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:30:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-E+GMZQsbl/qQn7wQJyD/nl/muj/Q8tsck4DYoiixGnM="}]}, {"Route": "uploads/images/713c55958e434619b8dbd17a5ef62b7e.ia1swkvltk.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\713c55958e434619b8dbd17a5ef62b7e.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "885730"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 16:21:07 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ia1swkvltk"}, {"Name": "label", "Value": "uploads/images/713c55958e434619b8dbd17a5ef62b7e.png"}, {"Name": "integrity", "Value": "sha256-duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8="}]}, {"Route": "uploads/images/713c55958e434619b8dbd17a5ef62b7e.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\713c55958e434619b8dbd17a5ef62b7e.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "885730"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 16:21:07 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8="}]}, {"Route": "uploads/images/8422115944564f0d9b5336c152d37758.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\8422115944564f0d9b5336c152d37758.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "196383"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:04:29 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY="}]}, {"Route": "uploads/images/8422115944564f0d9b5336c152d37758.pnn48fdjlm.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\8422115944564f0d9b5336c152d37758.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "196383"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:04:29 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pnn48fdjlm"}, {"Name": "label", "Value": "uploads/images/8422115944564f0d9b5336c152d37758.png"}, {"Name": "integrity", "Value": "sha256-xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY="}]}, {"Route": "uploads/images/86f9084a89de49dcb333d63c0bf993b9.3oz33zfofh.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\86f9084a89de49dcb333d63c0bf993b9.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "87614"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"UxYff5Z/UZ8/0B2kpJToiiF7lnlcm5vqlH7G0bOO6Bw=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 16:15:05 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3oz33zfofh"}, {"Name": "label", "Value": "uploads/images/86f9084a89de49dcb333d63c0bf993b9.webp"}, {"Name": "integrity", "Value": "sha256-UxYff5Z/UZ8/0B2kpJToiiF7lnlcm5vqlH7G0bOO6Bw="}]}, {"Route": "uploads/images/86f9084a89de49dcb333d63c0bf993b9.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\86f9084a89de49dcb333d63c0bf993b9.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "87614"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"UxYff5Z/UZ8/0B2kpJToiiF7lnlcm5vqlH7G0bOO6Bw=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 16:15:05 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UxYff5Z/UZ8/0B2kpJToiiF7lnlcm5vqlH7G0bOO6Bw="}]}, {"Route": "uploads/images/87959591ff1d4bb9b6d3cf9617456555.45mp2a8mvz.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\87959591ff1d4bb9b6d3cf9617456555.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "659876"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"qBmDm8T4QaftiVzlhIA0uT+wdWrw6rn1MEAx8WEsFfg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:16:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "45mp2a8mvz"}, {"Name": "label", "Value": "uploads/images/87959591ff1d4bb9b6d3cf9617456555.png"}, {"Name": "integrity", "Value": "sha256-qBmDm8T4QaftiVzlhIA0uT+wdWrw6rn1MEAx8WEsFfg="}]}, {"Route": "uploads/images/87959591ff1d4bb9b6d3cf9617456555.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\87959591ff1d4bb9b6d3cf9617456555.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "659876"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"qBmDm8T4QaftiVzlhIA0uT+wdWrw6rn1MEAx8WEsFfg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:16:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qBmDm8T4QaftiVzlhIA0uT+wdWrw6rn1MEAx8WEsFfg="}]}, {"Route": "uploads/images/8e4b91c5d9284cea9b84c8b70c264c79.eb9ax9ytkh.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\8e4b91c5d9284cea9b84c8b70c264c79.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "726022"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:36:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eb9ax9ytkh"}, {"Name": "label", "Value": "uploads/images/8e4b91c5d9284cea9b84c8b70c264c79.png"}, {"Name": "integrity", "Value": "sha256-Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4="}]}, {"Route": "uploads/images/8e4b91c5d9284cea9b84c8b70c264c79.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\8e4b91c5d9284cea9b84c8b70c264c79.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "726022"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:36:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4="}]}, {"Route": "uploads/images/97b49fcfbb844432a4b311cb199e86f9.evylwqip3e.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\97b49fcfbb844432a4b311cb199e86f9.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86139"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Jul 2025 01:54:38 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "evylwqip3e"}, {"Name": "label", "Value": "uploads/images/97b49fcfbb844432a4b311cb199e86f9.png"}, {"Name": "integrity", "Value": "sha256-NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0="}]}, {"Route": "uploads/images/97b49fcfbb844432a4b311cb199e86f9.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\97b49fcfbb844432a4b311cb199e86f9.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86139"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Jul 2025 01:54:38 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0="}]}, {"Route": "uploads/images/9854cf13659b4bb6a2141d0824af4666.evylwqip3e.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9854cf13659b4bb6a2141d0824af4666.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86139"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:07:12 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "evylwqip3e"}, {"Name": "label", "Value": "uploads/images/9854cf13659b4bb6a2141d0824af4666.png"}, {"Name": "integrity", "Value": "sha256-NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0="}]}, {"Route": "uploads/images/9854cf13659b4bb6a2141d0824af4666.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9854cf13659b4bb6a2141d0824af4666.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86139"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:07:12 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0="}]}, {"Route": "uploads/images/9a342b7e0e404095a917be1fc40364ae.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9a342b7e0e404095a917be1fc40364ae.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 02:50:41 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/9a342b7e0e404095a917be1fc40364ae.vfb68j65u8.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9a342b7e0e404095a917be1fc40364ae.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 02:50:41 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vfb68j65u8"}, {"Name": "label", "Value": "uploads/images/9a342b7e0e404095a917be1fc40364ae.png"}, {"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/9df790c0db6d4d7e9f302edc88b5804c.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9df790c0db6d4d7e9f302edc88b5804c.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "155911"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"uX8tYvCPYY6gXw26SuClSH9rciWhnx9t0oz+T27ZDSI=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 16:15:51 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uX8tYvCPYY6gXw26SuClSH9rciWhnx9t0oz+T27ZDSI="}]}, {"Route": "uploads/images/9df790c0db6d4d7e9f302edc88b5804c.r7rmd0638a.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9df790c0db6d4d7e9f302edc88b5804c.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "155911"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"uX8tYvCPYY6gXw26SuClSH9rciWhnx9t0oz+T27ZDSI=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 16:15:51 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r7rmd0638a"}, {"Name": "label", "Value": "uploads/images/9df790c0db6d4d7e9f302edc88b5804c.png"}, {"Name": "integrity", "Value": "sha256-uX8tYvCPYY6gXw26SuClSH9rciWhnx9t0oz+T27ZDSI="}]}, {"Route": "uploads/images/a64a54a1298e4b8c81bf46d705996980.160dqoac5z.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\a64a54a1298e4b8c81bf46d705996980.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "853270"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"52Oa6P2DToTBO8LaH35EDZ/Ez/Hb2ZEjhbM/+dpRNYY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 13:31:15 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "160dqoac5z"}, {"Name": "label", "Value": "uploads/images/a64a54a1298e4b8c81bf46d705996980.png"}, {"Name": "integrity", "Value": "sha256-52Oa6P2DToTBO8LaH35EDZ/Ez/Hb2ZEjhbM/+dpRNYY="}]}, {"Route": "uploads/images/a64a54a1298e4b8c81bf46d705996980.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\a64a54a1298e4b8c81bf46d705996980.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "853270"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"52Oa6P2DToTBO8LaH35EDZ/Ez/Hb2ZEjhbM/+dpRNYY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 13:31:15 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-52Oa6P2DToTBO8LaH35EDZ/Ez/Hb2ZEjhbM/+dpRNYY="}]}, {"Route": "uploads/images/aa469b655153465e9cd1265d5a36a8dd.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\aa469b655153465e9cd1265d5a36a8dd.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 02:41:59 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/aa469b655153465e9cd1265d5a36a8dd.vfb68j65u8.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\aa469b655153465e9cd1265d5a36a8dd.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 02:41:59 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vfb68j65u8"}, {"Name": "label", "Value": "uploads/images/aa469b655153465e9cd1265d5a36a8dd.png"}, {"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/aa812b7960134dd380c2c5fcc1286289.eb9ax9ytkh.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\aa812b7960134dd380c2c5fcc1286289.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "726022"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 18:31:04 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eb9ax9ytkh"}, {"Name": "label", "Value": "uploads/images/aa812b7960134dd380c2c5fcc1286289.png"}, {"Name": "integrity", "Value": "sha256-Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4="}]}, {"Route": "uploads/images/aa812b7960134dd380c2c5fcc1286289.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\aa812b7960134dd380c2c5fcc1286289.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "726022"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 18:31:04 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4="}]}, {"Route": "uploads/images/aee04329d556487dbb70eccf248638db.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\aee04329d556487dbb70eccf248638db.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "196383"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Jul 2025 01:48:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY="}]}, {"Route": "uploads/images/aee04329d556487dbb70eccf248638db.pnn48fdjlm.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\aee04329d556487dbb70eccf248638db.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "196383"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Jul 2025 01:48:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pnn48fdjlm"}, {"Name": "label", "Value": "uploads/images/aee04329d556487dbb70eccf248638db.png"}, {"Name": "integrity", "Value": "sha256-xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY="}]}, {"Route": "uploads/images/bd28a12d15e24fb48511eb051459a6bc.ia1swkvltk.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\bd28a12d15e24fb48511eb051459a6bc.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "885730"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 16:07:29 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ia1swkvltk"}, {"Name": "label", "Value": "uploads/images/bd28a12d15e24fb48511eb051459a6bc.png"}, {"Name": "integrity", "Value": "sha256-duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8="}]}, {"Route": "uploads/images/bd28a12d15e24fb48511eb051459a6bc.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\bd28a12d15e24fb48511eb051459a6bc.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "885730"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 16:07:29 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8="}]}, {"Route": "uploads/images/c12085ba2bf34fb09f79c7850e7a6e87.lb3ihrmur6.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c12085ba2bf34fb09f79c7850e7a6e87.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "62981"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"r0HmmgXg80L5bGekrx0SHvXKeEOeDQvth5XrNkEGbDw=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:16:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lb3ihrmur6"}, {"Name": "label", "Value": "uploads/images/c12085ba2bf34fb09f79c7850e7a6e87.png"}, {"Name": "integrity", "Value": "sha256-r0HmmgXg80L5bGekrx0SHvXKeEOeDQvth5XrNkEGbDw="}]}, {"Route": "uploads/images/c12085ba2bf34fb09f79c7850e7a6e87.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c12085ba2bf34fb09f79c7850e7a6e87.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "62981"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"r0HmmgXg80L5bGekrx0SHvXKeEOeDQvth5XrNkEGbDw=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:16:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-r0HmmgXg80L5bGekrx0SHvXKeEOeDQvth5XrNkEGbDw="}]}, {"Route": "uploads/images/c3cf257303cd47dfa6018f1b87bdae6c.ljkl4o8aee.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c3cf257303cd47dfa6018f1b87bdae6c.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3582183"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"v8CNdUHekPmKC+kXCvGU9mz/SMbNlbVn/2Cx0uizk9U=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 18:33:51 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ljkl4o8aee"}, {"Name": "label", "Value": "uploads/images/c3cf257303cd47dfa6018f1b87bdae6c.png"}, {"Name": "integrity", "Value": "sha256-v8CNdUHekPmKC+kXCvGU9mz/SMbNlbVn/2Cx0uizk9U="}]}, {"Route": "uploads/images/c3cf257303cd47dfa6018f1b87bdae6c.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c3cf257303cd47dfa6018f1b87bdae6c.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3582183"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"v8CNdUHekPmKC+kXCvGU9mz/SMbNlbVn/2Cx0uizk9U=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 18:33:51 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-v8CNdUHekPmKC+kXCvGU9mz/SMbNlbVn/2Cx0uizk9U="}]}, {"Route": "uploads/images/c4be6ded3cb74eeeb91a53dd8f79d074.5za9gpxx61.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c4be6ded3cb74eeeb91a53dd8f79d074.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 02:38:51 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5za9gpxx61"}, {"Name": "label", "Value": "uploads/images/c4be6ded3cb74eeeb91a53dd8f79d074.webp"}, {"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/c4be6ded3cb74eeeb91a53dd8f79d074.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c4be6ded3cb74eeeb91a53dd8f79d074.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 02:38:51 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/ca0161389fa346e6be985efb101cf3f7.45mp2a8mvz.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\ca0161389fa346e6be985efb101cf3f7.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "659876"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"qBmDm8T4QaftiVzlhIA0uT+wdWrw6rn1MEAx8WEsFfg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:28:24 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "45mp2a8mvz"}, {"Name": "label", "Value": "uploads/images/ca0161389fa346e6be985efb101cf3f7.png"}, {"Name": "integrity", "Value": "sha256-qBmDm8T4QaftiVzlhIA0uT+wdWrw6rn1MEAx8WEsFfg="}]}, {"Route": "uploads/images/ca0161389fa346e6be985efb101cf3f7.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\ca0161389fa346e6be985efb101cf3f7.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "659876"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"qBmDm8T4QaftiVzlhIA0uT+wdWrw6rn1MEAx8WEsFfg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:28:24 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qBmDm8T4QaftiVzlhIA0uT+wdWrw6rn1MEAx8WEsFfg="}]}, {"Route": "uploads/images/db5e4060478d44bdbd0eda3592b741c4.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\db5e4060478d44bdbd0eda3592b741c4.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 01:16:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/db5e4060478d44bdbd0eda3592b741c4.vfb68j65u8.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\db5e4060478d44bdbd0eda3592b741c4.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 01:16:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vfb68j65u8"}, {"Name": "label", "Value": "uploads/images/db5e4060478d44bdbd0eda3592b741c4.png"}, {"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/e97afe0fea7d46fa90ae97941b717777.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\e97afe0fea7d46fa90ae97941b717777.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 14:51:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/e97afe0fea7d46fa90ae97941b717777.vfb68j65u8.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\e97afe0fea7d46fa90ae97941b717777.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 14:51:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vfb68j65u8"}, {"Name": "label", "Value": "uploads/images/e97afe0fea7d46fa90ae97941b717777.png"}, {"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/eb79cd51dc3d43dbbda06c629027ec98.bm200vq19x.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\eb79cd51dc3d43dbbda06c629027ec98.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51973"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Q8l8GUhamHBK0YbIi2QYsf3keVpACWQScC1PakHwt/o=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:25:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bm200vq19x"}, {"Name": "label", "Value": "uploads/images/eb79cd51dc3d43dbbda06c629027ec98.png"}, {"Name": "integrity", "Value": "sha256-Q8l8GUhamHBK0YbIi2QYsf3keVpACWQScC1PakHwt/o="}]}, {"Route": "uploads/images/eb79cd51dc3d43dbbda06c629027ec98.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\eb79cd51dc3d43dbbda06c629027ec98.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51973"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Q8l8GUhamHBK0YbIi2QYsf3keVpACWQScC1PakHwt/o=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:25:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Q8l8GUhamHBK0YbIi2QYsf3keVpACWQScC1PakHwt/o="}]}, {"Route": "uploads/images/eec2598549b14c3cb6efa0a6ca5bfe8d.jj02sd2q5h.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\eec2598549b14c3cb6efa0a6ca5bfe8d.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1280957"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"wQB9mCk9PpicYOKXGpuIQC0mLLuKirO2SQ+vB/qwMbU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:09:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jj02sd2q5h"}, {"Name": "label", "Value": "uploads/images/eec2598549b14c3cb6efa0a6ca5bfe8d.png"}, {"Name": "integrity", "Value": "sha256-wQB9mCk9PpicYOKXGpuIQC0mLLuKirO2SQ+vB/qwMbU="}]}, {"Route": "uploads/images/eec2598549b14c3cb6efa0a6ca5bfe8d.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\eec2598549b14c3cb6efa0a6ca5bfe8d.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1280957"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"wQB9mCk9PpicYOKXGpuIQC0mLLuKirO2SQ+vB/qwMbU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:09:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wQB9mCk9PpicYOKXGpuIQC0mLLuKirO2SQ+vB/qwMbU="}]}, {"Route": "uploads/images/f706c5792702492a90e00628f2eb9ba6.5za9gpxx61.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\f706c5792702492a90e00628f2eb9ba6.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 02:38:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5za9gpxx61"}, {"Name": "label", "Value": "uploads/images/f706c5792702492a90e00628f2eb9ba6.webp"}, {"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/f706c5792702492a90e00628f2eb9ba6.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\f706c5792702492a90e00628f2eb9ba6.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 02:38:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/f97829c0fe164622a2e1eae38e40117a.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\f97829c0fe164622a2e1eae38e40117a.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "216207"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"GcH838F74sMYRZGDHUKT/emduBGDC+GW1yLAVMoCHIc=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:48:04 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GcH838F74sMYRZGDHUKT/emduBGDC+GW1yLAVMoCHIc="}]}, {"Route": "uploads/images/f97829c0fe164622a2e1eae38e40117a.t3dbuv7mfy.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\f97829c0fe164622a2e1eae38e40117a.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "216207"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"GcH838F74sMYRZGDHUKT/emduBGDC+GW1yLAVMoCHIc=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:48:04 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t3dbuv7mfy"}, {"Name": "label", "Value": "uploads/images/f97829c0fe164622a2e1eae38e40117a.png"}, {"Name": "integrity", "Value": "sha256-GcH838F74sMYRZGDHUKT/emduBGDC+GW1yLAVMoCHIc="}]}, {"Route": "uploads/images/fb22d922074c4743951c4e34232de868.eb9ax9ytkh.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\fb22d922074c4743951c4e34232de868.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "726022"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 17:58:32 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eb9ax9ytkh"}, {"Name": "label", "Value": "uploads/images/fb22d922074c4743951c4e34232de868.png"}, {"Name": "integrity", "Value": "sha256-Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4="}]}, {"Route": "uploads/images/fb22d922074c4743951c4e34232de868.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\fb22d922074c4743951c4e34232de868.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "726022"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 17:58:32 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4="}]}, {"Route": "uploads/images/fcbd8c0981174ebb8f826b95d2ac61c7.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\fcbd8c0981174ebb8f826b95d2ac61c7.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "196383"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:50:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY="}]}, {"Route": "uploads/images/fcbd8c0981174ebb8f826b95d2ac61c7.pnn48fdjlm.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\fcbd8c0981174ebb8f826b95d2ac61c7.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "196383"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:50:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pnn48fdjlm"}, {"Name": "label", "Value": "uploads/images/fcbd8c0981174ebb8f826b95d2ac61c7.png"}, {"Name": "integrity", "Value": "sha256-xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY="}]}]}