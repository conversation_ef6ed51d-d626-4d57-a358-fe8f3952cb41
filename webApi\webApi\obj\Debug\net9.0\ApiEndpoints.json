[{"ContainingType": "webApi.Controllers.ActionEntityController", "Method": "GetActions", "RelativePath": "api/ActionEntity", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ActionEntity, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ActionEntityController", "Method": "PostAction", "RelativePath": "api/ActionEntity", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "action", "Type": "webApi.Models.ActionEntity", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.ActionEntity", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ActionEntityController", "Method": "GetAction", "RelativePath": "api/ActionEntity/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.ActionEntity", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ActionEntityController", "Method": "PutAction", "RelativePath": "api/ActionEntity/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "action", "Type": "webApi.Models.ActionEntity", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.ActionEntityController", "Method": "DeleteAction", "RelativePath": "api/ActionEntity/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.ActivityLogsController", "Method": "GetActivityLogs", "RelativePath": "api/ActivityLogs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ActivityLog, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ActivityLogsController", "Method": "PostActivityLog", "RelativePath": "api/ActivityLogs", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "activityLog", "Type": "webApi.Models.ActivityLog", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.ActivityLog", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.ActivityLogsController", "Method": "GetActivityLog", "RelativePath": "api/ActivityLogs/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.ActivityLog", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.ActivityLogsController", "Method": "GetActivityLogsByAction", "RelativePath": "api/ActivityLogs/action/{action}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "action", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ActivityLog, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ActivityLogsController", "Method": "GetActions", "RelativePath": "api/ActivityLogs/actions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ActivityLogsController", "Method": "GetActivityLogsByChangeType", "RelativePath": "api/ActivityLogs/change-type/{changeType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "changeType", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ActivityLog, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ActivityLogsController", "Method": "GetChangeTypes", "RelativePath": "api/ActivityLogs/change-types", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ActivityLogsController", "Method": "GetActivityLogsByDateRange", "RelativePath": "api/ActivityLogs/date-range", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.Int64", "IsRequired": false}, {"Name": "endDate", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ActivityLog, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ActivityLogsController", "Method": "LogActivity", "RelativePath": "api/ActivityLogs/log", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "action", "Type": "System.String", "IsRequired": false}, {"Name": "entityType", "Type": "System.String", "IsRequired": false}, {"Name": "entityId", "Type": "System.Int32", "IsRequired": false}, {"Name": "description", "Type": "System.String", "IsRequired": false}, {"Name": "userId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "webApi.Models.ActivityLog", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.ActivityLogsController", "Method": "GetTaskActivityLogs", "RelativePath": "api/ActivityLogs/task/{taskId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ActivityLog, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ActivityLogsController", "Method": "GetUserActivityLogs", "RelativePath": "api/ActivityLogs/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ActivityLog, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.AdminController", "Method": "GetAllDepartments", "RelativePath": "api/Admin/departments", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "includeInactive", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.AdminController", "Method": "GetSystemHealth", "RelativePath": "api/Admin/health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.AdminController", "Method": "GetAllPermissions", "RelativePath": "api/Admin/permissions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.AdminController", "Method": "RefreshSystemData", "RelativePath": "api/Admin/refresh", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.AdminController", "Method": "GetRoleDistributionReport", "RelativePath": "api/Admin/reports/role-distribution", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.AdminController", "Method": "GetUserActivityReport", "RelativePath": "api/Admin/reports/user-activity", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "days", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.AdminController", "Method": "GetAllRoles", "RelativePath": "api/Admin/roles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "includeInactive", "Type": "System.Boolean", "IsRequired": false}, {"Name": "includeDetails", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.AdminController", "Method": "GetSystemStatistics", "RelativePath": "api/Admin/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.AdminController", "Method": "GetAllUsers", "RelativePath": "api/Admin/users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "includeInactive", "Type": "System.Boolean", "IsRequired": false}, {"Name": "departmentId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "roleId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.AdminController", "Method": "UpdateUserStatus", "RelativePath": "api/Admin/users/{userId}/status", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}, {"Name": "isActive", "Type": "System.Boolean", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.AdminController", "Method": "BulkUpdateUserStatus", "RelativePath": "api/Admin/users/bulk-status", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "webApi.Controllers.BulkUpdateRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.ArchiveCategoriesController", "Method": "GetArchiveCategories", "RelativePath": "api/ArchiveCategories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ArchiveCategory, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ArchiveCategoriesController", "Method": "PostArchiveCategory", "RelativePath": "api/ArchiveCategories", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "archiveCategory", "Type": "webApi.Models.ArchiveCategory", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.ArchiveCategory", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.ArchiveCategoriesController", "Method": "GetArchiveCategory", "RelativePath": "api/ArchiveCategories/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.ArchiveCategory", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.ArchiveCategoriesController", "Method": "PutArchiveCategory", "RelativePath": "api/ArchiveCategories/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "archiveCategory", "Type": "webApi.Models.ArchiveCategory", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.ArchiveCategoriesController", "Method": "DeleteArchiveCategory", "RelativePath": "api/ArchiveCategories/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.ArchiveCategoriesController", "Method": "GetDocumentsCount", "RelativePath": "api/ArchiveCategories/{id}/documents/count", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ArchiveCategoriesController", "Method": "MoveCategory", "RelativePath": "api/ArchiveCategories/{id}/move", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "newParentId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.ArchiveCategoriesController", "Method": "GetCategoryPath", "RelativePath": "api/ArchiveCategories/{id}/path", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.ArchiveCategoriesController", "Method": "ToggleActive", "RelativePath": "api/ArchiveCategories/{id}/toggle-active", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "active", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.ArchiveCategoriesController", "Method": "GetChildCategories", "RelativePath": "api/ArchiveCategories/parent/{parentId}/children", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "parentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ArchiveCategory, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ArchiveCategoriesController", "Method": "GetRootCategories", "RelativePath": "api/ArchiveCategories/root", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ArchiveCategory, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ArchiveCategoriesController", "Method": "SearchCategories", "RelativePath": "api/ArchiveCategories/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "searchTerm", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ArchiveCategory, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ArchiveCategoriesController", "Method": "GetCategoriesTree", "RelativePath": "api/ArchiveCategories/tree", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ArchiveDocumentsController", "Method": "GetArchiveDocuments", "RelativePath": "api/ArchiveDocuments", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ArchiveDocument, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ArchiveDocumentsController", "Method": "PostArchiveDocument", "RelativePath": "api/ArchiveDocuments", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "archiveDocument", "Type": "webApi.Models.ArchiveDocument", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.ArchiveDocument", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.ArchiveDocumentsController", "Method": "GetArchiveDocument", "RelativePath": "api/ArchiveDocuments/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.ArchiveDocument", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.ArchiveDocumentsController", "Method": "PutArchiveDocument", "RelativePath": "api/ArchiveDocuments/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "archiveDocument", "Type": "webApi.Models.ArchiveDocument", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.ArchiveDocumentsController", "Method": "DeleteArchiveDocument", "RelativePath": "api/ArchiveDocuments/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.ArchiveDocumentsController", "Method": "GetDocumentTags", "RelativePath": "api/ArchiveDocuments/{id}/tags", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ArchiveTag, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.ArchiveDocumentsController", "Method": "GetDocumentsByCategory", "RelativePath": "api/ArchiveDocuments/category/{categoryId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "categoryId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ArchiveDocument, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ArchiveDocumentsController", "Method": "SearchDocuments", "RelativePath": "api/ArchiveDocuments/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "searchTerm", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ArchiveDocument, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ArchiveDocumentTagsController", "Method": "GetArchiveDocumentTags", "RelativePath": "api/ArchiveDocumentTags", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ArchiveDocumentTagsController", "Method": "GetDocumentTags", "RelativePath": "api/ArchiveDocumentTags/document/{documentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "documentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.ArchiveDocumentTagsController", "Method": "LinkDocumentTag", "RelativePath": "api/ArchiveDocumentTags/link", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "webApi.Controllers.LinkDocumentTagRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 409}]}, {"ContainingType": "webApi.Controllers.ArchiveDocumentTagsController", "Method": "LinkDocumentMultipleTags", "RelativePath": "api/ArchiveDocumentTags/link-multiple", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "webApi.Controllers.LinkDocumentMultipleTagsRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.ArchiveDocumentTagsController", "Method": "GetStatistics", "RelativePath": "api/ArchiveDocumentTags/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ArchiveDocumentTagsController", "Method": "GetTagDocuments", "RelativePath": "api/ArchiveDocumentTags/tag/{tagId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tagId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.ArchiveDocumentTagsController", "Method": "UnlinkDocumentTag", "RelativePath": "api/ArchiveDocumentTags/unlink", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "webApi.Controllers.UnlinkDocumentTagRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.ArchiveTagsController", "Method": "GetArchiveTags", "RelativePath": "api/ArchiveTags", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ArchiveTag, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ArchiveTagsController", "Method": "PostArchiveTag", "RelativePath": "api/ArchiveTags", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "archiveTag", "Type": "webApi.Models.ArchiveTag", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.ArchiveTag", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.ArchiveTagsController", "Method": "GetArchiveTag", "RelativePath": "api/ArchiveTags/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.ArchiveTag", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.ArchiveTagsController", "Method": "PutArchiveTag", "RelativePath": "api/ArchiveTags/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "archiveTag", "Type": "webApi.Models.ArchiveTag", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.ArchiveTagsController", "Method": "DeleteArchiveTag", "RelativePath": "api/ArchiveTags/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.ArchiveTagsController", "Method": "GetTagDocuments", "RelativePath": "api/ArchiveTags/{id}/documents", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ArchiveDocument, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.ArchiveTagsController", "Method": "ToggleActive", "RelativePath": "api/ArchiveTags/{id}/toggle-active", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "active", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.ArchiveTagsController", "Method": "GetActiveTags", "RelativePath": "api/ArchiveTags/active", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ArchiveTag, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ArchiveTagsController", "Method": "SearchTags", "RelativePath": "api/ArchiveTags/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "searchTerm", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ArchiveTag, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.AttachmentsController", "Method": "GetAttachments", "RelativePath": "api/Attachments", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Attachment, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.AttachmentsController", "Method": "PostAttachment", "RelativePath": "api/Attachments", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "attachment", "Type": "webApi.Models.Attachment", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Attachment", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.AttachmentsController", "Method": "GetAttachment", "RelativePath": "api/Attachments/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Attachment", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.AttachmentsController", "Method": "PutAttachment", "RelativePath": "api/Attachments/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "attachment", "Type": "webApi.Models.Attachment", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.AttachmentsController", "Method": "DeleteAttachment", "RelativePath": "api/Attachments/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "permanent", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.AttachmentsController", "Method": "DownloadAttachment", "RelativePath": "api/Attachments/{id}/download", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.AttachmentsController", "Method": "CleanupDeletedFiles", "RelativePath": "api/Attachments/cleanup", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "older<PERSON><PERSON><PERSON><PERSON>", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.AttachmentsController", "Method": "GetAttachmentsStatistics", "RelativePath": "api/Attachments/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.AttachmentsController", "Method": "GetTaskAttachments", "RelativePath": "api/Attachments/task/{taskId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Attachment, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.AttachmentsController", "Method": "GetTaskAttachmentsCount", "RelativePath": "api/Attachments/task/{taskId}/count", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.AttachmentsController", "Method": "UploadAttachment", "RelativePath": "api/Attachments/upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "taskId", "Type": "System.Int32", "IsRequired": false}, {"Name": "uploadedBy", "Type": "System.Int32", "IsRequired": false}, {"Name": "folder", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "webApi.Models.Attachment", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 413}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 415}]}, {"ContainingType": "webApi.Controllers.AttachmentsController", "Method": "GetUserAttachments", "RelativePath": "api/Attachments/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Attachment, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.AuthController", "Method": "ChangePassword", "RelativePath": "api/Auth/change-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "webApi.Models.Auth.ChangePasswordRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Auth.AuthResponse", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "webApi.Controllers.AuthController", "Method": "CheckSuperAdminExists", "RelativePath": "api/Auth/check-super-admin-exists", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.AuthController", "Method": "CreateDefaultSuperAdmin", "RelativePath": "api/Auth/create-default-super-admin", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "webApi.Models.Auth.RegisterRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Auth.AuthResponse", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 409}]}, {"ContainingType": "webApi.Controllers.AuthController", "Method": "ForgotPassword", "RelativePath": "api/Auth/forgot-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "webApi.Models.Auth.ForgotPasswordRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Auth.AuthResponse", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "webApi.Models.Auth.LoginRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Auth.AuthResponse", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "webApi.Controllers.AuthController", "Method": "Logout", "RelativePath": "api/Auth/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "webApi.Controllers.AuthController", "Method": "GetProfile", "RelativePath": "api/Auth/profile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "webApi.Models.Auth.UserInfo", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.AuthController", "Method": "RefreshToken", "RelativePath": "api/Auth/refresh-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "webApi.Models.Auth.RefreshTokenRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Auth.AuthResponse", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "webApi.Controllers.AuthController", "Method": "Register", "RelativePath": "api/Auth/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "webApi.Models.Auth.RegisterRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Auth.AuthResponse", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 409}]}, {"ContainingType": "webApi.Controllers.AuthController", "Method": "TestConnection", "RelativePath": "api/Auth/test", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.AuthController", "Method": "UpdateOnlineStatus", "RelativePath": "api/Auth/update-online-status", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "isOnline", "Type": "System.Boolean", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "webApi.Controllers.AuthController", "Method": "ValidateToken", "RelativePath": "api/Auth/validate-token", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "webApi.Models.Auth.UserInfo", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "webApi.Controllers.BackupsController", "Method": "GetBackups", "RelativePath": "api/Backups", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Backup, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.BackupsController", "Method": "PostBackup", "RelativePath": "api/Backups", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "backup", "Type": "webApi.Models.Backup", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Backup", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.BackupsController", "Method": "GetBackup", "RelativePath": "api/Backups/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Backup", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.BackupsController", "Method": "DeleteBackup", "RelativePath": "api/Backups/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.BackupsController", "Method": "DownloadBackup", "RelativePath": "api/Backups/{id}/download", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.BackupsController", "Method": "RestoreBackup", "RelativePath": "api/Backups/{id}/restore", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "restoredBy", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.BackupsController", "Method": "GetAutoBackups", "RelativePath": "api/Backups/auto", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Backup, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.BackupsController", "Method": "CleanupOldBackups", "RelativePath": "api/Backups/cleanup", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "daysOld", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.BackupsController", "Method": "CreateBackup", "RelativePath": "api/Backups/create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "fileName", "Type": "System.String", "IsRequired": false}, {"Name": "filePath", "Type": "System.String", "IsRequired": false}, {"Name": "fileSize", "Type": "System.Int64", "IsRequired": false}, {"Name": "description", "Type": "System.String", "IsRequired": false}, {"Name": "created<PERSON>y", "Type": "System.Int32", "IsRequired": false}, {"Name": "isAutoBackup", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "webApi.Models.Backup", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.BackupsController", "Method": "GetRestoredBackups", "RelativePath": "api/Backups/restored", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Backup, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.BackupsController", "Method": "GetBackupStatistics", "RelativePath": "api/Backups/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.CalendarEventsController", "Method": "GetCalendarEvents", "RelativePath": "api/CalendarEvents", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.CalendarEvent, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.CalendarEventsController", "Method": "PostCalendarEvent", "RelativePath": "api/CalendarEvents", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "calendarEvent", "Type": "webApi.Models.CalendarEvent", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.CalendarEvent", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.CalendarEventsController", "Method": "GetCalendarEvent", "RelativePath": "api/CalendarEvents/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.CalendarEvent", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.CalendarEventsController", "Method": "PutCalendarEvent", "RelativePath": "api/CalendarEvents/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "calendarEvent", "Type": "webApi.Models.CalendarEvent", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.CalendarEventsController", "Method": "DeleteCalendarEvent", "RelativePath": "api/CalendarEvents/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.CalendarEventsController", "Method": "GetCalendarEventsByDateRange", "RelativePath": "api/CalendarEvents/range", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.Int64", "IsRequired": false}, {"Name": "endDate", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.CalendarEvent, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.CalendarEventsController", "Method": "GetTaskCalendarEvents", "RelativePath": "api/CalendarEvents/task/{taskId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.CalendarEvent, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.CalendarEventsController", "Method": "GetUserCalendarEvents", "RelativePath": "api/CalendarEvents/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.CalendarEvent, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.CalendarEventsController", "Method": "GetUserCalendarEventsByDateRange", "RelativePath": "api/CalendarEvents/user/{userId}/range", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}, {"Name": "startDate", "Type": "System.Int64", "IsRequired": false}, {"Name": "endDate", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.CalendarEvent, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.CalendarEventsController", "Method": "GetTodayEvents", "RelativePath": "api/CalendarEvents/user/{userId}/today", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.CalendarEvent, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ChatGroupsController", "Method": "GetChatGroups", "RelativePath": "api/ChatGroups", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ChatGroup, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ChatGroupsController", "Method": "CreateChatGroup", "RelativePath": "api/ChatGroups", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "webApi.Models.DTOs.CreateChatGroupRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.ChatGroup", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ChatGroupsController", "Method": "JoinGroup", "RelativePath": "api/ChatGroups/{groupId}/join", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "groupId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "webApi.Controllers.JoinGroupRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.ChatGroupsController", "Method": "LeaveGroup", "RelativePath": "api/ChatGroups/{groupId}/leave/{userId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "groupId", "Type": "System.Int32", "IsRequired": true}, {"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.ChatGroupsController", "Method": "AddMemberToGroup", "RelativePath": "api/ChatGroups/{groupId}/members", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "groupId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "webApi.Controllers.AddMemberRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.ChatGroupsController", "Method": "GetGroupMembers", "RelativePath": "api/ChatGroups/{groupId}/members", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "groupId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ChatGroupsController", "Method": "RemoveMemberFromGroup", "RelativePath": "api/ChatGroups/{groupId}/members/{userId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "groupId", "Type": "System.Int32", "IsRequired": true}, {"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.ChatGroupsController", "Method": "UpdateMemberRole", "RelativePath": "api/ChatGroups/{groupId}/members/{userId}/role", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "groupId", "Type": "System.Int32", "IsRequired": true}, {"Name": "userId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "webApi.Controllers.UpdateRoleRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.ChatGroupsController", "Method": "GetChatGroup", "RelativePath": "api/ChatGroups/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.ChatGroup", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ChatGroupsController", "Method": "UpdateChatGroup", "RelativePath": "api/ChatGroups/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "chatGroup", "Type": "webApi.Models.ChatGroup", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.ChatGroupsController", "Method": "DeleteChatGroup", "RelativePath": "api/ChatGroups/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.ChatGroupsController", "Method": "ArchiveGroup", "RelativePath": "api/ChatGroups/{id}/archive", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.ChatGroupsController", "Method": "UnarchiveGroup", "RelativePath": "api/ChatGroups/{id}/unarchive", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.ChatGroupsController", "Method": "GetPublicGroups", "RelativePath": "api/ChatGroups/public", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ChatGroup, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ChatGroupsController", "Method": "SearchGroups", "RelativePath": "api/ChatGroups/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "searchTerm", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ChatGroup, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ChatGroupsController", "Method": "GetUserGroups", "RelativePath": "api/ChatGroups/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ChatGroup, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ChatGroupsController", "Method": "GetUserDirectMessages", "RelativePath": "api/ChatGroups/user/{userId}/direct-messages", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ChatGroup, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.CustomRolesController", "Method": "GetCustomRoles", "RelativePath": "api/CustomRoles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.CustomRole, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.CustomRolesController", "Method": "PostCustomRole", "RelativePath": "api/CustomRoles", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "role", "Type": "webApi.Models.CustomRole", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.CustomRole", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.CustomRolesController", "Method": "GetCustomRole", "RelativePath": "api/CustomRoles/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.CustomRole", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.CustomRolesController", "Method": "PutCustomRole", "RelativePath": "api/CustomRoles/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "role", "Type": "webApi.Models.CustomRole", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.CustomRolesController", "Method": "DeleteCustomRole", "RelativePath": "api/CustomRoles/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.CustomRolesController", "Method": "GetCustomRolePermissions", "RelativePath": "api/CustomRoles/{id}/permissions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Permission, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.CustomRolesController", "Method": "GetCustomRoleUsers", "RelativePath": "api/CustomRoles/{id}/users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.User, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.DashboardsController", "Method": "GetDashboards", "RelativePath": "api/Dashboards", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Dashboard, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.DashboardsController", "Method": "PostDashboard", "RelativePath": "api/Dashboards", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dashboard", "Type": "webApi.Models.Dashboard", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Dashboard", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.DashboardsController", "Method": "ShareDashboard", "RelativePath": "api/Dashboards/{dashboardId}/share/{userId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dashboardId", "Type": "System.Int32", "IsRequired": true}, {"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.DashboardsController", "Method": "UnshareDashboard", "RelativePath": "api/Dashboards/{dashboardId}/unshare/{userId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "dashboardId", "Type": "System.Int32", "IsRequired": true}, {"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.DashboardsController", "Method": "GetDashboard", "RelativePath": "api/Dashboards/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Dashboard", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.DashboardsController", "Method": "PutDashboard", "RelativePath": "api/Dashboards/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dashboard", "Type": "webApi.Models.Dashboard", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.DashboardsController", "Method": "DeleteDashboard", "RelativePath": "api/Dashboards/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.DashboardsController", "Method": "SetAsDefault", "RelativePath": "api/Dashboards/{id}/set-default", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.DashboardsController", "Method": "GetSharedDashboards", "RelativePath": "api/Dashboards/shared/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Dashboard, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.DashboardsController", "Method": "GetSystemStatistics", "RelativePath": "api/Dashboards/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.DashboardsController", "Method": "GetUserDashboards", "RelativePath": "api/Dashboards/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Dashboard, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.DashboardsController", "Method": "GetDefaultDashboard", "RelativePath": "api/Dashboards/user/{userId}/default", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Dashboard", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.DashboardWidgetsController", "Method": "GetDashboardWidgets", "RelativePath": "api/DashboardWidgets", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.DashboardWidget, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.DashboardWidgetsController", "Method": "PostDashboardWidget", "RelativePath": "api/DashboardWidgets", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dashboardWidget", "Type": "webApi.Models.DashboardWidget", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.DashboardWidget", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.DashboardWidgetsController", "Method": "GetDashboardWidget", "RelativePath": "api/DashboardWidgets/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.DashboardWidget", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.DashboardWidgetsController", "Method": "PutDashboardWidget", "RelativePath": "api/DashboardWidgets/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dashboardWidget", "Type": "webApi.Models.DashboardWidget", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.DashboardWidgetsController", "Method": "DeleteDashboardWidget", "RelativePath": "api/DashboardWidgets/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.DashboardWidgetsController", "Method": "UpdateWidgetPosition", "RelativePath": "api/DashboardWidgets/{id}/position", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "positionX", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "positionY", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "width", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "height", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.DashboardWidgetsController", "Method": "ToggleWidgetVisibility", "RelativePath": "api/DashboardWidgets/{id}/visibility", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "visible", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.DashboardWidgetsController", "Method": "GetDashboardWidgetsByDashboard", "RelativePath": "api/DashboardWidgets/dashboard/{dashboardId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "dashboardId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.DashboardWidget, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.DashboardWidgetsController", "Method": "ReorderWidgets", "RelativePath": "api/DashboardWidgets/dashboard/{dashboardId}/reorder", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "dashboardId", "Type": "System.Int32", "IsRequired": true}, {"Name": "widgetOrders", "Type": "System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.DashboardWidgetsController", "Method": "GetVisibleDashboardWidgets", "RelativePath": "api/DashboardWidgets/dashboard/{dashboardId}/visible", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "dashboardId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.DashboardWidget, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.DashboardWidgetsController", "Method": "GetDashboardWidgetsByType", "RelativePath": "api/DashboardWidgets/type/{widgetType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "widgetType", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.DashboardWidget, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.DashboardWidgetsController", "Method": "GetWidgetTypes", "RelativePath": "api/DashboardWidgets/widget-types", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.DatabaseController", "Method": "GetSampleData", "RelativePath": "api/Database/sample-data", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.DatabaseController", "Method": "SeedDatabase", "RelativePath": "api/Database/seed", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "webApi.Controllers.DatabaseController", "Method": "GetSystemStatus", "RelativePath": "api/Database/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "webApi.Controllers.DatabaseController", "Method": "GetTableNames", "RelativePath": "api/Database/tables", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "webApi.Controllers.DepartmentsController", "Method": "GetDepartments", "RelativePath": "api/Departments", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "search", "Type": "System.String", "IsRequired": false}, {"Name": "searchQuery", "Type": "System.String", "IsRequired": false}, {"Name": "orderBy", "Type": "System.String", "IsRequired": false}, {"Name": "sortBy", "Type": "System.String", "IsRequired": false}, {"Name": "orderDirection", "Type": "System.String", "IsRequired": false}, {"Name": "sortOrder", "Type": "System.String", "IsRequired": false}, {"Name": "isActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "managerId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "webApi.Controllers.DepartmentsController", "Method": "PostDepartment", "RelativePath": "api/Departments", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "department", "Type": "webApi.Models.Department", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Department", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.DepartmentsController", "Method": "GetDepartment", "RelativePath": "api/Departments/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Department", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "webApi.Controllers.DepartmentsController", "Method": "PutDepartment", "RelativePath": "api/Departments/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "department", "Type": "webApi.Models.Department", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.DepartmentsController", "Method": "DeleteDepartment", "RelativePath": "api/Departments/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.DepartmentsController", "Method": "ActivateDepartment", "RelativePath": "api/Departments/{id}/activate", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.DepartmentsController", "Method": "GetDepartmentUsers", "RelativePath": "api/Departments/{id}/assign-users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.User, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.DepartmentsController", "Method": "AssignUsersToDepartment", "RelativePath": "api/Departments/{id}/assign-users", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "webApi.Controllers.DepartmentsController+AssignUsersDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.DepartmentsController", "Method": "DeactivateDepartment", "RelativePath": "api/Departments/{id}/deactivate", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.DepartmentsController", "Method": "MoveDepartment", "RelativePath": "api/Departments/{id}/move", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "webApi.Controllers.DepartmentsController+MoveDepartmentDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.DepartmentsController", "Method": "GetDepartmentStatistics", "RelativePath": "api/Departments/{id}/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.DepartmentsController", "Method": "GetDepartmentTasks", "RelativePath": "api/Departments/{id}/tasks", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Task, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.DepartmentsController", "Method": "GetSubDepartments", "RelativePath": "api/Departments/{parentId}/children", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "parentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Department, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "webApi.Controllers.DepartmentsController", "Method": "ExportDepartments", "RelativePath": "api/Departments/export", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.DepartmentsController", "Method": "GetDepartmentHierarchy", "RelativePath": "api/Departments/hierarchy", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Department, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "webApi.Controllers.DepartmentsController", "Method": "ImportDepartments", "RelativePath": "api/Departments/import", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "departments", "Type": "System.Collections.Generic.List`1[[webApi.Models.Department, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.DepartmentsController", "Method": "MergeDepartments", "RelativePath": "api/Departments/merge", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "webApi.Controllers.DepartmentsController+MergeDepartmentsDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.DepartmentsController", "Method": "GetParentDepartments", "RelativePath": "api/Departments/parents", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.DepartmentsController", "Method": "ReorderDepartments", "RelativePath": "api/Departments/reorder", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "webApi.Controllers.DepartmentsController+ReorderDepartmentsDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.DepartmentsController", "Method": "GetDepartmentsStatistics", "RelativePath": "api/Departments/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.DepartmentsController", "Method": "TransferEmployee", "RelativePath": "api/Departments/transfer-employee", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "webApi.Controllers.DepartmentsController+TransferEmployeeDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.DepartmentsController", "Method": "TransferTask", "RelativePath": "api/Departments/transfer-task", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "webApi.Controllers.DepartmentsController+TransferTaskDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.DiagnosticsController", "Method": "GetDatabaseStatus", "RelativePath": "api/Diagnostics/database", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.DiagnosticsController", "Method": "FixDataIssues", "RelativePath": "api/Diagnostics/fix", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.DiagnosticsController", "Method": "GetHealth", "RelativePath": "api/Diagnostics/health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.DiagnosticsController", "Method": "GetTasksStatus", "RelativePath": "api/Diagnostics/tasks", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.DiagnosticsController", "Method": "TestTasksQuery", "RelativePath": "api/Diagnostics/tasks/test", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.GroupMembersController", "Method": "GetGroupMembers", "RelativePath": "api/GroupMembers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.GroupMembersController", "Method": "AddGroupMember", "RelativePath": "api/GroupMembers", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "groupMember", "Type": "webApi.Models.GroupMember", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.GroupMember", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.GroupMembersController", "Method": "GetGroupMember", "RelativePath": "api/GroupMembers/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.GroupMember", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.GroupMembersController", "Method": "RemoveGroupMember", "RelativePath": "api/GroupMembers/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.GroupMembersController", "Method": "UpdateMemberRole", "RelativePath": "api/GroupMembers/{id}/role", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "webApi.Controllers.UpdateRoleRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.GroupMembersController", "Method": "GetMembersByGroup", "RelativePath": "api/GroupMembers/group/{groupId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "groupId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.GroupMembersController", "Method": "GetGroupsByUser", "RelativePath": "api/GroupMembers/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.MessageAttachmentsController", "Method": "GetMessageAttachments", "RelativePath": "api/MessageAttachments", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.MessageAttachment, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.MessageAttachmentsController", "Method": "GetMessageAttachment", "RelativePath": "api/MessageAttachments/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.MessageAttachment", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.MessageAttachmentsController", "Method": "UpdateMessageAttachment", "RelativePath": "api/MessageAttachments/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "attachment", "Type": "webApi.Models.MessageAttachment", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.MessageAttachmentsController", "Method": "DeleteMessageAttachment", "RelativePath": "api/MessageAttachments/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.MessageAttachmentsController", "Method": "DownloadAttachment", "RelativePath": "api/MessageAttachments/{id}/download", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.MessageAttachmentsController", "Method": "GetAttachmentsByMessage", "RelativePath": "api/MessageAttachments/message/{messageId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "messageId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.MessageAttachment, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.MessageAttachmentsController", "Method": "GetAttachmentStatistics", "RelativePath": "api/MessageAttachments/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.MessageAttachmentsController", "Method": "UploadAttachment", "RelativePath": "api/MessageAttachments/upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "messageId", "Type": "System.Int32", "IsRequired": false}, {"Name": "uploadedBy", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "webApi.Models.MessageAttachment", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.MessageReactionsController", "Method": "GetMessageReactions", "RelativePath": "api/MessageReactions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.MessageReaction, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.MessageReactionsController", "Method": "AddReaction", "RelativePath": "api/MessageReactions", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "reaction", "Type": "webApi.Models.MessageReaction", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.MessageReaction", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.MessageReactionsController", "Method": "GetMessageReaction", "RelativePath": "api/MessageReactions/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.MessageReaction", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.MessageReactionsController", "Method": "DeleteReaction", "RelativePath": "api/MessageReactions/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.MessageReactionsController", "Method": "GetReactionsByMessage", "RelativePath": "api/MessageReactions/message/{messageId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "messageId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.MessageReactionsController", "Method": "GetMessageReactionStatistics", "RelativePath": "api/MessageReactions/message/{messageId}/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "messageId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.MessageReactionsController", "Method": "GetReactionStatistics", "RelativePath": "api/MessageReactions/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.MessageReactionsController", "Method": "ToggleReaction", "RelativePath": "api/MessageReactions/toggle", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "webApi.Controllers.ToggleReactionRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.MessageReadsController", "Method": "GetMessageReads", "RelativePath": "api/MessageReads", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.MessageRead, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.MessageReadsController", "Method": "GetMessageRead", "RelativePath": "api/MessageReads/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.MessageRead", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.MessageReadsController", "Method": "DeleteMessageRead", "RelativePath": "api/MessageReads/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.MessageReadsController", "Method": "MarkMessageAsRead", "RelativePath": "api/MessageReads/mark-read", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "webApi.Controllers.MarkAsReadRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.MessageRead", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.MessageReadsController", "Method": "GetMessageReadsByMessage", "RelativePath": "api/MessageReads/message/{messageId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "messageId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.MessageReadsController", "Method": "GetMessageReadStatistics", "RelativePath": "api/MessageReads/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.MessageReadsController", "Method": "GetMessageReadsByUser", "RelativePath": "api/MessageReads/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.MessageRead, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.MessagesController", "Method": "GetMessages", "RelativePath": "api/Messages", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Message, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.MessagesController", "Method": "SendMessage", "RelativePath": "api/Messages", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "message", "Type": "webApi.Models.Message", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Message", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.MessagesController", "Method": "GetMessage", "RelativePath": "api/Messages/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Message", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.MessagesController", "Method": "UpdateMessage", "RelativePath": "api/Messages/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "message", "Type": "webApi.Models.Message", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.MessagesController", "Method": "DeleteMessage", "RelativePath": "api/Messages/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.MessagesController", "Method": "MarkMessageAsRead", "RelativePath": "api/Messages/{id}/mark-read", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.MessagesController", "Method": "PinMessage", "RelativePath": "api/Messages/{id}/pin/{userId}", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.MessagesController", "Method": "ForwardMessage", "RelativePath": "api/Messages/{messageId}/forward", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "messageId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "webApi.Controllers.ForwardMessageRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Message", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.MessagesController", "Method": "ReplyToMessage", "RelativePath": "api/Messages/{messageId}/reply", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "messageId", "Type": "System.Int32", "IsRequired": true}, {"Name": "reply", "Type": "webApi.Models.Message", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Message", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.MessagesController", "Method": "GetGroupMessages", "RelativePath": "api/Messages/group/{groupId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "groupId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Message, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.MessagesController", "Method": "DeleteGroupMessages", "RelativePath": "api/Messages/group/{groupId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "groupId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.MessagesController", "Method": "GetMessagesByDateRange", "RelativePath": "api/Messages/group/{groupId}/date-range", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "groupId", "Type": "System.Int32", "IsRequired": true}, {"Name": "start", "Type": "System.Int64", "IsRequired": false}, {"Name": "end", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Message, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.MessagesController", "Method": "MarkGroupMessagesAsRead", "RelativePath": "api/Messages/group/{groupId}/mark-all-read", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "groupId", "Type": "System.Int32", "IsRequired": true}, {"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.MessagesController", "Method": "SearchGroupMessages", "RelativePath": "api/Messages/group/{groupId}/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "groupId", "Type": "System.Int32", "IsRequired": true}, {"Name": "q", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Message, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.MessagesController", "Method": "GetGroupUnreadMessageCount", "RelativePath": "api/Messages/group/{groupId}/unread-count", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "groupId", "Type": "System.Int32", "IsRequired": true}, {"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.MessagesController", "Method": "GetRecentMessages", "RelativePath": "api/Messages/recent", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "limit", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Message, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.MessagesController", "Method": "SearchMessages", "RelativePath": "api/Messages/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "q", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Message, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.MessagesController", "Method": "GetMessageStatistics", "RelativePath": "api/Messages/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.MessagesController", "Method": "GetUnreadMessages", "RelativePath": "api/Messages/unread", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Message, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.MessagesController", "Method": "GetUnreadMessageCount", "RelativePath": "api/Messages/unread-count", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.MessagesController", "Method": "GetUserMessages", "RelativePath": "api/Messages/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Message, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.NotificationsController", "Method": "GetNotifications", "RelativePath": "api/Notifications", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Notification, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.NotificationsController", "Method": "PostNotification", "RelativePath": "api/Notifications", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "notification", "Type": "webApi.Models.Notification", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Notification", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.NotificationsController", "Method": "GetNotification", "RelativePath": "api/Notifications/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Notification", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.NotificationsController", "Method": "PutNotification", "RelativePath": "api/Notifications/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "notification", "Type": "webApi.Models.Notification", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.NotificationsController", "Method": "DeleteNotification", "RelativePath": "api/Notifications/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.NotificationsController", "Method": "MarkAsRead", "RelativePath": "api/Notifications/{id}/mark-read", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.NotificationsController", "Method": "GetUserNotifications", "RelativePath": "api/Notifications/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Notification, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.NotificationsController", "Method": "MarkAllAsRead", "RelativePath": "api/Notifications/user/{userId}/mark-all-read", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}]}, {"ContainingType": "webApi.Controllers.NotificationsController", "Method": "GetUnreadNotifications", "RelativePath": "api/Notifications/user/{userId}/unread", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Notification, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.NotificationsController", "Method": "GetUnreadNotificationsCount", "RelativePath": "api/Notifications/user/{userId}/unread/count", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.NotificationSettingsController", "Method": "GetNotificationSettings", "RelativePath": "api/NotificationSettings", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.NotificationSetting, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.NotificationSettingsController", "Method": "PostNotificationSetting", "RelativePath": "api/NotificationSettings", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "notificationSetting", "Type": "webApi.Models.NotificationSetting", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.NotificationSetting", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.NotificationSettingsController", "Method": "GetNotificationSetting", "RelativePath": "api/NotificationSettings/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.NotificationSetting", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.NotificationSettingsController", "Method": "PutNotificationSetting", "RelativePath": "api/NotificationSettings/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "notificationSetting", "Type": "webApi.Models.NotificationSetting", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.NotificationSettingsController", "Method": "DeleteNotificationSetting", "RelativePath": "api/NotificationSettings/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.NotificationSettingsController", "Method": "ToggleNotificationSetting", "RelativePath": "api/NotificationSettings/{id}/toggle", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "enabled", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.NotificationSettingsController", "Method": "GetNotificationTypes", "RelativePath": "api/NotificationSettings/types", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.NotificationSettingsController", "Method": "GetUserNotificationSettings", "RelativePath": "api/NotificationSettings/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.NotificationSetting, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.NotificationSettingsController", "Method": "BulkUpdateUserNotificationSettings", "RelativePath": "api/NotificationSettings/user/{userId}/bulk-update", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}, {"Name": "settings", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.NotificationSettingsController", "Method": "GetUserNotificationSettingByType", "RelativePath": "api/NotificationSettings/user/{userId}/type/{notificationType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}, {"Name": "notificationType", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.NotificationSetting", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.PermissionsController", "Method": "GetPermissions", "RelativePath": "api/Permissions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Permission, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.PermissionsController", "Method": "PostPermission", "RelativePath": "api/Permissions", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "permission", "Type": "webApi.Models.Permission", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Permission", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.PermissionsController", "Method": "GetPermission", "RelativePath": "api/Permissions/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Permission", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.PermissionsController", "Method": "PutPermission", "RelativePath": "api/Permissions/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "permission", "Type": "webApi.Models.Permission", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.PermissionsController", "Method": "DeletePermission", "RelativePath": "api/Permissions/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.PermissionsController", "Method": "GetUsersWithPermission", "RelativePath": "api/Permissions/{id}/users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.User, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.PermissionsController", "Method": "GetCustomRolePermissions", "RelativePath": "api/Permissions/by-custom-role/{customRoleId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "customRoleId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Permission, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.PermissionsController", "Method": "GetRolePermissions", "RelativePath": "api/Permissions/by-role/{roleId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "roleId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Permission, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.PermissionsController", "Method": "GetUserDirectPermissions", "RelativePath": "api/Permissions/by-user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Permission, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.PermissionsController", "Method": "CheckMultiplePermissions", "RelativePath": "api/Permissions/check-multiple/{userId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "webApi.Controllers.CheckMultiplePermissionsRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.PermissionsController", "Method": "CheckUserPermission", "RelativePath": "api/Permissions/check/{userId}/{permissionName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}, {"Name": "permissionName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.PermissionsController", "Method": "ClearUserPermissionsCache", "RelativePath": "api/Permissions/clear-cache/{userId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.PermissionsController", "Method": "GetPermissionsByGroup", "RelativePath": "api/Permissions/group/{group}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "group", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Permission, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.PermissionsController", "Method": "GetPermissionGroups", "RelativePath": "api/Permissions/groups", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.PermissionsController", "Method": "QuickCheckPermission", "RelativePath": "api/Permissions/quick-check/{userId}/{permissionName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}, {"Name": "permissionName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.PermissionsController", "Method": "GetAllUserPermissions", "RelativePath": "api/Permissions/user-all-permissions/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.PowerBIController", "Method": "CreateReport", "RelativePath": "api/PowerBI", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "reportData", "Type": "System.Text.Json.JsonElement", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.PowerBIController", "Method": "GetReport", "RelativePath": "api/PowerBI/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.PowerBIController", "Method": "UpdateReport", "RelativePath": "api/PowerBI/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "reportData", "Type": "System.Text.Json.JsonElement", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.PowerBIController", "Method": "DeleteReport", "RelativePath": "api/PowerBI/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.PowerBIController", "Method": "GetChartData", "RelativePath": "api/PowerBI/chart-data", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "reportData", "Type": "System.Text.Json.JsonElement", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.PowerBIController", "Method": "GetMyReports", "RelativePath": "api/PowerBI/my-reports", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.PowerBIController", "Method": "GetSharedReports", "RelativePath": "api/PowerBI/shared-reports", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.PowerBIController", "Method": "SuggestTableRelations", "RelativePath": "api/PowerBI/suggest-relations", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "table1", "Type": "System.String", "IsRequired": false}, {"Name": "table2", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.PowerBIController", "Method": "GetAvailableTables", "RelativePath": "api/PowerBI/tables", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.PowerBIController", "Method": "GetTableColumns", "RelativePath": "api/PowerBI/tables/{tableName}/columns", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tableName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.ReportsController", "Method": "GetReports", "RelativePath": "api/Reports", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Report, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ReportsController", "Method": "PostReport", "RelativePath": "api/Reports", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "report", "Type": "webApi.Models.Report", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Report", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.ReportsController", "Method": "GetReport", "RelativePath": "api/Reports/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Report", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.ReportsController", "Method": "PutReport", "RelativePath": "api/Reports/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "report", "Type": "webApi.Models.Report", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.ReportsController", "Method": "DeleteReport", "RelativePath": "api/Reports/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.ReportsController", "Method": "GetPublicReports", "RelativePath": "api/Reports/public", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Report, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ReportsController", "Method": "GetTasksStatistics", "RelativePath": "api/Reports/tasks-statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ReportsController", "Method": "GetReportsByType", "RelativePath": "api/Reports/type/{reportType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "reportType", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Report, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ReportsController", "Method": "GetUserPerformanceReport", "RelativePath": "api/Reports/user-performance", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ReportsController", "Method": "GetUserReports", "RelativePath": "api/Reports/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Report, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ReportsController", "Method": "GetUsersStatistics", "RelativePath": "api/Reports/users-statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ReportSchedulesController", "Method": "GetReportSchedules", "RelativePath": "api/ReportSchedules", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ReportSchedule, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ReportSchedulesController", "Method": "PostReportSchedule", "RelativePath": "api/ReportSchedules", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "reportSchedule", "Type": "webApi.Models.ReportSchedule", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.ReportSchedule", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.ReportSchedulesController", "Method": "GetReportSchedule", "RelativePath": "api/ReportSchedules/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.ReportSchedule", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.ReportSchedulesController", "Method": "PutReportSchedule", "RelativePath": "api/ReportSchedules/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "reportSchedule", "Type": "webApi.Models.ReportSchedule", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.ReportSchedulesController", "Method": "DeleteReportSchedule", "RelativePath": "api/ReportSchedules/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.ReportSchedulesController", "Method": "ToggleActive", "RelativePath": "api/ReportSchedules/{id}/toggle-active", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "active", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.ReportSchedulesController", "Method": "UpdateNextRunTime", "RelativePath": "api/ReportSchedules/{id}/update-next-run", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.ReportSchedulesController", "Method": "GetActiveSchedules", "RelativePath": "api/ReportSchedules/active", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ReportSchedule, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ReportSchedulesController", "Method": "GetDueSchedules", "RelativePath": "api/ReportSchedules/due", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ReportSchedule, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ReportSchedulesController", "Method": "GetSchedulesByFrequency", "RelativePath": "api/ReportSchedules/frequency/{frequency}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "frequency", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ReportSchedule, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ReportSchedulesController", "Method": "GetReportSchedulesByReport", "RelativePath": "api/ReportSchedules/report/{reportId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "reportId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ReportSchedule, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ReportSchedulesController", "Method": "GetScheduleStatistics", "RelativePath": "api/ReportSchedules/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.RolesController", "Method": "GetRoles", "RelativePath": "api/Roles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Role, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.RolesController", "Method": "PostRole", "RelativePath": "api/Roles", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "role", "Type": "webApi.Models.Role", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Role", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.RolesController", "Method": "GetRole", "RelativePath": "api/Roles/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Role", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.RolesController", "Method": "PutRole", "RelativePath": "api/Roles/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "role", "Type": "webApi.Models.Role", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.RolesController", "Method": "DeleteRole", "RelativePath": "api/Roles/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.RolesController", "Method": "GetAllPermissionsForRole", "RelativePath": "api/Roles/{id}/all-permissions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Permission, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.RolesController", "Method": "GetCustomRolesForRole", "RelativePath": "api/Roles/{id}/custom-roles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.CustomRole, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.RolesController", "Method": "GetRoleDefaultPermissions", "RelativePath": "api/Roles/{id}/default-permissions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.RoleDefaultPermission, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.RolesController", "Method": "GrantDefaultPermissionToRole", "RelativePath": "api/Roles/{id}/default-permissions/grant", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "webApi.Models.GrantRevokePermissionDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.RolesController", "Method": "RevokeDefaultPermissionFromRole", "RelativePath": "api/Roles/{id}/default-permissions/revoke", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "webApi.Models.GrantRevokePermissionDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.RolesController", "Method": "GetRolePermissions", "RelativePath": "api/Roles/{id}/permissions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Permission, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.RolesController", "Method": "GetRoleUsers", "RelativePath": "api/Roles/{id}/users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.User, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ScreenController", "Method": "GetScreens", "RelativePath": "api/Screen", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Screen, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ScreenController", "Method": "PostScreen", "RelativePath": "api/Screen", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "screen", "Type": "webApi.Models.Screen", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Screen", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ScreenController", "Method": "GetScreen", "RelativePath": "api/Screen/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Screen", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ScreenController", "Method": "PutScreen", "RelativePath": "api/Screen/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "screen", "Type": "webApi.Models.Screen", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.ScreenController", "Method": "DeleteScreen", "RelativePath": "api/Screen/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.ScreenActionsController", "Method": "GetScreenActions", "RelativePath": "api/ScreenActions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ScreenAction, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ScreenActionsController", "Method": "PostScreenAction", "RelativePath": "api/ScreenActions", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "screenAction", "Type": "webApi.Models.ScreenAction", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.ScreenAction", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ScreenActionsController", "Method": "PutScreenAction", "RelativePath": "api/ScreenActions/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "screenAction", "Type": "webApi.Models.ScreenAction", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.ScreenActionsController", "Method": "DeleteScreenAction", "RelativePath": "api/ScreenActions/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.ScreenActionsController", "Method": "GetScreensByAction", "RelativePath": "api/ScreenActions/by-action/{actionId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "actionId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Screen, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.ScreenActionsController", "Method": "GetActionsByScreen", "RelativePath": "api/ScreenActions/by-screen/{screenId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "screenId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.ActionEntity, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.SeedDataController", "Method": "ClearAllData", "RelativePath": "api/SeedData/clear-all", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.SeedDataController", "Method": "SeedAllData", "RelativePath": "api/SeedData/seed-all", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.SeedDataController", "Method": "SeedDepartments", "RelativePath": "api/SeedData/seed-departments", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.SeedDataController", "Method": "SeedTasks", "RelativePath": "api/SeedData/seed-tasks", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.SeedDataController", "Method": "SeedUsers", "RelativePath": "api/SeedData/seed-users", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.SubtasksController", "Method": "GetSubtasks", "RelativePath": "api/Subtasks", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Subtask, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.SubtasksController", "Method": "PostSubtask", "RelativePath": "api/Subtasks", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "webApi.Controllers.CreateSubtaskRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Subtask", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.SubtasksController", "Method": "GetSubtask", "RelativePath": "api/Subtasks/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Subtask", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.SubtasksController", "Method": "PutSubtask", "RelativePath": "api/Subtasks/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "subtask", "Type": "webApi.Models.Subtask", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Subtask", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.SubtasksController", "Method": "DeleteSubtask", "RelativePath": "api/Subtasks/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.SubtasksController", "Method": "CompleteSubtask", "RelativePath": "api/Subtasks/{id}/complete", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.SubtasksController", "Method": "UncompleteSubtask", "RelativePath": "api/Subtasks/{id}/uncomplete", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.SubtasksController", "Method": "GetSubtasksByTask", "RelativePath": "api/Subtasks/task/{taskId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Subtask, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.SubtasksController", "Method": "GetCompletedSubtasksByTask", "RelativePath": "api/Subtasks/task/{taskId}/completed", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Subtask, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.SubtasksController", "Method": "GetCompletedSubtasksCount", "RelativePath": "api/Subtasks/task/{taskId}/completed/count", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.SubtasksController", "Method": "GetSubtasksCompletionPercentage", "RelativePath": "api/Subtasks/task/{taskId}/completion-percentage", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Double", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.SubtasksController", "Method": "GetSubtasksCount", "RelativePath": "api/Subtasks/task/{taskId}/count", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.SubtasksController", "Method": "GetPendingSubtasksByTask", "RelativePath": "api/Subtasks/task/{taskId}/pending", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Subtask, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.SystemLogsController", "Method": "GetSystemLogs", "RelativePath": "api/SystemLogs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.SystemLog, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.SystemLogsController", "Method": "PostSystemLog", "RelativePath": "api/SystemLogs", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "systemLog", "Type": "webApi.Models.SystemLog", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.SystemLog", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.SystemLogsController", "Method": "GetSystemLog", "RelativePath": "api/SystemLogs/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.SystemLog", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.SystemLogsController", "Method": "DeleteSystemLog", "RelativePath": "api/SystemLogs/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.SystemLogsController", "Method": "CleanupOldLogs", "RelativePath": "api/SystemLogs/cleanup", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "daysOld", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.SystemLogsController", "Method": "CleanupLogsByLevel", "RelativePath": "api/SystemLogs/cleanup/level/{level}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "level", "Type": "System.String", "IsRequired": true}, {"Name": "daysOld", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.SystemLogsController", "Method": "GetSystemLogsByDateRange", "RelativePath": "api/SystemLogs/date-range", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.Int64", "IsRequired": false}, {"Name": "endDate", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.SystemLog, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.SystemLogsController", "Method": "GetErrorLogs", "RelativePath": "api/SystemLogs/errors", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.SystemLog, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.SystemLogsController", "Method": "GetSystemLogsByLevel", "RelativePath": "api/SystemLogs/level/{level}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "level", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.SystemLog, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.SystemLogsController", "Method": "LogQuick", "RelativePath": "api/SystemLogs/log", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "logLevel", "Type": "System.String", "IsRequired": false}, {"Name": "logType", "Type": "System.String", "IsRequired": false}, {"Name": "message", "Type": "System.String", "IsRequired": false}, {"Name": "userId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "details", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "webApi.Models.SystemLog", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.SystemLogsController", "Method": "SearchSystemLogs", "RelativePath": "api/SystemLogs/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "searchTerm", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.SystemLog, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.SystemLogsController", "Method": "GetLogStatistics", "RelativePath": "api/SystemLogs/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.SystemLogsController", "Method": "GetSystemLogsByType", "RelativePath": "api/SystemLogs/type/{logType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "logType", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.SystemLog, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.SystemLogsController", "Method": "GetSystemLogsByUser", "RelativePath": "api/SystemLogs/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.SystemLog, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.SystemSettingsController", "Method": "GetSystemSettings", "RelativePath": "api/SystemSettings", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.SystemSetting, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.SystemSettingsController", "Method": "PostSystemSetting", "RelativePath": "api/SystemSettings", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "systemSetting", "Type": "webApi.Models.SystemSetting", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.SystemSetting", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.SystemSettingsController", "Method": "GetSystemSetting", "RelativePath": "api/SystemSettings/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.SystemSetting", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.SystemSettingsController", "Method": "PutSystemSetting", "RelativePath": "api/SystemSettings/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "systemSetting", "Type": "webApi.Models.SystemSetting", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.SystemSettingsController", "Method": "DeleteSystemSetting", "RelativePath": "api/SystemSettings/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.SystemSettingsController", "Method": "BulkUpdateSystemSettings", "RelativePath": "api/SystemSettings/bulk-update", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "settings", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.SystemSettingsController", "Method": "GetSystemSettingsByGroup", "RelativePath": "api/SystemSettings/group/{group}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "group", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.SystemSetting, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.SystemSettingsController", "Method": "GetSettingGroups", "RelativePath": "api/SystemSettings/groups", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.SystemSettingsController", "Method": "GetSystemSettingByKey", "RelativePath": "api/SystemSettings/key/{key}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "key", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.SystemSetting", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.SystemSettingsController", "Method": "UpdateSystemSettingValue", "RelativePath": "api/SystemSettings/key/{key}", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "key", "Type": "System.String", "IsRequired": true}, {"Name": "value", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.SystemSettingsController", "Method": "DeleteSystemSettingByKey", "RelativePath": "api/SystemSettings/key/{key}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "key", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.SystemSettingsController", "Method": "GetSystemSettingValue", "RelativePath": "api/SystemSettings/value/{key}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "key", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.TaskAccessController", "Method": "AddUserToTaskAccess", "RelativePath": "api/TaskAccess", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "webApi.Controllers.TaskAccessRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.TaskAccessController", "Method": "GetTaskAccessUsers", "RelativePath": "api/TaskAccess/task/{taskId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskAccessController", "Method": "UpdateTaskAccess", "RelativePath": "api/TaskAccess/task/{taskId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "webApi.Controllers.UpdateTaskAccessRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.TaskAccessController", "Method": "GetTaskContributors", "RelativePath": "api/TaskAccess/task/{taskId}/contributors", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskAccessController", "Method": "RemoveUserFromTaskAccess", "RelativePath": "api/TaskAccess/task/{taskId}/user/{userId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}, {"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.TaskCommentsController", "Method": "GetTaskComments", "RelativePath": "api/TaskComments", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.TaskComment, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskCommentsController", "Method": "PostTaskComment", "RelativePath": "api/TaskComments", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDto", "Type": "webApi.Models.DTOs.CreateTaskCommentDto", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.TaskComment", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.TaskCommentsController", "Method": "GetTaskComment", "RelativePath": "api/TaskComments/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.TaskComment", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.TaskCommentsController", "Method": "PutTaskComment", "RelativePath": "api/TaskComments/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "taskComment", "Type": "webApi.Models.TaskComment", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.TaskCommentsController", "Method": "DeleteTaskComment", "RelativePath": "api/TaskComments/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.TaskCommentsController", "Method": "DeleteTaskCommentPermanent", "RelativePath": "api/TaskComments/{id}/permanent", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.TaskCommentsController", "Method": "PostTaskCommentSimple", "RelativePath": "api/TaskComments/simple", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDto", "Type": "webApi.Models.DTOs.CreateTaskCommentDto", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.TaskComment", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.TaskCommentsController", "Method": "GetTaskCommentsByTask", "RelativePath": "api/TaskComments/task/{taskId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.TaskComment, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskCommentsController", "Method": "GetTaskCommentsCount", "RelativePath": "api/TaskComments/task/{taskId}/count", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskCommentsController", "Method": "GetTaskCommentsByUser", "RelativePath": "api/TaskComments/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.TaskComment, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskDocumentsController", "Method": "GetTaskDocuments", "RelativePath": "api/TaskDocuments", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": false}, {"Name": "type", "Type": "System.String", "IsRequired": false}, {"Name": "isShared", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "permission", "Type": "System.String", "IsRequired": false}, {"Name": "search", "Type": "System.String", "IsRequired": false}, {"Name": "fromDate", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "toDate", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[webApi.Models.TaskDocumentResponse, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskDocumentsController", "Method": "CreateTaskDocument", "RelativePath": "api/TaskDocuments", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "webApi.Models.CreateTaskDocumentRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.TaskDocumentResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskDocumentsController", "Method": "GetTaskDocumentById", "RelativePath": "api/TaskDocuments/{taskId}/{archiveDocumentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}, {"Name": "archiveDocumentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.TaskDocumentResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskDocumentsController", "Method": "UpdateTaskDocument", "RelativePath": "api/TaskDocuments/{taskId}/{archiveDocumentId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}, {"Name": "archiveDocumentId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "webApi.Models.UpdateTaskDocumentRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.TaskDocumentResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskDocumentsController", "Method": "DeleteTaskDocument", "RelativePath": "api/TaskDocuments/{taskId}/{archiveDocumentId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}, {"Name": "archiveDocumentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.TaskDocumentsController", "Method": "ShareDocumentWithContributors", "RelativePath": "api/TaskDocuments/{taskId}/{archiveDocumentId}/share", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}, {"Name": "archiveDocumentId", "Type": "System.Int32", "IsRequired": true}, {"Name": "contributorIds", "Type": "System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.TaskDocumentsController", "Method": "UnshareDocument", "RelativePath": "api/TaskDocuments/{taskId}/{archiveDocumentId}/unshare", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}, {"Name": "archiveDocumentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.TaskDocumentsController", "Method": "GetRecentTaskDocuments", "RelativePath": "api/TaskDocuments/recent/{taskId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}, {"Name": "limit", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[webApi.Models.TaskDocumentResponse, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskDocumentsController", "Method": "SearchTaskDocuments", "RelativePath": "api/TaskDocuments/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "query", "Type": "System.String", "IsRequired": false}, {"Name": "taskId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "type", "Type": "System.String", "IsRequired": false}, {"Name": "isShared", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "permission", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[webApi.Models.TaskDocumentResponse, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskDocumentsController", "Method": "GetSharedTaskDocuments", "RelativePath": "api/TaskDocuments/shared/{taskId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[webApi.Models.TaskDocumentResponse, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskDocumentsController", "Method": "GetTaskDocumentStats", "RelativePath": "api/TaskDocuments/stats/{taskId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.TaskDocumentStats", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskHistoryController", "Method": "GetTaskHistories", "RelativePath": "api/TaskHistory", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.TaskHistory, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskHistoryController", "Method": "PostTaskHistory", "RelativePath": "api/TaskHistory", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskHistory", "Type": "webApi.Models.TaskHistory", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.TaskHistory", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.TaskHistoryController", "Method": "GetTaskHistory", "RelativePath": "api/TaskHistory/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.TaskHistory", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.TaskHistoryController", "Method": "PutTaskHistory", "RelativePath": "api/TaskHistory/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "taskHistory", "Type": "webApi.Models.TaskHistory", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.TaskHistoryController", "Method": "DeleteTaskHistory", "RelativePath": "api/TaskHistory/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.TaskHistoryController", "Method": "GetTaskHistoryByAction", "RelativePath": "api/TaskHistory/action/{action}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "action", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.TaskHistory, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskHistoryController", "Method": "GetActions", "RelativePath": "api/TaskHistory/actions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskHistoryController", "Method": "GetTaskHistoryByChangeType", "RelativePath": "api/TaskHistory/change-type/{changeType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "changeType", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.TaskHistory, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskHistoryController", "Method": "GetChangeTypes", "RelativePath": "api/TaskHistory/change-types", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskHistoryController", "Method": "GetTaskHistoryByDateRange", "RelativePath": "api/TaskHistory/date-range", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.Int64", "IsRequired": false}, {"Name": "endDate", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.TaskHistory, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskHistoryController", "Method": "GetTaskHistoryByTask", "RelativePath": "api/TaskHistory/task/{taskId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.TaskHistory, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskHistoryController", "Method": "GetTaskHistoryByUser", "RelativePath": "api/TaskHistory/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.TaskHistory, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskMessagesController", "Method": "SendMessage", "RelativePath": "api/TaskMessages", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "webApi.Models.SendTaskMessageRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.TaskMessageResponse", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskMessagesController", "Method": "GetAllTaskMessages", "RelativePath": "api/TaskMessages", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.TaskMessageResponse, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskMessagesController", "Method": "GetMessage", "RelativePath": "api/TaskMessages/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.TaskMessageResponse", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskMessagesController", "Method": "UpdateMessage", "RelativePath": "api/TaskMessages/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "webApi.Models.UpdateTaskMessageRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.TaskMessageResponse", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskMessagesController", "Method": "DeleteMessage", "RelativePath": "api/TaskMessages/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.TaskMessagesController", "Method": "MarkForFollowUp", "RelativePath": "api/TaskMessages/{id}/follow-up", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "webApi.Models.MarkForFollowUpRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.TaskMessageResponse", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskMessagesController", "Method": "PinMessage", "RelativePath": "api/TaskMessages/{id}/pin", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "webApi.Models.PinTaskMessageRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.TaskMessageResponse", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskMessagesController", "Method": "MarkAsRead", "RelativePath": "api/TaskMessages/{id}/read", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.TaskMessagesController", "Method": "GetTaskMessages", "RelativePath": "api/TaskMessages/task/{taskId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.TaskMessageResponse, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskMessagesController", "Method": "GetPinnedMessages", "RelativePath": "api/TaskMessages/task/{taskId}/pinned", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.TaskMessageResponse, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskPriorityController", "Method": "GetTaskPriorities", "RelativePath": "api/TaskPriority", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskPriorityController", "Method": "PostTaskPriority", "RelativePath": "api/TaskPriority", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskPriority", "Type": "System.Object", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.TaskPriorityController", "Method": "GetTaskPriority", "RelativePath": "api/TaskPriority/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.TaskPriorityController", "Method": "PutTaskPriority", "RelativePath": "api/TaskPriority/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "taskPriority", "Type": "System.Object", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.TaskPriorityController", "Method": "DeleteTaskPriority", "RelativePath": "api/TaskPriority/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.TaskPriorityController", "Method": "GetTasksCountByPriority", "RelativePath": "api/TaskPriority/{id}/tasks/count", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.TaskProgressTrackersController", "Method": "GetTaskProgressTrackers", "RelativePath": "api/TaskProgressTrackers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.TaskProgressTracker, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskProgressTrackersController", "Method": "PostTaskProgressTracker", "RelativePath": "api/TaskProgressTrackers", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskProgressTracker", "Type": "webApi.Models.TaskProgressTracker", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.TaskProgressTracker", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.TaskProgressTrackersController", "Method": "GetTaskProgressTracker", "RelativePath": "api/TaskProgressTrackers/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.TaskProgressTracker", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.TaskProgressTrackersController", "Method": "PutTaskProgressTracker", "RelativePath": "api/TaskProgressTrackers/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "taskProgressTracker", "Type": "webApi.Models.TaskProgressTracker", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.TaskProgressTrackersController", "Method": "DeleteTaskProgressTracker", "RelativePath": "api/TaskProgressTrackers/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.TaskProgressTrackersController", "Method": "GetTaskProgressTrackersByDateRange", "RelativePath": "api/TaskProgressTrackers/date-range", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.Int64", "IsRequired": false}, {"Name": "endDate", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.TaskProgressTracker, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskProgressTrackersController", "Method": "GetProgressStatistics", "RelativePath": "api/TaskProgressTrackers/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskProgressTrackersController", "Method": "GetTaskProgressTrackersByTask", "RelativePath": "api/TaskProgressTrackers/task/{taskId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.TaskProgressTracker, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskProgressTrackersController", "Method": "DeleteTaskProgressTrackersByTask", "RelativePath": "api/TaskProgressTrackers/task/{taskId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskProgressTrackersController", "Method": "GetLatestTaskProgress", "RelativePath": "api/TaskProgressTrackers/task/{taskId}/latest", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.TaskProgressTracker", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.TaskProgressTrackersController", "Method": "UpdateTaskProgress", "RelativePath": "api/TaskProgressTrackers/update-progress", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": false}, {"Name": "progressPercentage", "Type": "System.Int32", "IsRequired": false}, {"Name": "updatedBy", "Type": "System.Int32", "IsRequired": false}, {"Name": "notes", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "webApi.Models.TaskProgressTracker", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.TaskProgressTrackersController", "Method": "GetTaskProgressTrackersByUser", "RelativePath": "api/TaskProgressTrackers/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.TaskProgressTracker, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TasksController", "Method": "GetTasks", "RelativePath": "api/Tasks", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "search", "Type": "System.String", "IsRequired": false}, {"Name": "searchQuery", "Type": "System.String", "IsRequired": false}, {"Name": "orderBy", "Type": "System.String", "IsRequired": false}, {"Name": "sortBy", "Type": "System.String", "IsRequired": false}, {"Name": "orderDirection", "Type": "System.String", "IsRequired": false}, {"Name": "sortOrder", "Type": "System.String", "IsRequired": false}, {"Name": "status", "Type": "System.String", "IsRequired": false}, {"Name": "priority", "Type": "System.String", "IsRequired": false}, {"Name": "assigneeId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "departmentId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TasksController", "Method": "PostTask", "RelativePath": "api/Tasks", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "task", "Type": "webApi.Models.Task", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Task", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.TasksController", "Method": "GetTask", "RelativePath": "api/Tasks/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Task", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.TasksController", "Method": "PutTask", "RelativePath": "api/Tasks/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "task", "Type": "webApi.Models.Task", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.TasksController", "Method": "DeleteTask", "RelativePath": "api/Tasks/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.TasksController", "Method": "GetTaskComprehensiveReportData", "RelativePath": "api/Tasks/{id}/comprehensive-report-data", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.TasksController", "Method": "TransferTask", "RelativePath": "api/Tasks/{id}/transfer", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "transferRequest", "Type": "TaskTransferRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.Task", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.TasksController", "Method": "GetTasksByAssignee", "RelativePath": "api/Tasks/assignee/{assigneeId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "assigneeId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Task, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TasksController", "Method": "GetTasksByPriority", "RelativePath": "api/Tasks/priority/{priority}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "priority", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Task, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TasksController", "Method": "GetTasksByStatus", "RelativePath": "api/Tasks/status/{status}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "status", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Task, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskStatusController", "Method": "GetTaskStatuses", "RelativePath": "api/TaskStatus", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.TaskStatusController", "Method": "PostTaskStatus", "RelativePath": "api/TaskStatus", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskStatus", "Type": "System.Object", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.TaskStatusController", "Method": "GetTaskStatus", "RelativePath": "api/TaskStatus/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.TaskStatusController", "Method": "PutTaskStatus", "RelativePath": "api/TaskStatus/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "taskStatus", "Type": "System.Object", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.TaskStatusController", "Method": "DeleteTaskStatus", "RelativePath": "api/TaskStatus/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.TaskStatusController", "Method": "GetTasksCountByStatus", "RelativePath": "api/TaskStatus/{id}/tasks/count", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.TaskTypesController", "Method": "GetTaskTypes", "RelativePath": "api/TaskTypes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.TaskType, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskTypesController", "Method": "PostTaskType", "RelativePath": "api/TaskTypes", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskType", "Type": "webApi.Models.TaskType", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.TaskType", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.TaskTypesController", "Method": "GetTaskType", "RelativePath": "api/TaskTypes/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.TaskType", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.TaskTypesController", "Method": "PutTaskType", "RelativePath": "api/TaskTypes/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "taskType", "Type": "webApi.Models.TaskType", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.TaskTypesController", "Method": "DeleteTaskType", "RelativePath": "api/TaskTypes/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.TaskTypesController", "Method": "SetAsDefault", "RelativePath": "api/TaskTypes/{id}/set-default", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.TaskTypesController", "Method": "GetTasksByType", "RelativePath": "api/TaskTypes/{id}/tasks", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.Task, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskTypesController", "Method": "GetTasksCountByType", "RelativePath": "api/TaskTypes/{id}/tasks/count", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TaskTypesController", "Method": "UnsetAsDefault", "RelativePath": "api/TaskTypes/{id}/unset-default", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.TaskTypesController", "Method": "GetDefaultTaskTypes", "RelativePath": "api/TaskTypes/default", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.TaskType, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TimeTrackingEntriesController", "Method": "GetTimeTrackingEntries", "RelativePath": "api/TimeTrackingEntries", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.TimeTrackingEntry, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TimeTrackingEntriesController", "Method": "PostTimeTrackingEntry", "RelativePath": "api/TimeTrackingEntries", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "timeTrackingEntry", "Type": "webApi.Models.TimeTrackingEntry", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.TimeTrackingEntry", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.TimeTrackingEntriesController", "Method": "GetTimeTrackingEntry", "RelativePath": "api/TimeTrackingEntries/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.TimeTrackingEntry", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.TimeTrackingEntriesController", "Method": "PutTimeTrackingEntry", "RelativePath": "api/TimeTrackingEntries/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "timeTrackingEntry", "Type": "webApi.Models.TimeTrackingEntry", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.TimeTrackingEntriesController", "Method": "DeleteTimeTrackingEntry", "RelativePath": "api/TimeTrackingEntries/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.TimeTrackingEntriesController", "Method": "StopTimeTracking", "RelativePath": "api/TimeTrackingEntries/{id}/stop", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.TimeTrackingEntry", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.TimeTrackingEntriesController", "Method": "StartTimeTracking", "RelativePath": "api/TimeTrackingEntries/start", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": false}, {"Name": "userId", "Type": "System.Int32", "IsRequired": false}, {"Name": "description", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "webApi.Models.TimeTrackingEntry", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.TimeTrackingEntriesController", "Method": "GetTimeTrackingEntriesByTask", "RelativePath": "api/TimeTrackingEntries/task/{taskId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.TimeTrackingEntry, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TimeTrackingEntriesController", "Method": "GetTotalTimeForTask", "RelativePath": "api/TimeTrackingEntries/task/{taskId}/total-time", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TimeTrackingEntriesController", "Method": "GetTimeTrackingEntriesByUser", "RelativePath": "api/TimeTrackingEntries/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.TimeTrackingEntry, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TimeTrackingEntriesController", "Method": "GetActiveTimeTrackingEntries", "RelativePath": "api/TimeTrackingEntries/user/{userId}/active", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.TimeTrackingEntry, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.TimeTrackingEntriesController", "Method": "GetTotalTimeForUser", "RelativePath": "api/TimeTrackingEntries/user/{userId}/total-time", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}, {"Name": "startDate", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.UploadController", "Method": "DeleteImage", "RelativePath": "api/Upload", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "webApi.Controllers.DeleteImageRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "webApi.Controllers.UploadController", "Method": "UploadImage", "RelativePath": "api/Upload/image", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "image", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "folder", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "webApi.Controllers.UserPermissionsController", "Method": "GetUserPermissions", "RelativePath": "api/UserPermissions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.UserPermission, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.UserPermissionsController", "Method": "PostUserPermission", "RelativePath": "api/UserPermissions", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userPermission", "Type": "webApi.Models.UserPermission", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.UserPermission", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.UserPermissionsController", "Method": "GetUserPermission", "RelativePath": "api/UserPermissions/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.UserPermission", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.UserPermissionsController", "Method": "PutUserPermission", "RelativePath": "api/UserPermissions/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "userPermission", "Type": "webApi.Models.UserPermission", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.UserPermissionsController", "Method": "DeleteUserPermission", "RelativePath": "api/UserPermissions/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.UserPermissionsController", "Method": "TestDatabase", "RelativePath": "api/UserPermissions/test", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "webApi.Controllers.UserPermissionsController", "Method": "GetCustomUserPermissions", "RelativePath": "api/UserPermissions/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.UserPermission, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.UsersController", "Method": "GetUsers", "RelativePath": "api/Users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "search", "Type": "System.String", "IsRequired": false}, {"Name": "searchQuery", "Type": "System.String", "IsRequired": false}, {"Name": "orderBy", "Type": "System.String", "IsRequired": false}, {"Name": "sortBy", "Type": "System.String", "IsRequired": false}, {"Name": "orderDirection", "Type": "System.String", "IsRequired": false}, {"Name": "sortOrder", "Type": "System.String", "IsRequired": false}, {"Name": "role", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "departmentId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "isActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.UsersController", "Method": "PostUser", "RelativePath": "api/Users", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "user", "Type": "webApi.Models.User", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.User", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "webApi.Controllers.UsersController", "Method": "GetUser", "RelativePath": "api/Users/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "webApi.Models.User", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.UsersController", "Method": "PutUser", "RelativePath": "api/Users/<USER>", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "user", "Type": "webApi.Models.User", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.UsersController", "Method": "DeleteUser", "RelativePath": "api/Users/<USER>", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "webApi.Controllers.UsersController", "Method": "UploadAvatar", "RelativePath": "api/Users/<USER>/upload-avatar", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}, {"Name": "avatar", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "webApi.Controllers.UsersController", "Method": "GetUsersByDepartment", "RelativePath": "api/Users/<USER>/{departmentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "departmentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.User, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.UsersController", "Method": "GetUsersByRole", "RelativePath": "api/Users/<USER>/{roleId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "roleId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.User, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "webApi.Controllers.UsersController", "Method": "SearchUsers", "RelativePath": "api/Users/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "searchTerm", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[webApi.Models.User, webApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}]