﻿using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.OpenApi;
using Microsoft.EntityFrameworkCore;
using webApi.Models;

namespace webApi.Models;

public partial class User
{
    public int Id { get; set; }

    public string Name { get; set; } = null!;

    public string Email { get; set; } = null!;

    public string? Username { get; set; }

    public string Password { get; set; } = null!;

    public string? ProfileImage { get; set; }

    public int? DepartmentId { get; set; }

    public int? RoleId { get; set; } // المفتاح الخارجي الرسمي
    
    

    public bool IsActive { get; set; }

    public bool IsOnline { get; set; }

    public long CreatedAt { get; set; }

    public long? LastLogin { get; set; }

    public long? LastSeen { get; set; }

    public string? FirstName { get; set; }

    public string? LastName { get; set; }

    public bool IsDeleted { get; set; }

    [JsonIgnore]
    public virtual ICollection<ArchiveCategory> ArchiveCategories { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<ArchiveDocument> ArchiveDocuments { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<ArchiveTag> ArchiveTags { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<Attachment> Attachments { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<Backup> BackupCreatedByNavigations { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<Backup> BackupRestoredByNavigations { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<CalendarEvent> CalendarEvents { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<ChatGroup> ChatGroups { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<Dashboard> Dashboards { get; set; } = [];

    public virtual Department? Department { get; set; }
    public virtual Role? Role { get; set; } // الربط الصحيح مع جدول الأدوار


    [JsonIgnore]
    public virtual ICollection<Department> Departments { get; set; } = [];

    //[JsonIgnore]
    //public virtual ICollection<Role> Roles { get; set; } = [];


    public virtual ICollection<GroupMember> GroupMembers { get; set; } = new List<GroupMember>();

    [JsonIgnore]
    public virtual ICollection<MessageAttachment> MessageAttachments { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<Message> MessageMarkedForFollowUpByNavigations { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<Message> MessagePinnedByNavigations { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<MessageReaction> MessageReactions { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<Message> MessageReceivers { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<Message> MessageSenders { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<NotificationSetting> NotificationSettings { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<Notification> Notifications { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<ReportSchedule> ReportSchedules { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<Report> Reports { get; set; } = [];


    [JsonIgnore]
    public virtual ICollection<SystemLog> SystemLogs { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<SystemSetting> SystemSettings { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<Task> TaskAssignees { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<TaskComment> TaskComments { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<Task> TaskCreators { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<TaskHistory> TaskHistoryChangedByNavigations { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<TaskHistory> TaskHistoryUsers { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<TaskPriority> TaskPriorities { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<TaskProgressTracker> TaskProgressTrackers { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<TimeTrackingEntry> TimeTrackingEntries { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<UserPermission> UserPermissionGrantedByNavigations { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<ActivityLog> ActivityLogs { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<ActivityLog> ActivityLogsChangedBy { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<Dashboard> DashboardsNavigation { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<Message> Messages { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<Task> Tasks { get; set; } = [];

    /// <summary>
    /// رسائل المهام المرسلة من هذا المستخدم
    /// </summary>
    [JsonIgnore]
    public virtual ICollection<TaskMessage> TaskMessagesSent { get; set; } = [];

    /// <summary>
    /// رسائل المهام المثبتة من هذا المستخدم
    /// </summary>
    [JsonIgnore]
    public virtual ICollection<TaskMessage> TaskMessagesPinned { get; set; } = [];

    /// <summary>
    /// رسائل المهام المحددة للمتابعة من هذا المستخدم
    /// </summary>
    [JsonIgnore]
    public virtual ICollection<TaskMessage> TaskMessagesMarkedForFollowUp { get; set; } = [];

    /// <summary>
    /// حالات قراءة رسائل المهام لهذا المستخدم
    /// </summary>
    [JsonIgnore]
    public virtual ICollection<TaskMessageRead> TaskMessageReads { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<CustomRole> CustomRolesCreated { get; set; } = new List<CustomRole>();
    [JsonIgnore]
    public virtual ICollection<UserCustomRole> UserCustomRoles { get; set; } = new List<UserCustomRole>();
    [JsonIgnore]
    public virtual ICollection<CustomRolePermission> CustomRolePermissionsCreated { get; set; } = new List<CustomRolePermission>();

    [JsonIgnore]
    public virtual ICollection<UserPermission> UserPermissions { get; set; } = new List<UserPermission>();

    // لا تستخدم user.Role مباشرة في أي منطق تحقق أو ربط، بل استخدم فقط user.RoleId كمفتاح خارجي
}


