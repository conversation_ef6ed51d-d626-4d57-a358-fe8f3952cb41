using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using webApi.Models;
using webApi.Services;
using Microsoft.AspNetCore.Authorization;

namespace webApi.Controllers
{
    /// <summary>
    /// متحكم إداري موحد ومحسن لإدارة جميع جوانب النظام
    /// يوفر APIs شاملة للمستخدمين والأدوار والصلاحيات والإحصائيات
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    //[Authorize] // يتطلب المصادقة لجميع العمليات الإدارية
    public class AdminController : ControllerBase
    {
        private readonly TasksDbContext _context;
        private readonly UserPermissionService _permissionService;
        private readonly ILogger<AdminController> _logger;

        public AdminController(
            TasksDbContext context, 
            UserPermissionService permissionService,
            ILogger<AdminController> logger)
        {
            _context = context;
            _permissionService = permissionService;
            _logger = logger;
        }

        // ===== إحصائيات النظام =====

        /// <summary>
        /// الحصول على إحصائيات شاملة للنظام
        /// </summary>
        /// <returns>إحصائيات النظام</returns>
        [HttpGet("statistics")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetSystemStatistics()
        {
            try
            {
                var totalUsers = await _context.Users.CountAsync();
                var activeUsers = await _context.Users.CountAsync(u => u.IsActive);
                var totalRoles = await _context.Set<Role>().CountAsync();
                var activeRoles = await _context.Set<Role>().CountAsync(r => r.IsActive);
                var totalPermissions = await _context.Permissions.CountAsync();
                var totalDepartments = await _context.Departments.CountAsync();
                var activeDepartments = await _context.Departments.CountAsync(d => d.IsActive);

                var statistics = new
                {
                    users = new
                    {
                        total = totalUsers,
                        active = activeUsers,
                        inactive = totalUsers - activeUsers
                    },
                    roles = new
                    {
                        total = totalRoles,
                        active = activeRoles,
                        inactive = totalRoles - activeRoles
                    },
                    permissions = new
                    {
                        total = totalPermissions
                    },
                    departments = new
                    {
                        total = totalDepartments,
                        active = activeDepartments,
                        inactive = totalDepartments - activeDepartments
                    },
                    lastUpdated = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
                };

                _logger.LogInformation("تم جلب إحصائيات النظام بنجاح");
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب إحصائيات النظام");
                return StatusCode(500, new { message = "خطأ في جلب إحصائيات النظام", error = ex.Message });
            }
        }

        // ===== إدارة المستخدمين المحسنة =====

        /// <summary>
        /// الحصول على جميع المستخدمين مع معلومات مفصلة (محسن للأداء)
        /// </summary>
        /// <param name="includeInactive">تضمين المستخدمين غير النشطين</param>
        /// <param name="departmentId">فلتر حسب القسم</param>
        /// <param name="roleId">فلتر حسب الدور</param>
        /// <param name="page">رقم الصفحة (افتراضي 1)</param>
        /// <param name="pageSize">حجم الصفحة (افتراضي 50)</param>
        /// <returns>قائمة المستخدمين مع معلومات الصفحات</returns>
        [HttpGet("users")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetAllUsers(
            bool includeInactive = false,
            int? departmentId = null,
            int? roleId = null,
            int page = 1,
            int pageSize = 50)
        {
            try
            {
                var query = _context.Users.AsNoTracking() // تحسين الأداء - عدم تتبع التغييرات
                    .Include(u => u.Role)
                    .Include(u => u.Department)
                    .AsQueryable();

                if (!includeInactive)
                {
                    query = query.Where(u => u.IsActive);
                }

                if (departmentId.HasValue)
                {
                    query = query.Where(u => u.DepartmentId == departmentId.Value);
                }

                if (roleId.HasValue)
                {
                    query = query.Where(u => u.RoleId == roleId.Value);
                }

                // حساب العدد الإجمالي
                var totalCount = await query.CountAsync();

                // تطبيق الصفحات
                var users = await query
                    .OrderBy(u => u.Name)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(u => new // إرجاع البيانات المطلوبة فقط
                    {
                        u.Id,
                        u.Name,
                        u.Email,
                        u.IsActive,
                        u.CreatedAt,
                        u.LastLogin,
                        u.LastSeen,
                        Role = u.Role != null ? new { u.Role.Id, u.Role.Name, u.Role.DisplayName } : null,
                        Department = u.Department != null ? new { u.Department.Id, u.Department.Name } : null
                    })
                    .ToListAsync();

                var result = new
                {
                    Users = users,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize,
                    TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                };

                _logger.LogInformation($"تم جلب {users.Count} مستخدم من أصل {totalCount} (الصفحة {page})");
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب المستخدمين");
                return StatusCode(500, new { message = "خطأ في جلب المستخدمين", error = ex.Message });
            }
        }

        /// <summary>
        /// تحديث حالة نشاط المستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="isActive">الحالة الجديدة</param>
        /// <returns>نتيجة العملية</returns>
        [HttpPut("users/{userId}/status")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult> UpdateUserStatus(int userId, [FromBody] bool isActive)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    return NotFound(new { message = "المستخدم غير موجود" });
                }

                user.IsActive = isActive;
                await _context.SaveChangesAsync();

                _logger.LogInformation($"تم تحديث حالة المستخدم {userId} إلى {(isActive ? "نشط" : "غير نشط")}");
                return Ok(new { message = $"تم تحديث حالة المستخدم إلى {(isActive ? "نشط" : "غير نشط")}" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"خطأ في تحديث حالة المستخدم {userId}");
                return StatusCode(500, new { message = "خطأ في تحديث حالة المستخدم", error = ex.Message });
            }
        }

        // ===== إدارة الأدوار المحسنة =====

        /// <summary>
        /// الحصول على جميع الأدوار مع إحصائيات (محسن للأداء)
        /// </summary>
        /// <param name="includeInactive">تضمين الأدوار غير النشطة</param>
        /// <param name="includeDetails">تضمين التفاصيل الكاملة (المستخدمين والصلاحيات)</param>
        /// <returns>قائمة الأدوار مع الإحصائيات</returns>
        [HttpGet("roles")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetAllRoles(bool includeInactive = false, bool includeDetails = false)
        {
            try
            {
                var query = _context.Set<Role>().AsNoTracking();

                if (!includeInactive)
                {
                    query = query.Where(r => r.IsActive);
                }

                if (includeDetails)
                {
                    // تحميل التفاصيل الكاملة فقط عند الحاجة
                    var rolesWithDetails = await query
                        .Include(r => r.RoleDefaultPermissions)
                        .ThenInclude(rdp => rdp.Permission)
                        .Include(r => r.Users.Where(u => u.IsActive))
                        .OrderBy(r => r.Level)
                        .ThenBy(r => r.Name)
                        .ToListAsync();

                    var detailedResult = rolesWithDetails.Select(r => new
                    {
                        r.Id,
                        r.Name,
                        r.DisplayName,
                        r.Description,
                        r.Level,
                        r.IsSystemRole,
                        r.IsActive,
                        r.CreatedAt,
                        r.UpdatedAt,
                        UsersCount = r.Users?.Count ?? 0,
                        PermissionsCount = r.RoleDefaultPermissions?.Count ?? 0,
                        Users = r.Users?.Select(u => new { u.Id, u.Name, u.Email }).ToList(),
                        Permissions = r.RoleDefaultPermissions?.Select(rdp => new
                        {
                            rdp.Permission.Id,
                            rdp.Permission.Name,
                            rdp.Permission.Description
                        }).ToList()
                    });

                    _logger.LogInformation($"تم جلب {rolesWithDetails.Count} دور مع التفاصيل");
                    return Ok(detailedResult);
                }
                else
                {
                    // تحميل البيانات الأساسية فقط لتحسين الأداء
                    var basicRoles = await query
                        .OrderBy(r => r.Level)
                        .ThenBy(r => r.Name)
                        .Select(r => new
                        {
                            r.Id,
                            r.Name,
                            r.DisplayName,
                            r.Description,
                            r.Level,
                            r.IsSystemRole,
                            r.IsActive,
                            r.CreatedAt,
                            r.UpdatedAt,
                            // حساب العدد بدون تحميل البيانات
                            UsersCount = _context.Users.Count(u => u.RoleId == r.Id && u.IsActive),
                            PermissionsCount = _context.Set<RoleDefaultPermission>().Count(rdp => rdp.RoleId == r.Id)
                        })
                        .ToListAsync();

                    _logger.LogInformation($"تم جلب {basicRoles.Count} دور (بيانات أساسية)");
                    return Ok(basicRoles);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الأدوار");
                return StatusCode(500, new { message = "خطأ في جلب الأدوار", error = ex.Message });
            }
        }

        // ===== إدارة الصلاحيات المحسنة =====

        /// <summary>
        /// الحصول على جميع الصلاحيات مع تجميع حسب المجموعة
        /// </summary>
        /// <returns>الصلاحيات مجمعة حسب المجموعة</returns>
        [HttpGet("permissions")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetAllPermissions()
        {
            try
            {
                var permissions = await _context.Permissions
                    .Include(p => p.Screen)
                    .Include(p => p.Action)
                    .OrderBy(p => p.PermissionGroup)
                    .ThenBy(p => p.Level)
                    .ThenBy(p => p.Name)
                    .ToListAsync();

                var groupedPermissions = permissions
                    .GroupBy(p => p.PermissionGroup)
                    .Select(g => new
                    {
                        Group = g.Key,
                        Count = g.Count(),
                        Permissions = g.Select(p => new
                        {
                            p.Id,
                            p.Name,
                            p.Description,
                            p.Level,
                            p.Category,
                            p.Icon,
                            p.Color,
                            p.IsDefault,
                            Screen = p.Screen != null ? new { p.Screen.Id, p.Screen.Name } : null,
                            Action = p.Action != null ? new { p.Action.Id, p.Action.Name } : null
                        }).ToList()
                    })
                    .ToList();

                _logger.LogInformation($"تم جلب {permissions.Count} صلاحية في {groupedPermissions.Count} مجموعة");
                return Ok(groupedPermissions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الصلاحيات");
                return StatusCode(500, new { message = "خطأ في جلب الصلاحيات", error = ex.Message });
            }
        }

        // ===== إدارة الأقسام =====

        /// <summary>
        /// الحصول على جميع الأقسام مع التسلسل الهرمي
        /// </summary>
        /// <param name="includeInactive">تضمين الأقسام غير النشطة</param>
        /// <returns>الأقسام مع التسلسل الهرمي</returns>
        [HttpGet("departments")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetAllDepartments(bool includeInactive = false)
        {
            try
            {
                var query = _context.Departments
                    .Include(d => d.Manager)
                    .Include(d => d.Parent)
                    .Include(d => d.Children)
                    .Include(d => d.Users)
                    .AsQueryable();

                if (!includeInactive)
                {
                    query = query.Where(d => d.IsActive);
                }

                var departments = await query
                    .OrderBy(d => d.Level)
                    .ThenBy(d => d.SortOrder)
                    .ThenBy(d => d.Name)
                    .ToListAsync();

                var departmentsWithStats = departments.Select(d => new
                {
                    d.Id,
                    d.Name,
                    d.Description,
                    d.ManagerId,
                    d.IsActive,
                    d.CreatedAt,
                    d.ParentId,
                    d.Level,
                    d.SortOrder,
                    Manager = d.Manager != null ? new { d.Manager.Id, d.Manager.Name } : null,
                    Parent = d.Parent != null ? new { d.Parent.Id, d.Parent.Name } : null,
                    UsersCount = d.Users?.Count ?? 0,
                    ChildrenCount = d.Children?.Count ?? 0,
                    Users = d.Users?.Select(u => new { u.Id, u.Name, u.Email }).ToList()
                });

                _logger.LogInformation($"تم جلب {departments.Count} قسم");
                return Ok(departmentsWithStats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الأقسام");
                return StatusCode(500, new { message = "خطأ في جلب الأقسام", error = ex.Message });
            }
        }

        // ===== إدارة النظام والصيانة =====

        /// <summary>
        /// الحصول على معلومات صحة النظام
        /// </summary>
        /// <returns>معلومات صحة النظام</returns>
        [HttpGet("health")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetSystemHealth()
        {
            try
            {
                var dbConnectionHealthy = await _context.Database.CanConnectAsync();
                var totalRecords = await _context.Users.CountAsync() +
                                 await _context.Set<Role>().CountAsync() +
                                 await _context.Permissions.CountAsync();

                var health = new
                {
                    Status = dbConnectionHealthy ? "Healthy" : "Unhealthy",
                    DatabaseConnection = dbConnectionHealthy,
                    TotalRecords = totalRecords,
                    Timestamp = DateTimeOffset.UtcNow,
                    Version = "2.0.0",
                    Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production"
                };

                _logger.LogInformation("تم فحص صحة النظام");
                return Ok(health);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فحص صحة النظام");
                return StatusCode(500, new {
                    Status = "Unhealthy",
                    message = "خطأ في فحص صحة النظام",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// تحديث جميع البيانات (إعادة تحميل)
        /// </summary>
        /// <returns>نتيجة العملية</returns>
        [HttpPost("refresh")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> RefreshSystemData()
        {
            try
            {
                // إعادة تحميل البيانات من قاعدة البيانات
                await _context.Database.ExecuteSqlRawAsync("SELECT 1"); // اختبار الاتصال

                var refreshResult = new
                {
                    Success = true,
                    Message = "تم تحديث البيانات بنجاح",
                    Timestamp = DateTimeOffset.UtcNow,
                    RefreshedTables = new[] { "Users", "Roles", "Permissions", "Departments" }
                };

                _logger.LogInformation("تم تحديث بيانات النظام");
                return Ok(refreshResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث بيانات النظام");
                return StatusCode(500, new {
                    Success = false,
                    message = "خطأ في تحديث بيانات النظام",
                    error = ex.Message
                });
            }
        }

        // ===== تقارير وتحليلات =====

        /// <summary>
        /// الحصول على تقرير نشاط المستخدمين
        /// </summary>
        /// <param name="days">عدد الأيام للتقرير (افتراضي 30)</param>
        /// <returns>تقرير نشاط المستخدمين</returns>
        [HttpGet("reports/user-activity")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetUserActivityReport(int days = 30)
        {
            try
            {
                var cutoffDate = DateTimeOffset.UtcNow.AddDays(-days).ToUnixTimeSeconds();

                var activeUsers = await _context.Users
                    .Where(u => u.IsActive && u.LastSeen.HasValue && u.LastSeen > cutoffDate)
                    .CountAsync();

                var totalUsers = await _context.Users.CountAsync(u => u.IsActive);

                var recentLogins = await _context.Users
                    .Where(u => u.LastLogin.HasValue && u.LastLogin > cutoffDate)
                    .CountAsync();

                var report = new
                {
                    Period = $"آخر {days} يوم",
                    TotalActiveUsers = totalUsers,
                    RecentlyActiveUsers = activeUsers,
                    RecentLogins = recentLogins,
                    ActivityRate = totalUsers > 0 ? (double)activeUsers / totalUsers * 100 : 0,
                    GeneratedAt = DateTimeOffset.UtcNow
                };

                _logger.LogInformation($"تم إنشاء تقرير نشاط المستخدمين لآخر {days} يوم");
                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير نشاط المستخدمين");
                return StatusCode(500, new { message = "خطأ في إنشاء التقرير", error = ex.Message });
            }
        }

        /// <summary>
        /// الحصول على تقرير توزيع الأدوار
        /// </summary>
        /// <returns>تقرير توزيع الأدوار</returns>
        [HttpGet("reports/role-distribution")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetRoleDistributionReport()
        {
            try
            {
                var roleDistribution = await _context.Set<Role>()
                    .Where(r => r.IsActive)
                    .Select(r => new
                    {
                        r.Id,
                        r.Name,
                        r.DisplayName,
                        r.Level,
                        r.IsSystemRole,
                        UsersCount = r.Users.Count(u => u.IsActive)
                    })
                    .OrderByDescending(r => r.UsersCount)
                    .ToListAsync();

                var totalUsers = roleDistribution.Sum(r => r.UsersCount);

                var report = new
                {
                    TotalUsers = totalUsers,
                    TotalRoles = roleDistribution.Count,
                    Distribution = roleDistribution.Select(r => new
                    {
                        r.Id,
                        r.Name,
                        r.DisplayName,
                        r.Level,
                        r.IsSystemRole,
                        r.UsersCount,
                        Percentage = totalUsers > 0 ? (double)r.UsersCount / totalUsers * 100 : 0
                    }),
                    GeneratedAt = DateTimeOffset.UtcNow
                };

                _logger.LogInformation("تم إنشاء تقرير توزيع الأدوار");
                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير توزيع الأدوار");
                return StatusCode(500, new { message = "خطأ في إنشاء التقرير", error = ex.Message });
            }
        }

        // ===== عمليات مجمعة =====

        /// <summary>
        /// تحديث حالة عدة مستخدمين دفعة واحدة
        /// </summary>
        /// <param name="request">طلب التحديث المجمع</param>
        /// <returns>نتيجة العملية</returns>
        [HttpPut("users/bulk-status")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<object>> BulkUpdateUserStatus([FromBody] BulkUpdateRequest request)
        {
            try
            {
                if (request.UserIds == null || !request.UserIds.Any())
                {
                    return BadRequest(new { message = "يجب تحديد معرفات المستخدمين" });
                }

                var users = await _context.Users
                    .Where(u => request.UserIds.Contains(u.Id))
                    .ToListAsync();

                if (!users.Any())
                {
                    return BadRequest(new { message = "لم يتم العثور على المستخدمين المحددين" });
                }

                foreach (var user in users)
                {
                    user.IsActive = request.IsActive;
                }

                await _context.SaveChangesAsync();

                var result = new
                {
                    Success = true,
                    Message = $"تم تحديث حالة {users.Count} مستخدم إلى {(request.IsActive ? "نشط" : "غير نشط")}",
                    UpdatedCount = users.Count,
                    UpdatedUsers = users.Select(u => new { u.Id, u.Name, u.IsActive }).ToList()
                };

                _logger.LogInformation($"تم تحديث حالة {users.Count} مستخدم بشكل مجمع");
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحديث المجمع لحالة المستخدمين");
                return StatusCode(500, new { message = "خطأ في التحديث المجمع", error = ex.Message });
            }
        }
    }

    /// <summary>
    /// نموذج طلب التحديث المجمع
    /// </summary>
    public class BulkUpdateRequest
    {
        public List<int> UserIds { get; set; } = new();
        public bool IsActive { get; set; }
    }
}
