import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
// import 'package:permission_handler/permission_handler.dart'; // غير متوفر
import 'package:file_picker/file_picker.dart';
import 'package:csv/csv.dart';
import 'package:excel/excel.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import '../models/unified_models.dart';
import 'api/unified_api_services.dart';

/// أنواع التصدير المدعومة
enum ExportFormat { csv, excel, pdf, json }

/// أنواع البيانات المدعومة للتصدير
enum DataType { users, departments, tasks, roles, permissions, reports }

/// خدمة التصدير والاستيراد الشاملة
/// تدعم تصدير واستيراد البيانات بصيغ مختلفة
class ExportImportService {
  static final ExportImportService _instance = ExportImportService._internal();
  factory ExportImportService() => _instance;
  ExportImportService._internal();

  final UsersApiService _usersApiService = UsersApiService();
  final DepartmentsApiService _departmentsApiService = DepartmentsApiService();
  final TaskApiService _taskApiService = TaskApiService();



  /// تصدير البيانات بالصيغة المحددة
  Future<String?> exportData({
    required DataType dataType,
    required ExportFormat format,
    Map<String, dynamic>? filters,
  }) async {
    try {
      debugPrint('🔄 بدء تصدير ${dataType.name} بصيغة ${format.name}...');

      // طلب الصلاحيات
      if (!await _requestPermissions()) {
        throw Exception('لا توجد صلاحيات للوصول للتخزين');
      }

      // الحصول على البيانات
      final data = await _fetchData(dataType, filters);
      if (data.isEmpty) {
        throw Exception('لا توجد بيانات للتصدير');
      }

      // تصدير البيانات حسب الصيغة
      String? filePath;
      switch (format) {
        case ExportFormat.csv:
          filePath = await _exportToCsv(dataType, data);
          break;
        case ExportFormat.excel:
          filePath = await _exportToExcel(dataType, data);
          break;
        case ExportFormat.pdf:
          filePath = await _exportToPdf(dataType, data);
          break;
        case ExportFormat.json:
          filePath = await _exportToJson(dataType, data);
          break;
      }

      debugPrint('✅ تم التصدير بنجاح: $filePath');
      return filePath;
    } catch (e) {
      debugPrint('❌ خطأ في التصدير: $e');
      rethrow;
    }
  }

  /// استيراد البيانات من ملف
  Future<Map<String, dynamic>> importData({
    required DataType dataType,
    required String filePath,
  }) async {
    try {
      debugPrint('🔄 بدء استيراد ${dataType.name} من: $filePath');

      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('الملف غير موجود');
      }

      // تحديد نوع الملف
      final extension = filePath.split('.').last.toLowerCase();
      List<Map<String, dynamic>> importedData = [];

      switch (extension) {
        case 'csv':
          importedData = await _importFromCsv(file);
          break;
        case 'xlsx':
        case 'xls':
          importedData = await _importFromExcel(file);
          break;
        case 'json':
          importedData = await _importFromJson(file);
          break;
        default:
          throw Exception('صيغة الملف غير مدعومة: $extension');
      }

      // معالجة البيانات المستوردة
      final result = await _processImportedData(dataType, importedData);

      debugPrint('✅ تم الاستيراد بنجاح: ${result['imported']} عنصر');
      return result;
    } catch (e) {
      debugPrint('❌ خطأ في الاستيراد: $e');
      rethrow;
    }
  }

  /// اختيار ملف للاستيراد
  Future<String?> pickFileForImport() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv', 'xlsx', 'xls', 'json'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        return result.files.first.path;
      }
      return null;
    } catch (e) {
      debugPrint('❌ خطأ في اختيار الملف: $e');
      return null;
    }
  }

  /// طلب صلاحيات التخزين
  Future<bool> _requestPermissions() async {
    if (kIsWeb) return true;

    // في التطبيق الحقيقي، يمكن استخدام permission_handler
    // final status = await Permission.storage.request();
    // return status.isGranted;

    // مؤقتاً نفترض أن الصلاحيات متاحة
    return true;
  }

  /// الحصول على البيانات حسب النوع
  Future<List<Map<String, dynamic>>> _fetchData(
    DataType dataType,
    Map<String, dynamic>? filters,
  ) async {
    switch (dataType) {
      case DataType.users:
        final users = await _usersApiService.getAllUsers();
        return users.map((u) => u.toJson()).toList();

      case DataType.departments:
        final departments = await _departmentsApiService.getAllDepartments();
        return departments.map((d) => d.toJson()).toList();

      case DataType.tasks:
        final tasks = await _taskApiService.getAllTasks();
        return tasks.map((t) => t.toJson()).toList();

      default:
        return [];
    }
  }

  /// تصدير إلى CSV
  Future<String> _exportToCsv(
    DataType dataType,
    List<Map<String, dynamic>> data,
  ) async {
    if (data.isEmpty) throw Exception('لا توجد بيانات للتصدير');

    // إنشاء الرؤوس
    final headers = data.first.keys.toList();
    
    // إنشاء الصفوف
    final rows = <List<dynamic>>[headers];
    for (final item in data) {
      rows.add(headers.map((header) => item[header]?.toString() ?? '').toList());
    }

    // تحويل إلى CSV
    final csv = const ListToCsvConverter().convert(rows);

    // حفظ الملف
    final directory = await getApplicationDocumentsDirectory();
    final fileName = '${dataType.name}_${DateTime.now().millisecondsSinceEpoch}.csv';
    final file = File('${directory.path}/$fileName');
    await file.writeAsString(csv, encoding: utf8);

    return file.path;
  }

  /// تصدير إلى Excel
  Future<String> _exportToExcel(
    DataType dataType,
    List<Map<String, dynamic>> data,
  ) async {
    final excel = Excel.createExcel();
    final sheet = excel['البيانات'];

    if (data.isNotEmpty) {
      // إضافة الرؤوس
      final headers = data.first.keys.toList();
      for (int i = 0; i < headers.length; i++) {
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
            .value = TextCellValue(headers[i]);
      }

      // إضافة البيانات
      for (int rowIndex = 0; rowIndex < data.length; rowIndex++) {
        final item = data[rowIndex];
        for (int colIndex = 0; colIndex < headers.length; colIndex++) {
          final value = item[headers[colIndex]]?.toString() ?? '';
          sheet.cell(CellIndex.indexByColumnRow(
            columnIndex: colIndex,
            rowIndex: rowIndex + 1,
          )).value = TextCellValue(value);
        }
      }
    }

    // حفظ الملف
    final directory = await getApplicationDocumentsDirectory();
    final fileName = '${dataType.name}_${DateTime.now().millisecondsSinceEpoch}.xlsx';
    final file = File('${directory.path}/$fileName');
    await file.writeAsBytes(excel.encode()!);

    return file.path;
  }

  /// تصدير إلى PDF
  Future<String> _exportToPdf(
    DataType dataType,
    List<Map<String, dynamic>> data,
  ) async {
    final pdf = pw.Document();

    // إنشاء جدول البيانات
    if (data.isNotEmpty) {
      final headers = data.first.keys.toList();
      
      pdf.addPage(
        pw.Page(
          build: (pw.Context context) {
            return pw.TableHelper.fromTextArray(
              headers: headers,
              data: data.map((item) => 
                headers.map((header) => item[header]?.toString() ?? '').toList()
              ).toList(),
              headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              cellAlignment: pw.Alignment.centerRight,
            );
          },
        ),
      );
    }

    // حفظ الملف
    final directory = await getApplicationDocumentsDirectory();
    final fileName = '${dataType.name}_${DateTime.now().millisecondsSinceEpoch}.pdf';
    final file = File('${directory.path}/$fileName');
    await file.writeAsBytes(await pdf.save());

    return file.path;
  }

  /// تصدير إلى JSON
  Future<String> _exportToJson(
    DataType dataType,
    List<Map<String, dynamic>> data,
  ) async {
    final jsonData = {
      'dataType': dataType.name,
      'exportDate': DateTime.now().toIso8601String(),
      'count': data.length,
      'data': data,
    };

    final jsonString = const JsonEncoder.withIndent('  ').convert(jsonData);

    // حفظ الملف
    final directory = await getApplicationDocumentsDirectory();
    final fileName = '${dataType.name}_${DateTime.now().millisecondsSinceEpoch}.json';
    final file = File('${directory.path}/$fileName');
    await file.writeAsString(jsonString, encoding: utf8);

    return file.path;
  }

  /// استيراد من CSV
  Future<List<Map<String, dynamic>>> _importFromCsv(File file) async {
    final content = await file.readAsString(encoding: utf8);
    final rows = const CsvToListConverter().convert(content);
    
    if (rows.isEmpty) return [];

    final headers = rows.first.map((e) => e.toString()).toList();
    final data = <Map<String, dynamic>>[];

    for (int i = 1; i < rows.length; i++) {
      final row = rows[i];
      final item = <String, dynamic>{};
      for (int j = 0; j < headers.length && j < row.length; j++) {
        item[headers[j]] = row[j];
      }
      data.add(item);
    }

    return data;
  }

  /// استيراد من Excel
  Future<List<Map<String, dynamic>>> _importFromExcel(File file) async {
    final bytes = await file.readAsBytes();
    final excel = Excel.decodeBytes(bytes);
    
    final data = <Map<String, dynamic>>[];
    
    for (final table in excel.tables.keys) {
      final sheet = excel.tables[table]!;
      if (sheet.rows.isEmpty) continue;

      // الحصول على الرؤوس
      final headers = sheet.rows.first
          .map((cell) => cell?.value?.toString() ?? '')
          .toList();

      // معالجة البيانات
      for (int i = 1; i < sheet.rows.length; i++) {
        final row = sheet.rows[i];
        final item = <String, dynamic>{};
        
        for (int j = 0; j < headers.length && j < row.length; j++) {
          item[headers[j]] = row[j]?.value?.toString() ?? '';
        }
        data.add(item);
      }
      break; // نأخذ أول جدول فقط
    }

    return data;
  }

  /// استيراد من JSON
  Future<List<Map<String, dynamic>>> _importFromJson(File file) async {
    final content = await file.readAsString(encoding: utf8);
    final jsonData = jsonDecode(content);
    
    if (jsonData is Map && jsonData.containsKey('data')) {
      return List<Map<String, dynamic>>.from(jsonData['data']);
    } else if (jsonData is List) {
      return List<Map<String, dynamic>>.from(jsonData);
    }
    
    return [];
  }

  /// معالجة البيانات المستوردة
  Future<Map<String, dynamic>> _processImportedData(
    DataType dataType,
    List<Map<String, dynamic>> data,
  ) async {
    int imported = 0;
    int failed = 0;
    final errors = <String>[];

    for (final item in data) {
      try {
        switch (dataType) {
          case DataType.users:
            // معالجة استيراد المستخدمين
            await _importUser(item);
            break;
          case DataType.departments:
            // معالجة استيراد الأقسام
            await _importDepartment(item);
            break;
          case DataType.tasks:
            // معالجة استيراد المهام
            await _importTask(item);
            break;
          default:
            throw Exception('نوع البيانات غير مدعوم للاستيراد');
        }
        imported++;
      } catch (e) {
        failed++;
        errors.add('خطأ في العنصر ${item.toString()}: $e');
      }
    }

    return {
      'imported': imported,
      'failed': failed,
      'errors': errors,
      'total': data.length,
    };
  }

  /// استيراد مستخدم
  Future<void> _importUser(Map<String, dynamic> data) async {
    // تحويل البيانات إلى نموذج User وحفظها
    // يمكن تخصيص هذا حسب بنية البيانات
    await _usersApiService.createUser(User.fromJson(data));
  }

  /// استيراد قسم
  Future<void> _importDepartment(Map<String, dynamic> data) async {
    // تحويل البيانات إلى نموذج Department وحفظها
    await _departmentsApiService.createDepartment(Department.fromJson(data));
  }

  /// استيراد مهمة
  Future<void> _importTask(Map<String, dynamic> data) async {
    // تحويل البيانات إلى نموذج Task وحفظها
    await _taskApiService.createTask(Task.fromJson(data));
  }
}
