import 'role_model.dart';
import 'package:flutter/foundation.dart';

/// نموذج المستخدم المتوافق مع ASP.NET Core API
class User {
  final int id;
  final String name;
  final String email;
  final String? username;
  final String? password; // لن يتم إرسالها في الاستجابات
  final String? profileImage;
  final int? departmentId;
  final int? roleId; // المفتاح الخارجي الرسمي
  final Role? role; // العلاقة مع نموذج الدور فقط
  final bool isActive;
  final bool isOnline;
  final int createdAt; // Unix timestamp (converted from backend long)
  final int? lastLogin; // Unix timestamp (converted from backend long)
  final int? lastSeen; // Unix timestamp (converted from backend long)
  final String? firstName;
  final String? lastName;
  final bool isDeleted;

  const User({
    required this.id,
    required this.name,
    required this.email,
    this.username,
    this.password,
    this.profileImage,
    this.departmentId,
    this.roleId,
    this.role,
    this.isActive = true,
    this.isOnline = false,
    required this.createdAt,
    this.lastLogin,
    this.lastSeen,
    this.firstName,
    this.lastName,
    this.isDeleted = false,
  });

  /// إنشاء User من JSON (من API)
  factory User.fromJson(Map<String, dynamic> json) {
    T getField<T>(Map<String, dynamic> json, String key,
        {bool isRequired = true, T? defaultValue}) {
      final value = json[key];
      if (value == null) {
        if (isRequired && defaultValue == null) {
          throw FormatException(
              'حقل "$key" مطلوب ولكنه مفقود في User.fromJson');
        }
        return defaultValue as T;
      }
      if (value is T) {
        return value;
      }
      if (T == int && value is num) return value.toInt() as T;
      if (T == double && value is num) return value.toDouble() as T;
      if (T == String && value is num) {
        return value.toString() as T;
      }
      throw FormatException(
          'حقل "$key" يحتوي على نوع غير صحيح: \\${value.runtimeType}, المتوقع $T في User.fromJson');
    }

    int? roleId;
    final roleIdValue = json['roleId'];
    if (roleIdValue != null) {
      if (roleIdValue is int) {
        roleId = roleIdValue;
      } else if (roleIdValue is String) {
        roleId = int.tryParse(roleIdValue);
      }
    }

    // إذا لم يكن roleId متاحاً، استخدم قيمة افتراضية للمستخدمين العاديين
    roleId ??= 1; // دور المستخدم العادي كافتراضي

    Role? role;
    final roleValue = json['role'];
    if (roleValue != null && roleValue is Map<String, dynamic>) {
      try {
        role = Role.fromJson(roleValue);
        // تحديث roleId إذا كان متاحاً من role object
        if (roleId == 1 && role.id != 1) {
          roleId = role.id;
        }
      } catch (e) {
        if (kDebugMode) debugPrint('خطأ في تحليل الدور: $e');
      }
    }
    // إذا كان الدور نص أو رقم، تجاهله في role واستخدم فقط roleId
    // لا تضع UserRole في role أبداً

    int? departmentId;
    final deptIdValue = json['departmentId'];
    if (deptIdValue != null && deptIdValue != "") {
      if (deptIdValue is String) {
        departmentId = int.tryParse(deptIdValue);
        if (departmentId == null && kDebugMode) {
          debugPrint('تحذير: فشل في تحليل departmentId من السلسلة النصية: $deptIdValue');
        }
      } else if (deptIdValue is int) {
        departmentId = deptIdValue;
      } else if (kDebugMode) {
        debugPrint('تحذير: departmentId من نوع غير متوقع: \\${deptIdValue.runtimeType}');
      }
    }

    try {
      return User(
        id: getField<int>(json, 'id'),
        name: getField<String>(json, 'name'),
        email: getField<String>(json, 'email'),
        username: getField<String?>(json, 'username', isRequired: false),
        profileImage: getField<String?>(json, 'profileImage', isRequired: false),
        departmentId: departmentId,
        roleId: roleId,
        role: role,
        isActive: getField<bool>(json, 'isActive', isRequired: false, defaultValue: true),
        isOnline: getField<bool>(json, 'isOnline', isRequired: false, defaultValue: false),
        createdAt: json['createdAt'] != null 
            ? (getField<num>(json, 'createdAt', isRequired: false, defaultValue: 0)).toInt()
            : DateTime.now().millisecondsSinceEpoch ~/ 1000,
        lastLogin: json['lastLogin'] != null
            ? (getField<num>(json, 'lastLogin', isRequired: false, defaultValue: 0)).toInt()
            : null,
        lastSeen: json['lastSeen'] != null
            ? (getField<num>(json, 'lastSeen', isRequired: false, defaultValue: 0)).toInt()
            : null,
        firstName: getField<String?>(json, 'firstName', isRequired: false),
        lastName: getField<String?>(json, 'lastName', isRequired: false),
        isDeleted: getField<bool>(json, 'isDeleted', isRequired: false, defaultValue: false),
      );
    } catch (e) {
      if (kDebugMode) debugPrint('خطأ أثناء إنشاء كائن User: $e. JSON: $json');
      rethrow;
    }
  }

  /// تحويل User إلى JSON (للإرسال إلى API)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'username': username,
      'profileImage': profileImage,
      'departmentId': departmentId,
      'roleId': roleId,
      'role': role?.toJson(),
      'isActive': isActive,
      'isOnline': isOnline,
      'createdAt': createdAt,
      'lastLogin': lastLogin,
      'lastSeen': lastSeen,
      'firstName': firstName,
      'lastName': lastName,
      'isDeleted': isDeleted,
      if (password != null) 'password': password,
    };
  }

  /// إنشاء نسخة محدثة من User
  User copyWith({
    int? id,
    String? name,
    String? email,
    String? username,
    String? password,
    String? profileImage,
    int? departmentId,
    int? roleId,
    Role? role,
    bool? isActive,
    bool? isOnline,
    int? createdAt,
    int? lastLogin,
    int? lastSeen,
    String? firstName,
    String? lastName,
    bool? isDeleted,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      username: username ?? this.username,
      password: password ?? this.password,
      profileImage: profileImage ?? this.profileImage,
      departmentId: departmentId ?? this.departmentId,
      roleId: roleId ?? this.roleId,
      role: role ?? this.role,
      isActive: isActive ?? this.isActive,
      isOnline: isOnline ?? this.isOnline,
      createdAt: createdAt ?? this.createdAt,
      lastLogin: lastLogin ?? this.lastLogin,
      lastSeen: lastSeen ?? this.lastSeen,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// الحصول على اسم الدور
  String get roleName {
    if (role != null) {
      return role!.name;
    }
    // إذا لم يوجد كائن Role، استخدم roleId مع enum UserRole فقط للعرض
    return UserRole.fromInt(roleId ?? 1).displayName;
  }

  /// الحصول على معرف الدور
  int get roleIdValue {
    if (roleId != null) {
      return roleId!;
    }
    if (role != null) {
      return role!.id;
    }
    return 0;
  }

  /// التحقق من كون المستخدم مدير
  bool get isAdmin {
    if (role != null) {
      return role!.name.toLowerCase().contains('admin') || role!.name.toLowerCase().contains('مدير');
    }
    return false;
  }

  @override
  String toString() {
    return 'User(id: $id, name: $name, email: $email, role: $roleName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// تعداد أدوار المستخدمين متوافق مع API
enum UserRole {
  user(1, 'مستخدم'),
  supervisor(2, 'مشرف'),
  manager(3, 'مدير'),
  admin(4, 'مدير عام'),
  superAdmin(5, 'مدير النظام');

  const UserRole(this.value, this.displayName);

  final int value;
  final String displayName;

  /// إنشاء UserRole من قيمة int
  static UserRole fromInt(int value) {
    switch (value) {
      case 1:
        return UserRole.user;
      case 2:
        return UserRole.supervisor;
      case 3:
        return UserRole.manager;
      case 4:
        return UserRole.admin;
      case 5:
        return UserRole.superAdmin;
      default:
        return UserRole.user;
    }
  }

  /// إنشاء UserRole من قيمة String
  static UserRole fromString(String value) {
    switch (value.toLowerCase()) {
      case 'user':
        return UserRole.user;
      case 'supervisor':
        return UserRole.supervisor;
      case 'manager':
        return UserRole.manager;
      case 'admin':
        return UserRole.admin;
      case 'superadmin':
        return UserRole.superAdmin;
      default:
        return UserRole.user;
    }
  }

  /// إنشاء UserRole من نص عربي
  static UserRole fromArabic(String value) {
    switch (value.trim()) {
      case 'مستخدم':
        return UserRole.user;
      case 'مشرف':
        return UserRole.supervisor;
      case 'مدير':
        return UserRole.manager;
      case 'مدير عام':
        return UserRole.admin;
      case 'مدير النظام':
        return UserRole.superAdmin;
      default:
        return UserRole.user;
    }
  }

  /// التحقق من كون الدور إداري
  bool get isAdmin => this == UserRole.admin || this == UserRole.superAdmin;

  /// التحقق من كون الدور مدير نظام
  bool get isSuperAdmin => this == UserRole.superAdmin;

  /// التحقق من كون الدور مدير قسم أو أعلى
  bool get isManagerOrAbove =>
      this == UserRole.manager ||
      this == UserRole.admin ||
      this == UserRole.superAdmin;
}

/// معلومات المستخدم المختصرة (من AuthResponse)
class UserInfo {
  final int id;
  final String name;
  final String email;
  final String? username;
  final UserRole role;
  final String roleName;
  final int? departmentId;
  final String? departmentName;
  final String? profileImage;
  final bool isActive;
  final bool isOnline;

  const UserInfo({
    required this.id,
    required this.name,
    required this.email,
    this.username,
    required this.role,
    required this.roleName,
    this.departmentId,
    this.departmentName,
    this.profileImage,
    this.isActive = true,
    this.isOnline = false,
  });

  factory UserInfo.fromJson(Map<String, dynamic> json) {
    try {
      UserRole role;
      if (json['role'] is String) {
        role = UserRole.fromString(json['role'] as String);
      } else if (json['role'] is int) {
        role = UserRole.fromInt(json['role'] as int);
      } else {
        role = UserRole.user;
      }

      int? departmentId;
      if (json['departmentId'] != null && json['departmentId'] != "") {
        if (json['departmentId'] is String) {
          departmentId = int.tryParse(json['departmentId'] as String);
        } else if (json['departmentId'] is int) {
          departmentId = json['departmentId'] as int;
        }
      }

      return UserInfo(
        id: json['id'] as int? ?? 0,
        name: json['name'] as String? ?? '',
        email: json['email'] as String? ?? '',
        username: json['username'] as String?,
        role: role,
        roleName: json['roleName'] as String? ?? role.displayName,
        departmentId: departmentId,
        departmentName: json['departmentName'] as String?,
        profileImage: json['profileImage'] as String?,
        isActive: json['isActive'] as bool? ?? true,
        isOnline: json['isOnline'] as bool? ?? false,
      );
    } catch (e) {
      throw Exception('خطأ في تحليل UserInfo: $e');
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'username': username,
      'role': role.value,
      'roleName': roleName,
      'departmentId': departmentId,
      'departmentName': departmentName,
      'profileImage': profileImage,
      'isActive': isActive,
      'isOnline': isOnline,
    };
  }

  /// تحويل إلى User كامل
  User toUser() {
    return User(
      id: id,
      name: name,
      email: email,
      username: username,
      roleId: role.value, // استخدم roleId فقط
      // role: role, // لا تمرر UserRole إلى Role
      profileImage: profileImage,
      departmentId: departmentId,
      isActive: isActive,
      isOnline: isOnline,
      createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
    );
  }
}
