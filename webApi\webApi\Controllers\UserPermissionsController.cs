﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webApi.Models;

namespace webApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class UserPermissionsController : ControllerBase
    {
        private readonly TasksDbContext _context;

        public UserPermissionsController(TasksDbContext context)
        {
            _context = context;
        }

        // ملاحظة: جميع العمليات أدناه تدعم ربط الصلاحيات المباشرة بالمستخدمين بشكل ديناميكي واحترافي

        // GET: api/UserPermissions
        // جلب جميع الصلاحيات المباشرة للمستخدمين
        [HttpGet]
        public async Task<ActionResult<IEnumerable<UserPermission>>> GetUserPermissions()
        {
            return await _context.UserPermissions
                .Include(up => up.User)
                .Include(up => up.Permission)
                .ToListAsync();
        }

        // GET: api/UserPermissions/5
        // جلب صلاحية مباشرة محددة
        [HttpGet("{id}")]
        public async Task<ActionResult<UserPermission>> GetUserPermission(int id)
        {
            var userPermission = await _context.UserPermissions
                .Include(up => up.User)
                .Include(up => up.Permission)
                .FirstOrDefaultAsync(up => up.Id == id);
            if (userPermission == null)
            {
                return NotFound();
            }
            return userPermission;
        }

        // GET: api/UserPermissions/user/{userId}
        // جلب جميع الصلاحيات المخصصة لمستخدم محدد
        [HttpGet("user/{userId}")]
        public async Task<ActionResult<IEnumerable<UserPermission>>> GetCustomUserPermissions(int userId)
        {
            var userPermissions = await _context.UserPermissions
                .Where(up => up.UserId == userId && up.IsActive && !up.IsDeleted)
                .Include(up => up.User)
                .Include(up => up.Permission)
                .OrderBy(up => up.Permission.Name)
                .ToListAsync();

            return userPermissions;
        }

        // POST: api/UserPermissions
        // إضافة صلاحية مباشرة لمستخدم
        [HttpPost]
        public async Task<ActionResult<UserPermission>> PostUserPermission(UserPermission userPermission)
        {
            _context.UserPermissions.Add(userPermission);
            await _context.SaveChangesAsync();
            return CreatedAtAction("GetUserPermission", new { id = userPermission.Id }, userPermission);
        }

        // PUT: api/UserPermissions/5
        // تحديث صلاحية مباشرة لمستخدم
        [HttpPut("{id}")]
        public async Task<IActionResult> PutUserPermission(int id, UserPermission userPermission)
        {
            if (id != userPermission.Id)
            {
                return BadRequest();
            }
            _context.Entry(userPermission).State = EntityState.Modified;
            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!UserPermissionExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            return NoContent();
        }

        // DELETE: api/UserPermissions/5
        // حذف صلاحية مباشرة من مستخدم
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteUserPermission(int id)
        {
            var userPermission = await _context.UserPermissions.FindAsync(id);
            if (userPermission == null)
            {
                return NotFound();
            }
            _context.UserPermissions.Remove(userPermission);
            await _context.SaveChangesAsync();
            return NoContent();
        }

        private bool UserPermissionExists(int id)
        {
            return _context.UserPermissions.Any(e => e.Id == id);
        }
    }
}
