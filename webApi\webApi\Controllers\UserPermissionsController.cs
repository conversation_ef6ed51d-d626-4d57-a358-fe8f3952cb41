﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webApi.Models;

namespace webApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class UserPermissionsController : ControllerBase
    {
        private readonly TasksDbContext _context;

        public UserPermissionsController(TasksDbContext context)
        {
            _context = context;
        }

        // ملاحظة: جميع العمليات أدناه تدعم ربط الصلاحيات المباشرة بالمستخدمين بشكل ديناميكي واحترافي

        // GET: api/UserPermissions
        // جلب جميع الصلاحيات المباشرة للمستخدمين
        [HttpGet]
        public async Task<ActionResult<IEnumerable<UserPermission>>> GetUserPermissions()
        {
            return await _context.UserPermissions
                .Include(up => up.User)
                .Include(up => up.Permission)
                .ToListAsync();
        }

        // GET: api/UserPermissions/5
        // جلب صلاحية مباشرة محددة
        [HttpGet("{id}")]
        public async Task<ActionResult<UserPermission>> GetUserPermission(int id)
        {
            var userPermission = await _context.UserPermissions
                .Include(up => up.User)
                .Include(up => up.Permission)
                .FirstOrDefaultAsync(up => up.Id == id);
            if (userPermission == null)
            {
                return NotFound();
            }
            return userPermission;
        }

        // GET: api/UserPermissions/user/{userId}
        // جلب جميع الصلاحيات المخصصة لمستخدم محدد
        [HttpGet("user/{userId}")]
        public async Task<ActionResult<IEnumerable<UserPermission>>> GetCustomUserPermissions(int userId)
        {
            var userPermissions = await _context.UserPermissions
                .Where(up => up.UserId == userId && up.IsActive && !up.IsDeleted)
                .Include(up => up.User)
                .Include(up => up.Permission)
                .OrderBy(up => up.Permission.Name)
                .ToListAsync();

            return userPermissions;
        }

        // POST: api/UserPermissions
        // إضافة صلاحية مباشرة لمستخدم
        [HttpPost]
        public async Task<ActionResult<UserPermission>> PostUserPermission(UserPermission userPermission)
        {
            try
            {
                // تسجيل البيانات المستلمة للتشخيص
                Console.WriteLine($"🔍 محاولة إضافة صلاحية: UserId={userPermission.UserId}, PermissionId={userPermission.PermissionId}, GrantedBy={userPermission.GrantedBy}");

                // التحقق من صحة البيانات
                if (userPermission.UserId <= 0)
                {
                    return BadRequest(new { message = "معرف المستخدم غير صحيح", field = "UserId" });
                }

                if (userPermission.PermissionId <= 0)
                {
                    return BadRequest(new { message = "معرف الصلاحية غير صحيح", field = "PermissionId" });
                }

                if (userPermission.GrantedBy <= 0)
                {
                    return BadRequest(new { message = "معرف المانح غير صحيح", field = "GrantedBy" });
                }

                // التحقق من وجود المستخدم
                var userExists = await _context.Users.AnyAsync(u => u.Id == userPermission.UserId);
                if (!userExists)
                {
                    return BadRequest(new { message = "المستخدم غير موجود", field = "UserId" });
                }

                // التحقق من وجود الصلاحية
                var permissionExists = await _context.Permissions.AnyAsync(p => p.Id == userPermission.PermissionId);
                if (!permissionExists)
                {
                    return BadRequest(new { message = "الصلاحية غير موجودة", field = "PermissionId" });
                }

                // التحقق من وجود المانح
                var granterExists = await _context.Users.AnyAsync(u => u.Id == userPermission.GrantedBy);
                if (!granterExists)
                {
                    return BadRequest(new { message = "المانح غير موجود", field = "GrantedBy" });
                }

                // التحقق من وجود الصلاحية مسبقاً
                var existingPermission = await _context.UserPermissions
                    .FirstOrDefaultAsync(up => up.UserId == userPermission.UserId &&
                                              up.PermissionId == userPermission.PermissionId);

                if (existingPermission != null)
                {
                    // إذا كانت الصلاحية موجودة ولكن محذوفة أو غير نشطة، قم بتحديثها
                    if (existingPermission.IsDeleted || !existingPermission.IsActive)
                    {
                        existingPermission.IsActive = userPermission.IsActive;
                        existingPermission.IsDeleted = false;
                        existingPermission.GrantedBy = userPermission.GrantedBy;
                        existingPermission.GrantedAt = userPermission.GrantedAt;
                        existingPermission.ExpiresAt = userPermission.ExpiresAt;

                        await _context.SaveChangesAsync();
                        Console.WriteLine($"✅ تم تحديث الصلاحية الموجودة: {existingPermission.Id}");
                        return Ok(existingPermission);
                    }
                    else
                    {
                        // الصلاحية موجودة ونشطة
                        Console.WriteLine($"⚠️ الصلاحية موجودة ونشطة بالفعل");
                        return Conflict(new { message = "الصلاحية موجودة بالفعل للمستخدم" });
                    }
                }

                // إضافة الصلاحية الجديدة
                Console.WriteLine($"➕ إضافة صلاحية جديدة...");
                _context.UserPermissions.Add(userPermission);
                await _context.SaveChangesAsync();
                Console.WriteLine($"✅ تم إضافة الصلاحية بنجاح: {userPermission.Id}");
                return CreatedAtAction("GetUserPermission", new { id = userPermission.Id }, userPermission);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في إضافة الصلاحية: {ex.Message}");
                Console.WriteLine($"❌ تفاصيل الخطأ: {ex.InnerException?.Message}");
                return BadRequest(new { message = "خطأ في إضافة الصلاحية", error = ex.Message, innerError = ex.InnerException?.Message });
            }
        }

        // PUT: api/UserPermissions/5
        // تحديث صلاحية مباشرة لمستخدم
        [HttpPut("{id}")]
        public async Task<IActionResult> PutUserPermission(int id, UserPermission userPermission)
        {
            if (id != userPermission.Id)
            {
                return BadRequest();
            }
            _context.Entry(userPermission).State = EntityState.Modified;
            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!UserPermissionExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            return NoContent();
        }

        // DELETE: api/UserPermissions/5
        // حذف صلاحية مباشرة من مستخدم
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteUserPermission(int id)
        {
            var userPermission = await _context.UserPermissions.FindAsync(id);
            if (userPermission == null)
            {
                return NotFound();
            }
            _context.UserPermissions.Remove(userPermission);
            await _context.SaveChangesAsync();
            return NoContent();
        }

        private bool UserPermissionExists(int id)
        {
            return _context.UserPermissions.Any(e => e.Id == id);
        }
    }
}
