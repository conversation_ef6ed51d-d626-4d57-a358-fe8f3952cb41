using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webApi.Models;

namespace webApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class UserPermissionsController : ControllerBase
    {
        private readonly TasksDbContext _context;

        public UserPermissionsController(TasksDbContext context)
        {
            _context = context;
        }

        // ملاحظة: جميع العمليات أدناه تدعم ربط الصلاحيات المباشرة بالمستخدمين بشكل ديناميكي واحترافي

        // GET: api/UserPermissions/test
        // اختبار قاعدة البيانات
        [HttpGet("test")]
        public async Task<ActionResult<object>> TestDatabase()
        {
            try
            {
                Console.WriteLine("🔍 اختبار الاتصال بقاعدة البيانات");

                // اختبار جدول user_permissions
                var userPermissionsCount = await _context.UserPermissions.CountAsync();
                Console.WriteLine($"📊 عدد السجلات في user_permissions: {userPermissionsCount}");

                // اختبار جدول permissions
                var permissionsCount = await _context.Permissions.CountAsync();
                Console.WriteLine($"📊 عدد السجلات في permissions: {permissionsCount}");

                // اختبار جدول users
                var usersCount = await _context.Users.CountAsync();
                Console.WriteLine($"📊 عدد السجلات في users: {usersCount}");

                // جلب أول 5 سجلات من user_permissions
                var sampleData = await _context.UserPermissions.Take(5).ToListAsync();
                Console.WriteLine($"📊 عينة من البيانات: {sampleData.Count} سجل");

                return Ok(new {
                    UserPermissionsCount = userPermissionsCount,
                    PermissionsCount = permissionsCount,
                    UsersCount = usersCount,
                    SampleData = sampleData.Select(up => new {
                        up.Id,
                        up.UserId,
                        up.PermissionId,
                        up.IsActive,
                        up.IsDeleted
                    })
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار قاعدة البيانات: {ex.Message}");
                return BadRequest($"خطأ: {ex.Message}");
            }
        }

        // GET: api/UserPermissions/test-add/{userId}/{permissionId}
        // اختبار إضافة صلاحية جديدة لمستخدم محدد
        [HttpGet("test-add/{userId}/{permissionId}")]
        public async Task<ActionResult<object>> TestAddPermission(int userId, int permissionId)
        {
            try
            {
                Console.WriteLine($"🚀 اختبار إضافة صلاحية جديدة للمستخدم {userId} - الصلاحية {permissionId}");

                // إنشاء صلاحية جديدة للاختبار
                var newPermission = new UserPermission
                {
                    UserId = userId,
                    PermissionId = permissionId,
                    GrantedBy = 21, // المستخدم الحالي
                    GrantedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    IsActive = true,
                    IsDeleted = false,
                    ExpiresAt = null
                };

                Console.WriteLine($"🔍 محاولة إضافة صلاحية: UserId={newPermission.UserId}, PermissionId={newPermission.PermissionId}, GrantedBy={newPermission.GrantedBy}");

                // التحقق من وجود المستخدم
                var userExists = await _context.Users.AnyAsync(u => u.Id == newPermission.UserId);
                Console.WriteLine($"👤 المستخدم {userId} موجود: {userExists}");

                // التحقق من وجود الصلاحية
                var permissionExists = await _context.Permissions.AnyAsync(p => p.Id == newPermission.PermissionId);
                Console.WriteLine($"🔑 الصلاحية {permissionId} موجودة: {permissionExists}");

                // التحقق من عدم وجود الصلاحية مسبقاً
                var alreadyExists = await _context.UserPermissions
                    .AnyAsync(up => up.UserId == newPermission.UserId &&
                                   up.PermissionId == newPermission.PermissionId &&
                                   !up.IsDeleted);
                Console.WriteLine($"🔄 الصلاحية موجودة مسبقاً: {alreadyExists}");

                if (!userExists)
                {
                    Console.WriteLine($"❌ المستخدم {newPermission.UserId} غير موجود");
                    return BadRequest($"المستخدم {newPermission.UserId} غير موجود");
                }

                if (!permissionExists)
                {
                    Console.WriteLine($"❌ الصلاحية {newPermission.PermissionId} غير موجودة");
                    return BadRequest($"الصلاحية {newPermission.PermissionId} غير موجودة");
                }

                if (alreadyExists)
                {
                    Console.WriteLine($"⚠️ الصلاحية {newPermission.PermissionId} موجودة مسبقاً للمستخدم {newPermission.UserId}");
                    return BadRequest($"الصلاحية {newPermission.PermissionId} موجودة مسبقاً للمستخدم {newPermission.UserId}");
                }

                // إضافة الصلاحية
                Console.WriteLine($"➕ إضافة الصلاحية إلى قاعدة البيانات...");
                _context.UserPermissions.Add(newPermission);
                var result = await _context.SaveChangesAsync();
                Console.WriteLine($"✅ تم حفظ {result} سجل في قاعدة البيانات");

                // جلب الصلاحية المضافة مع التفاصيل
                var addedPermission = await _context.UserPermissions
                    .Include(up => up.User)
                    .Include(up => up.Permission)
                    .Include(up => up.GrantedByNavigation)
                    .FirstOrDefaultAsync(up => up.Id == newPermission.Id);

                Console.WriteLine($"🎉 تم إضافة الصلاحية بنجاح - ID: {addedPermission?.Id}");
                Console.WriteLine($"📋 تفاصيل الصلاحية المضافة:");
                Console.WriteLine($"   - اسم الصلاحية: {addedPermission?.Permission?.Name}");
                Console.WriteLine($"   - اسم المستخدم: {addedPermission?.User?.Name}");
                Console.WriteLine($"   - تاريخ الإضافة: {DateTimeOffset.FromUnixTimeSeconds(addedPermission?.GrantedAt ?? 0)}");

                return Ok(new {
                    Success = true,
                    Message = "تم إضافة الصلاحية بنجاح",
                    AddedPermission = new {
                        Id = addedPermission?.Id,
                        UserId = addedPermission?.UserId,
                        UserName = addedPermission?.User?.Name,
                        PermissionId = addedPermission?.PermissionId,
                        PermissionName = addedPermission?.Permission?.Name,
                        GrantedAt = addedPermission?.GrantedAt,
                        IsActive = addedPermission?.IsActive
                    }
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في إضافة الصلاحية: {ex.Message}");
                Console.WriteLine($"📋 تفاصيل الخطأ: {ex.StackTrace}");
                return BadRequest($"خطأ: {ex.Message}");
            }
        }

        // GET: api/UserPermissions
        // جلب جميع الصلاحيات المباشرة للمستخدمين
        [HttpGet]
        public async Task<ActionResult<IEnumerable<UserPermission>>> GetUserPermissions()
        {
            Console.WriteLine("🔍 طلب جلب جميع الصلاحيات المخصصة");

            var allPermissions = await _context.UserPermissions.ToListAsync();
            Console.WriteLine($"📊 إجمالي الصلاحيات في الجدول: {allPermissions.Count}");

            var result = await _context.UserPermissions
                .Include(up => up.User)
                .Include(up => up.Permission)
                .ToListAsync();

            Console.WriteLine($"✅ تم إرجاع {result.Count} صلاحية مع التفاصيل");
            return result;
        }

        // GET: api/UserPermissions/5
        // جلب صلاحية مباشرة محددة
        [HttpGet("{id}")]
        public async Task<ActionResult<UserPermission>> GetUserPermission(int id)
        {
            var userPermission = await _context.UserPermissions
                .Include(up => up.User)
                .Include(up => up.Permission)
                .FirstOrDefaultAsync(up => up.Id == id);
            if (userPermission == null)
            {
                return NotFound();
            }
            return userPermission;
        }

        // GET: api/UserPermissions/user/{userId}
        // جلب جميع الصلاحيات المخصصة لمستخدم محدد
        [HttpGet("user/{userId}")]
        public async Task<ActionResult<IEnumerable<UserPermission>>> GetCustomUserPermissions(int userId)
        {
            Console.WriteLine($"🔍 طلب جلب صلاحيات المستخدم: {userId}");

            // أولاً: جلب جميع الصلاحيات للمستخدم بدون شروط للتشخيص
            var allUserPermissions = await _context.UserPermissions
                .Where(up => up.UserId == userId)
                .ToListAsync();

            Console.WriteLine($"📊 إجمالي الصلاحيات للمستخدم {userId}: {allUserPermissions.Count}");

            foreach (var up in allUserPermissions)
            {
                Console.WriteLine($"   - الصلاحية {up.PermissionId}: IsActive={up.IsActive}, IsDeleted={up.IsDeleted}");
            }

            // ثانياً: جلب الصلاحيات النشطة وغير المحذوفة
            var userPermissions = await _context.UserPermissions
                .Where(up => up.UserId == userId && up.IsActive && !up.IsDeleted)
                .Include(up => up.User)
                .Include(up => up.Permission)
                .OrderBy(up => up.Permission.Name)
                .ToListAsync();

            Console.WriteLine($"✅ الصلاحيات النشطة للمستخدم {userId}: {userPermissions.Count}");
            return userPermissions;
        }

        // POST: api/UserPermissions
        // إضافة صلاحية مباشرة لمستخدم
        [HttpPost]
        public async Task<ActionResult<UserPermission>> PostUserPermission([FromBody] UserPermission userPermission)
        {
            Console.WriteLine($"🚀 POST UserPermissions تم استلام الطلب");
            Console.WriteLine($"🔍 البيانات المستلمة: {userPermission?.UserId}, {userPermission?.PermissionId}");

            try
            {
                // تسجيل البيانات المستلمة للتشخيص
                Console.WriteLine($"🔍 محاولة إضافة صلاحية: UserId={userPermission.UserId}, PermissionId={userPermission.PermissionId}, GrantedBy={userPermission.GrantedBy}");

                // التحقق من صحة البيانات
                if (userPermission.UserId <= 0)
                {
                    return BadRequest(new { message = "معرف المستخدم غير صحيح", field = "UserId" });
                }

                if (userPermission.PermissionId <= 0)
                {
                    return BadRequest(new { message = "معرف الصلاحية غير صحيح", field = "PermissionId" });
                }

                if (userPermission.GrantedBy <= 0)
                {
                    return BadRequest(new { message = "معرف المانح غير صحيح", field = "GrantedBy" });
                }

                // التحقق من وجود المستخدم
                var userExists = await _context.Users.AnyAsync(u => u.Id == userPermission.UserId);
                if (!userExists)
                {
                    return BadRequest(new { message = "المستخدم غير موجود", field = "UserId" });
                }

                // التحقق من وجود الصلاحية
                var permissionExists = await _context.Permissions.AnyAsync(p => p.Id == userPermission.PermissionId);
                if (!permissionExists)
                {
                    return BadRequest(new { message = "الصلاحية غير موجودة", field = "PermissionId" });
                }

                // التحقق من وجود المانح
                var granterExists = await _context.Users.AnyAsync(u => u.Id == userPermission.GrantedBy);
                if (!granterExists)
                {
                    return BadRequest(new { message = "المانح غير موجود", field = "GrantedBy" });
                }

                // التحقق من وجود الصلاحية مسبقاً
                var existingPermission = await _context.UserPermissions
                    .FirstOrDefaultAsync(up => up.UserId == userPermission.UserId &&
                                              up.PermissionId == userPermission.PermissionId);

                if (existingPermission != null)
                {
                    // إذا كانت الصلاحية موجودة ولكن محذوفة أو غير نشطة، قم بتحديثها
                    if (existingPermission.IsDeleted || !existingPermission.IsActive)
                    {
                        existingPermission.IsActive = userPermission.IsActive;
                        existingPermission.IsDeleted = false;
                        existingPermission.GrantedBy = userPermission.GrantedBy;
                        existingPermission.GrantedAt = userPermission.GrantedAt;
                        existingPermission.ExpiresAt = userPermission.ExpiresAt;

                        await _context.SaveChangesAsync();
                        Console.WriteLine($"✅ تم تحديث الصلاحية الموجودة: {existingPermission.Id}");
                        return Ok(existingPermission);
                    }
                    else
                    {
                        // الصلاحية موجودة ونشطة
                        Console.WriteLine($"⚠️ الصلاحية موجودة ونشطة بالفعل");
                        return Conflict(new { message = "الصلاحية موجودة بالفعل للمستخدم" });
                    }
                }

                // إضافة الصلاحية الجديدة
                Console.WriteLine($"➕ إضافة صلاحية جديدة...");
                _context.UserPermissions.Add(userPermission);
                await _context.SaveChangesAsync();
                Console.WriteLine($"✅ تم إضافة الصلاحية بنجاح: {userPermission.Id}");
                return CreatedAtAction("GetUserPermission", new { id = userPermission.Id }, userPermission);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في إضافة الصلاحية: {ex.Message}");
                Console.WriteLine($"❌ تفاصيل الخطأ: {ex.InnerException?.Message}");
                return BadRequest(new { message = "خطأ في إضافة الصلاحية", error = ex.Message, innerError = ex.InnerException?.Message });
            }
        }

        // PUT: api/UserPermissions/5
        // تحديث صلاحية مباشرة لمستخدم
        [HttpPut("{id}")]
        public async Task<IActionResult> PutUserPermission(int id, UserPermission userPermission)
        {
            if (id != userPermission.Id)
            {
                return BadRequest();
            }
            _context.Entry(userPermission).State = EntityState.Modified;
            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!UserPermissionExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            return NoContent();
        }

        // DELETE: api/UserPermissions/5
        // حذف صلاحية مباشرة من مستخدم
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteUserPermission(int id)
        {
            var userPermission = await _context.UserPermissions.FindAsync(id);
            if (userPermission == null)
            {
                return NotFound();
            }
            _context.UserPermissions.Remove(userPermission);
            await _context.SaveChangesAsync();
            return NoContent();
        }

        private bool UserPermissionExists(int id)
        {
            return _context.UserPermissions.Any(e => e.Id == id);
        }
    }
}
