import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../controllers/admin_controller.dart';
import '../../../models/user_model.dart';
import '../../../models/permission_models.dart';
import '../../../models/user_permission_model.dart';
import '../../../services/api/permissions_api_service.dart';
import '../../../services/unified_permission_service.dart';
import '../shared/admin_dialog_widget.dart';

/// حوار إدارة صلاحيات المستخدم
class UserPermissionsDialog extends StatefulWidget {
  final User user;

  const UserPermissionsDialog({super.key, required this.user});

  @override
  State<UserPermissionsDialog> createState() => _UserPermissionsDialogState();
}

class _UserPermissionsDialogState extends State<UserPermissionsDialog> {
  final AdminController _adminController = Get.find<AdminController>();
  final PermissionsApiService _permissionsApiService = PermissionsApiService();
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();
  
  final RxBool _isLoading = false.obs;
  final RxBool _isSaving = false.obs;
  final RxMap<int, bool> _userPermissions = <int, bool>{}.obs;
  final RxList<UserPermission> _customPermissions = <UserPermission>[].obs;

  @override
  void initState() {
    super.initState();
    _loadUserPermissions();
  }

  /// تحميل صلاحيات المستخدم
  Future<void> _loadUserPermissions() async {
    _isLoading.value = true;
    try {
      // تحميل الصلاحيات المخصصة للمستخدم
      final customPermissions = await _permissionsApiService.getCustomUserPermissions(widget.user.id);
      _customPermissions.assignAll(customPermissions);
      
      // تحويل الصلاحيات المخصصة إلى خريطة
      for (final customPerm in customPermissions) {
        _userPermissions[customPerm.permissionId] = customPerm.isActive;
      }
      
      debugPrint('✅ تم تحميل ${customPermissions.length} صلاحية مخصصة للمستخدم ${widget.user.name}');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل صلاحيات المستخدم: $e');
      AdminMessageDialog.showError(
        title: 'خطأ',
        message: 'فشل في تحميل صلاحيات المستخدم: $e',
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// حفظ تغييرات الصلاحيات
  Future<void> _savePermissions() async {
    _isSaving.value = true;
    try {
      // حذف جميع الصلاحيات المخصصة الحالية
      for (final customPerm in _customPermissions) {
        await _permissionsApiService.deleteCustomUserPermission(customPerm.id);
      }
      
      // إضافة الصلاحيات الجديدة
      for (final entry in _userPermissions.entries) {
        final userPermission = UserPermission(
          id: 0, // سيتم تعيينه من الخادم
          userId: widget.user.id,
          permissionId: entry.key,
          isActive: entry.value,
          grantedAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
          grantedBy: 1, // TODO: استخدام معرف المستخدم الحالي
        );
        
        await _permissionsApiService.addCustomUserPermission(userPermission);
      }
      
      AdminMessageDialog.showSuccess(
        title: 'نجح العملية',
        message: 'تم حفظ صلاحيات المستخدم بنجاح',
      );
      
      Get.back(result: true);
    } catch (e) {
      debugPrint('❌ خطأ في حفظ الصلاحيات: $e');
      AdminMessageDialog.showError(
        title: 'خطأ',
        message: 'فشل في حفظ صلاحيات المستخدم: $e',
      );
    } finally {
      _isSaving.value = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 600,
        height: 700,
        child: Column(
          children: [
            // رأس الحوار
            _buildDialogHeader(),
            
            // محتوى الحوار
            Expanded(
              child: Obx(() {
                if (_isLoading.value) {
                  return const Center(child: CircularProgressIndicator());
                }
                
                return _buildPermissionsList();
              }),
            ),
            
            // أزرار الحوار
            _buildDialogActions(),
          ],
        ),
      ),
    );
  }

  /// بناء رأس الحوار
  Widget _buildDialogHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      child: Row(
        children: [
          const Icon(Icons.security, color: Colors.white),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إدارة صلاحيات المستخدم',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  widget.user.name,
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Get.back(),
            icon: const Icon(Icons.close, color: Colors.white),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة الصلاحيات
  Widget _buildPermissionsList() {
    return Column(
      children: [
        // معلومات المستخدم
        _buildUserInfo(),
        
        const SizedBox(height: 16),
        
        // قائمة الصلاحيات
        Expanded(
          child: Obx(() {
            final permissions = _adminController.permissions;
            if (permissions.isEmpty) {
              return const Center(
                child: Text('لا توجد صلاحيات متاحة'),
              );
            }

            return ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              itemCount: permissions.length,
              itemBuilder: (context, index) {
                final permission = permissions[index];
                return _buildPermissionTile(permission);
              },
            );
          }),
        ),
      ],
    );
  }

  /// بناء معلومات المستخدم
  Widget _buildUserInfo() {
    return Container(
      margin: const EdgeInsets.all(24),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 25,
            backgroundColor: Theme.of(context).primaryColor,
            child: Text(
              widget.user.name.substring(0, 1),
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.user.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Text(
                  widget.user.email,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
                if (widget.user.role != null)
                  Text(
                    'الدور: ${widget.user.role!.displayName}',
                    style: TextStyle(
                      color: Colors.blue[600],
                      fontSize: 12,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر الصلاحية
  Widget _buildPermissionTile(Permission permission) {
    return Obx(() {
      final hasPermission = _userPermissions[permission.id] ?? false;
      
      return Card(
        margin: const EdgeInsets.only(bottom: 8),
        child: CheckboxListTile(
          title: Text(
            permission.name,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          subtitle: permission.description != null
            ? Text(permission.description!)
            : null,
          secondary: permission.icon != null
            ? Icon(IconData(
                int.tryParse(permission.icon!) ?? Icons.security.codePoint,
                fontFamily: 'MaterialIcons',
              ))
            : const Icon(Icons.security),
          value: hasPermission,
          onChanged: _permissionService.canManagePermissions()
            ? (value) {
                setState(() {
                  _userPermissions[permission.id] = value ?? false;
                });
              }
            : null,
          activeColor: Theme.of(context).primaryColor,
        ),
      );
    });
  }

  /// بناء أزرار الحوار
  Widget _buildDialogActions() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          const SizedBox(width: 16),
          Obx(() => ElevatedButton(
            onPressed: _isSaving.value || !_permissionService.canManagePermissions()
              ? null
              : _savePermissions,
            child: _isSaving.value
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('حفظ التغييرات'),
          )),
        ],
      ),
    );
  }
}
