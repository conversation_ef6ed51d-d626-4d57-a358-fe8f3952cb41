using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webApi.Models;
using webApi.Services;

namespace webApi.Controllers
{
    /// <summary>
    /// Controller for managing system permissions
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    public class PermissionsController : ControllerBase
    {
        private readonly TasksDbContext _context;
        private readonly UserPermissionService _permissionService;

        public PermissionsController(TasksDbContext context, UserPermissionService permissionService)
        {
            _context = context;
            _permissionService = permissionService;
        }

        // ملاحظة: جميع العمليات أدناه تدعم ربط الصلاحيات مع الأدوار الافتراضية والمخصصة والمستخدمين والشاشات والعمليات بشكل ديناميكي واحترافي

        /// <summary>
        /// جلب جميع الصلاحيات
        /// </summary>
        /// <returns>List of all permissions</returns>
        /// <response code="200">Returns the list of permissions</response>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Permission>>> GetPermissions()
        {
            return await _context.Permissions
                .Include(p => p.Screen)
                .Include(p => p.Action)
                .OrderBy(p => p.PermissionGroup)
                .ThenBy(p => p.Level)
                .ToListAsync();
        }

        /// <summary>
        /// جلب الصلاحيات حسب المجموعة
        /// </summary>
        /// <param name="group">Permission group name</param>
        /// <returns>List of permissions in the group</returns>
        /// <response code="200">Returns the list of permissions</response>
        [HttpGet("group/{group}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Permission>>> GetPermissionsByGroup(string group)
        {
            return await _context.Permissions
                .Where(p => p.PermissionGroup == group)
                .Include(p => p.Screen)
                .Include(p => p.Action)
                .OrderBy(p => p.Level)
                .ToListAsync();
        }

        /// <summary>
        /// جلب صلاحية محددة مع جميع العلاقات
        /// </summary>
        /// <param name="id">Permission ID</param>
        /// <returns>Permission details</returns>
        /// <response code="200">Returns the permission</response>
        /// <response code="404">If the permission is not found</response>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<Permission>> GetPermission(int id)
        {
            var permission = await _context.Permissions
                .Include(p => p.UserPermissions)
                //.Include(p => p.Users)
                .Include(p => p.Screen)
                .Include(p => p.Action)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (permission == null)
            {
                return NotFound();
            }

            return permission;
        }

        /// <summary>
        /// Update a permission
        /// </summary>
        /// <param name="id">Permission ID</param>
        /// <param name="permission">Updated permission data</param>
        /// <returns>No content</returns>
        /// <response code="204">Permission updated successfully</response>
        /// <response code="400">Invalid request</response>
        /// <response code="404">Permission not found</response>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> PutPermission(int id, Permission permission)
        {
            if (id != permission.Id)
            {
                return BadRequest();
            }

            permission.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            _context.Entry(permission).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!PermissionExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        /// <summary>
        /// Create a new permission
        /// </summary>
        /// <param name="permission">Permission data</param>
        /// <returns>Created permission</returns>
        /// <response code="201">Permission created successfully</response>
        /// <response code="400">Invalid request</response>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<Permission>> PostPermission(Permission permission)
        {
            permission.CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            
            _context.Permissions.Add(permission);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetPermission", new { id = permission.Id }, permission);
        }

        /// <summary>
        /// Delete a permission
        /// </summary>
        /// <param name="id">Permission ID</param>
        /// <returns>No content</returns>
        /// <response code="204">Permission deleted successfully</response>
        /// <response code="404">Permission not found</response>
        /// <response code="400">Cannot delete permission that is in use</response>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> DeletePermission(int id)
        {
            var permission = await _context.Permissions.FindAsync(id);
            if (permission == null)
            {
                return NotFound();
            }

            // Check if permission is being used
            var userPermissionsCount = await _context.UserPermissions
                .Where(up => up.PermissionId == id && up.IsActive && !up.IsDeleted)
                .CountAsync();

            if (userPermissionsCount > 0)
            {
                return BadRequest("Cannot delete permission that is currently being used.");
            }

            _context.Permissions.Remove(permission);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// Get users with a specific permission
        /// </summary>
        /// <param name="id">Permission ID</param>
        /// <returns>List of users with this permission</returns>
        /// <response code="200">Returns the list of users</response>
        /// <response code="404">Permission not found</response>
        [HttpGet("{id}/users")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<IEnumerable<User>>> GetUsersWithPermission(int id)
        {
            var permission = await _context.Permissions.FindAsync(id);
            if (permission == null)
            {
                return NotFound();
            }

            var users = await _context.Users
                .Where(u => u.UserPermissions.Any(up => up.PermissionId == id && up.IsActive && !up.IsDeleted))
                .Where(u => u.IsActive && !u.IsDeleted)
                .ToListAsync();

            return users;
        }

        /// <summary>
        /// Get all permission groups
        /// </summary>
        /// <returns>List of unique permission groups</returns>
        /// <response code="200">Returns the list of permission groups</response>
        [HttpGet("groups")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<string>>> GetPermissionGroups()
        {
            var groups = await _context.Permissions
                .Where(p => !string.IsNullOrEmpty(p.PermissionGroup))
                .Select(p => p.PermissionGroup)
                .Distinct()
                .OrderBy(g => g)
                .ToListAsync();

            return groups;
        }

        /// <summary>
        /// Check if current user has a specific permission
        /// </summary>
        /// <remarks>
        /// تم تعليق المنطق الثابت هنا. يجب أن يتم التحقق من الصلاحيات بشكل ديناميكي فقط عبر خدمة موحدة لجلب صلاحيات المستخدم.
        /// </remarks>
        //[HttpGet("check/{permissionName}")]
        //[ProducesResponseType(StatusCodes.Status200OK)]
        //public ActionResult<object> CheckPermission(string permissionName)
        //{
        //    try
        //    {
        //        // هذا المنطق كان يعيد true دائماً (غير مقبول في الإنتاج)
        //        // يجب الاعتماد فقط على الخدمة الديناميكية لجلب الصلاحيات
        //        var hasPermission = true;
        //        return Ok(new { hasPermission = hasPermission });
        //    }
        //    catch (Exception ex)
        //    {
        //        return BadRequest(new { error = ex.Message, hasPermission = false });
        //    }
        //}

        /// <summary>
        /// التحقق من صلاحية مستخدم محدد - طريقة محسنة
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="permissionName">اسم الصلاحية المراد التحقق منها</param>
        /// <returns>نتيجة التحقق من الصلاحية</returns>
        /// <response code="200">إرجاع نتيجة التحقق من الصلاحية</response>
        /// <response code="404">المستخدم غير موجود</response>
        [HttpGet("check/{userId}/{permissionName}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<object>> CheckUserPermission(int userId, string permissionName)
        {
            try
            {
                // جلب المستخدم مع علاقاته
                var user = await _context.Users
                    .Include(u => u.UserPermissions)
                    .Include(u => u.UserCustomRoles)
                        .ThenInclude(ucr => ucr.CustomRole)
                    .FirstOrDefaultAsync(u => u.Id == userId);
                if (user == null)
                {
                    return NotFound(new { error = "User not found", hasPermission = false });
                }

                // جلب جميع الأدوار من القاموس
                var rolesDict = await _context.Roles
                    .Where(r => r.IsActive)
                    .ToDictionaryAsync(r => r.Id, r => r.Name);

                // استبدال أي استخدام user.Role أو user.Role?.Name بجلب اسم الدور من القاموس عبر RoleId
                string role = user.RoleId.HasValue && rolesDict.ContainsKey(user.RoleId.Value) ? rolesDict[user.RoleId.Value] : null;

                // 1. الصلاحيات الافتراضية للدور
                var rolePermissions = new List<string>();
                if (user.RoleId.HasValue)
                {
                    rolePermissions = await _context.RoleDefaultPermissions
                        .Where(rp => rp.RoleId == user.RoleId.Value)
                        .Include(rp => rp.Permission)
                        .Select(rp => rp.Permission.Name)
                        .ToListAsync();
                }

                // 2. الصلاحيات المخصصة للمستخدم
                var userPermissions = await _context.UserPermissions
                    .Where(up => up.UserId == userId && up.IsActive && !up.IsDeleted)
                    .Include(up => up.Permission)
                    .Select(up => up.Permission.Name)
                    .ToListAsync();

                // 3. صلاحيات الأدوار المخصصة
                var customRoleIds = user.UserCustomRoles
                    .Where(ucr => !ucr.IsDeleted)
                    .Select(ucr => ucr.CustomRoleId)
                    .ToList();
                var customRolePermissions = new List<string>();
                if (customRoleIds.Any())
                {
                    customRolePermissions = await _context.CustomRolePermissions
                        .Where(crp => customRoleIds.Contains(crp.CustomRoleId) && !crp.IsDeleted)
                        .Include(crp => crp.Permission)
                        .Select(crp => crp.Permission.Name)
                        .ToListAsync();
                }

                // دمج جميع الصلاحيات
                var allPermissions = new HashSet<string>(rolePermissions, StringComparer.OrdinalIgnoreCase);
                foreach (var p in userPermissions) allPermissions.Add(p);
                foreach (var p in customRolePermissions) allPermissions.Add(p);

                var hasPermission = allPermissions.Contains(permissionName);

                return Ok(new { hasPermission = hasPermission });
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message, hasPermission = false });
            }
        }

        /// <summary>
        /// Get permissions for a specific role (default role)
        /// </summary>
        [HttpGet("by-role/{roleId}")]
        public async Task<ActionResult<IEnumerable<Permission>>> GetRolePermissions(int roleId)
        {
            var permissions = await _context.RoleDefaultPermissions
                .Where(rp => rp.RoleId == roleId)
                .Include(rp => rp.Permission)
                .Select(rp => rp.Permission)
                .ToListAsync();
            return permissions;
        }

        /// <summary>
        /// Get permissions for a specific custom role
        /// </summary>
        [HttpGet("by-custom-role/{customRoleId}")]
        public async Task<ActionResult<IEnumerable<Permission>>> GetCustomRolePermissions(int customRoleId)
        {
            var permissions = await _context.CustomRolePermissions
                .Where(crp => crp.CustomRoleId == customRoleId && !crp.IsDeleted)
                .Include(crp => crp.Permission)
                .Select(crp => crp.Permission)
                .ToListAsync();
            return permissions;
        }

        /// <summary>
        /// Get direct permissions for a specific user
        /// </summary>
        [HttpGet("by-user/{userId}")]
        public async Task<ActionResult<IEnumerable<Permission>>> GetUserDirectPermissions(int userId)
        {
            var permissions = await _context.UserPermissions
                .Where(up => up.UserId == userId && up.IsActive && !up.IsDeleted)
                .Include(up => up.Permission)
                .Select(up => up.Permission)
                .ToListAsync();
            return permissions;
        }

        /// <summary>
        /// التحقق السريع من صلاحية المستخدم - استخدام الخدمة المحسنة
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="permissionName">اسم الصلاحية</param>
        /// <returns>نتيجة التحقق</returns>
        [HttpGet("quick-check/{userId}/{permissionName}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> QuickCheckPermission(int userId, string permissionName)
        {
            try
            {
                // استخدام الخدمة المحسنة للتحقق السريع
                var hasPermission = await _permissionService.HasPermissionAsync(userId, permissionName);
                
                return Ok(new 
                { 
                    userId = userId,
                    permission = permissionName,
                    hasPermission = hasPermission,
                    checkedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    message = hasPermission ? "المستخدم يملك الصلاحية" : "المستخدم لا يملك الصلاحية"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new 
                { 
                    error = ex.Message, 
                    hasPermission = false,
                    message = "حدث خطأ أثناء التحقق من الصلاحية"
                });
            }
        }

        /// <summary>
        /// جلب جميع صلاحيات المستخدم الفعلية - محسن مع التخزين المؤقت
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>قائمة بجميع صلاحيات المستخدم</returns>
        [HttpGet("user-all-permissions/{userId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<object>> GetAllUserPermissions(int userId)
        {
            try
            {
                // التحقق من وجود المستخدم
                var userExists = await _context.Users
                    .AnyAsync(u => u.Id == userId && u.IsActive && !u.IsDeleted);
                
                if (!userExists)
                {
                    return NotFound(new 
                    { 
                        message = "المستخدم غير موجود أو غير نشط",
                        userId = userId
                    });
                }

                // جلب جميع الصلاحيات باستخدام الخدمة المحسنة
                var permissions = await _permissionService.GetAllUserPermissionsAsync(userId);
                
                return Ok(new 
                {
                    userId = userId,
                    totalPermissions = permissions.Count,
                    permissions = permissions.OrderBy(p => p).ToList(),
                    retrievedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    message = $"تم جلب {permissions.Count} صلاحية للمستخدم"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new 
                { 
                    error = ex.Message,
                    message = "حدث خطأ أثناء جلب صلاحيات المستخدم"
                });
            }
        }

        /// <summary>
        /// التحقق من صلاحيات متعددة للمستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="request">قائمة الصلاحيات المراد التحقق منها</param>
        /// <returns>نتائج التحقق من الصلاحيات</returns>
        [HttpPost("check-multiple/{userId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> CheckMultiplePermissions(int userId, [FromBody] CheckMultiplePermissionsRequest request)
        {
            try
            {
                if (request?.Permissions == null || !request.Permissions.Any())
                {
                    return BadRequest(new { message = "يجب تحديد صلاحيات للتحقق منها" });
                }

                var results = new List<object>();
                
                foreach (var permission in request.Permissions)
                {
                    var hasPermission = await _permissionService.HasPermissionAsync(userId, permission);
                    results.Add(new 
                    {
                        permission = permission,
                        hasPermission = hasPermission
                    });
                }

                // التحقق من جميع الصلاحيات أو أي صلاحية حسب الطلب
                var hasAllPermissions = await _permissionService.HasPermissionsAsync(userId, request.Permissions, true);
                var hasAnyPermission = await _permissionService.HasPermissionsAsync(userId, request.Permissions, false);

                return Ok(new 
                {
                    userId = userId,
                    totalChecked = request.Permissions.Count(),
                    hasAllPermissions = hasAllPermissions,
                    hasAnyPermission = hasAnyPermission,
                    results = results,
                    checkedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new 
                { 
                    error = ex.Message,
                    message = "حدث خطأ أثناء التحقق من الصلاحيات المتعددة"
                });
            }
        }

        /// <summary>
        /// مسح التخزين المؤقت لصلاحيات مستخدم محدد
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>نتيجة العملية</returns>
        [HttpPost("clear-cache/{userId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public ActionResult<object> ClearUserPermissionsCache(int userId)
        {
            try
            {
                _permissionService.ClearUserPermissionsCache(userId);
                
                return Ok(new 
                {
                    message = "تم مسح التخزين المؤقت لصلاحيات المستخدم بنجاح",
                    userId = userId,
                    clearedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new 
                { 
                    error = ex.Message,
                    message = "حدث خطأ أثناء مسح التخزين المؤقت"
                });
            }
        }

        private bool PermissionExists(int id)
        {
            return _context.Permissions.Any(e => e.Id == id);
        }
    }

    /// <summary>
    /// نموذج طلب التحقق من صلاحيات متعددة
    /// </summary>
    public class CheckMultiplePermissionsRequest
    {
        /// <summary>
        /// قائمة أسماء الصلاحيات المراد التحقق منها
        /// </summary>
        public IEnumerable<string> Permissions { get; set; } = new List<string>();
    }
}
