﻿﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace webApi.Models;

public partial class TasksDbContext : DbContext
{
    public TasksDbContext()
    {

    }

    public TasksDbContext(DbContextOptions<TasksDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<ArchiveCategory> ArchiveCategories { get; set; }

    public virtual DbSet<ArchiveDocument> ArchiveDocuments { get; set; }

    public virtual DbSet<ArchiveTag> ArchiveTags { get; set; }

    // ArchiveDocumentTag is handled as many-to-many relationship, no DbSet needed

    public virtual DbSet<Attachment> Attachments { get; set; }

    public virtual DbSet<Backup> Backups { get; set; }

    public virtual DbSet<CalendarEvent> CalendarEvents { get; set; }

    public virtual DbSet<Dashboard> Dashboards { get; set; }
    public virtual DbSet<DashboardWidget> DashboardWidgets { get; set; }
    public virtual DbSet<Department> Departments { get; set; }
    public virtual DbSet<Notification> Notifications { get; set; }
    public virtual DbSet<NotificationSetting> NotificationSettings { get; set; }
    public virtual DbSet<Permission> Permissions { get; set; }
    public virtual DbSet<Report> Reports { get; set; }

    public virtual DbSet<ReportSchedule> ReportSchedules { get; set; }

    public virtual DbSet<Subtask> Subtasks { get; set; }

    public virtual DbSet<SystemLog> SystemLogs { get; set; }

    public virtual DbSet<SystemSetting> SystemSettings { get; set; }

    public virtual DbSet<Task> Tasks { get; set; }

    public virtual DbSet<TaskComment> TaskComments { get; set; }

    public virtual DbSet<TaskDocument> TaskDocuments { get; set; }

    public virtual DbSet<TaskHistory> TaskHistories { get; set; }

    public virtual DbSet<TaskProgressTracker> TaskProgressTrackers { get; set; }

    public virtual DbSet<TaskType> TaskTypes { get; set; }

    public virtual DbSet<TimeTrackingEntry> TimeTrackingEntries { get; set; }

    public virtual DbSet<User> Users { get; set; }

    public virtual DbSet<UserPermission> UserPermissions { get; set; }

    public virtual DbSet<ActivityLog> ActivityLogs { get; set; }

    public virtual DbSet<TaskAccessUser> TaskAccessUsers { get; set; }

    public virtual DbSet<TaskMessage> TaskMessages { get; set; }

    public virtual DbSet<TaskMessageRead> TaskMessageReads { get; set; }

    // Chat related DbSets
    public virtual DbSet<ChatGroup> ChatGroups { get; set; }
    public virtual DbSet<Message> Messages { get; set; }
    public virtual DbSet<GroupMember> GroupMembers { get; set; }
    public virtual DbSet<MessageRead> MessageReads { get; set; }
    public virtual DbSet<MessageAttachment> MessageAttachments { get; set; }
    public virtual DbSet<MessageReaction> MessageReactions { get; set; }

    public virtual DbSet<Screen> Screens { get; set; }
    public virtual DbSet<ActionEntity> Actions { get; set; }

    public virtual DbSet<CustomRole> CustomRoles { get; set; }
    public virtual DbSet<CustomRolePermission> CustomRolePermissions { get; set; }
    public virtual DbSet<UserCustomRole> UserCustomRoles { get; set; }

    public virtual DbSet<ScreenAction> ScreenActions { get; set; }

    public virtual DbSet<RoleDefaultPermission> RoleDefaultPermissions { get; set; }

    public virtual DbSet<Role> Roles { get; set; } // إضافة DbSet<Role> Roles

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        // Connection string will be configured in Program.cs
        if (!optionsBuilder.IsConfigured)
        {
            // Fallback connection string for design-time operations
            optionsBuilder.UseSqlServer("Data Source=.\\sqlexpress;Initial Catalog=databasetasks;Integrated Security=True;Encrypt=False;Trust Server Certificate=True");
        }
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Configure Chat related entities
        modelBuilder.Entity<ChatGroup>(entity =>
        {
            entity.ToTable("chat_groups");
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Name).HasColumnName("name").HasMaxLength(100).IsRequired();
            entity.Property(e => e.Description).HasColumnName("description").HasMaxLength(500);
            entity.Property(e => e.IsPrivate).HasColumnName("is_private").HasDefaultValue(false);
            entity.Property(e => e.CreatedBy).HasColumnName("created_by").IsRequired();
            entity.Property(e => e.CreatedAt).HasColumnName("created_at").IsRequired();
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
            entity.Property(e => e.ImageUrl).HasColumnName("image_url").HasMaxLength(255);
            entity.Property(e => e.IsArchived).HasColumnName("is_archived").HasDefaultValue(false);
            entity.Property(e => e.IsDirectMessage).HasColumnName("is_direct_message").HasDefaultValue(false);
            entity.Property(e => e.GroupType).HasColumnName("group_type").HasMaxLength(50).HasDefaultValue("general");
            entity.Property(e => e.MaxMembers).HasColumnName("max_members");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted").HasDefaultValue(false);

            // تكوين العلاقة مع User بشكل صريح لمنع shadow properties
            entity.HasOne(e => e.Creator)
                .WithMany() // بدون navigation property عكسية
                .HasForeignKey(e => e.CreatedBy)
                .HasConstraintName("FK_chat_groups_created_by_users")
                .OnDelete(DeleteBehavior.Restrict);

            // تجاهل navigation properties الأخرى مؤقتاً
            entity.Ignore(e => e.Members);
            entity.Ignore(e => e.Messages);

            // إضافة فهارس لتحسين الأداء
            entity.HasIndex(e => e.IsDeleted).HasDatabaseName("IX_chat_groups_is_deleted");
            entity.HasIndex(e => e.GroupType).HasDatabaseName("IX_chat_groups_group_type");
            entity.HasIndex(e => new { e.IsDeleted, e.IsArchived }).HasDatabaseName("IX_chat_groups_deleted_archived");
        });

        modelBuilder.Entity<Message>(entity =>
        {
            entity.ToTable("messages");

            // تكوين الأعمدة لتتطابق مع جدول قاعدة البيانات الفعلي
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.GroupId).HasColumnName("group_id");
            entity.Property(e => e.SenderId).HasColumnName("sender_id");
            entity.Property(e => e.Content).HasColumnName("content").IsRequired();
            entity.Property(e => e.ContentType).HasColumnName("content_type").HasDefaultValue(0);
            entity.Property(e => e.ReplyToMessageId).HasColumnName("reply_to_message_id");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted").HasDefaultValue(false);
            entity.Property(e => e.IsRead).HasColumnName("is_read").HasDefaultValue(false);
            entity.Property(e => e.IsPinned).HasColumnName("is_pinned").HasDefaultValue(false);
            entity.Property(e => e.PinnedAt).HasColumnName("pinned_at");
            entity.Property(e => e.PinnedBy).HasColumnName("pinned_by");
            entity.Property(e => e.Priority).HasColumnName("priority").HasDefaultValue(0);
            entity.Property(e => e.IsMarkedForFollowUp).HasColumnName("is_marked_for_follow_up").HasDefaultValue(false);
            entity.Property(e => e.FollowUpAt).HasColumnName("follow_up_at");
            entity.Property(e => e.MarkedForFollowUpBy).HasColumnName("marked_for_follow_up_by");
            entity.Property(e => e.IsEdited).HasColumnName("is_edited").HasDefaultValue(false);
            entity.Property(e => e.ReceiverId).HasColumnName("receiver_id");
            entity.Property(e => e.SentAt).HasColumnName("sent_at");

            // تجاهل خصائص Navigation Properties التي تسبب مشاكل في EF
            entity.Ignore(e => e.MentionedUserIds);

            // تحديد العلاقات
            entity.HasOne(d => d.Group)
                .WithMany(p => p.Messages)
                .HasForeignKey(d => d.GroupId)
                .HasConstraintName("FK_messages_group_id_chat_groups")
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(d => d.Sender)
                .WithMany()
                .HasForeignKey(d => d.SenderId)
                .HasConstraintName("FK_messages_sender_id_users")
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(d => d.ReplyToMessage)
                .WithMany()
                .HasForeignKey(d => d.ReplyToMessageId)
                .HasConstraintName("FK_messages_reply_to_message_id_messages")
                .OnDelete(DeleteBehavior.Restrict);

            // تعطيل العلاقات المتعددة مع User مؤقتاً لحل مشكلة الأعمدة الإضافية
            // يمكن الوصول لهذه البيانات عبر استعلامات منفصلة

            // entity.HasOne(d => d.PinnedByUser)
            //     .WithMany()
            //     .HasForeignKey(d => d.PinnedBy)
            //     .HasConstraintName("FK_messages_pinned_by_users")
            //     .OnDelete(DeleteBehavior.Restrict)
            //     .IsRequired(false);

            // entity.HasOne(d => d.MarkedForFollowUpByUser)
            //     .WithMany()
            //     .HasForeignKey(d => d.MarkedForFollowUpBy)
            //     .HasConstraintName("FK_messages_marked_for_follow_up_by_users")
            //     .OnDelete(DeleteBehavior.Restrict)
            //     .IsRequired(false);

            // entity.HasOne(d => d.Receiver)
            //     .WithMany()
            //     .HasForeignKey(d => d.ReceiverId)
            //     .HasConstraintName("FK_messages_receiver_id_users")
            //     .OnDelete(DeleteBehavior.Restrict)
            //     .IsRequired(false);
        });

        modelBuilder.Entity<GroupMember>(entity =>
        {
            entity.ToTable("group_members");
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.GroupId).HasColumnName("group_id");
            entity.Property(e => e.UserId).HasColumnName("user_id");
            entity.Property(e => e.Role).HasColumnName("role").HasDefaultValue(1);
            entity.Property(e => e.JoinedAt).HasColumnName("joined_at");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted").HasDefaultValue(false);
            entity.Property(e => e.LeftAt).HasColumnName("left_at");

            entity.HasOne(d => d.Group)
                .WithMany(p => p.Members)
                .HasForeignKey(d => d.GroupId)
                .HasConstraintName("FK_group_members_group")
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(d => d.User)
                .WithMany()
                .HasForeignKey(d => d.UserId)
                .HasConstraintName("FK_group_members_user")
                .OnDelete(DeleteBehavior.Restrict);
        });

        modelBuilder.Entity<MessageRead>(entity =>
        {
            entity.ToTable("message_reads");
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.MessageId).HasColumnName("message_id");
            entity.Property(e => e.UserId).HasColumnName("user_id");
            entity.Property(e => e.ReadAt).HasColumnName("read_at");

            // إضافة قيد فريد لمنع تكرار القراءة من نفس المستخدم لنفس الرسالة
            entity.HasIndex(e => new { e.MessageId, e.UserId })
                .IsUnique()
                .HasDatabaseName("UQ_MessageRead_Message_User");

            entity.HasOne(d => d.Message)
                .WithMany(p => p.Reads)
                .HasForeignKey(d => d.MessageId)
                .HasConstraintName("FK_message_reads_message")
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(d => d.User)
                .WithMany()
                .HasForeignKey(d => d.UserId)
                .HasConstraintName("FK_message_reads_user")
                .OnDelete(DeleteBehavior.Restrict);
        });

        modelBuilder.Entity<MessageAttachment>(entity =>
        {
            entity.ToTable("message_attachments");
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.MessageId).HasColumnName("message_id");
            entity.Property(e => e.FileName).HasColumnName("file_name").HasMaxLength(255);
            entity.Property(e => e.FilePath).HasColumnName("file_path").HasMaxLength(255);
            entity.Property(e => e.FileSize).HasColumnName("file_size");
            entity.Property(e => e.FileType).HasColumnName("file_type").HasMaxLength(50);
            entity.Property(e => e.UploadedAt).HasColumnName("uploaded_at");
            entity.Property(e => e.UploadedBy).HasColumnName("uploaded_by");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted").HasDefaultValue(false);

            entity.HasOne(d => d.Message)
                .WithMany(p => p.Attachments)
                .HasForeignKey(d => d.MessageId)
                .HasConstraintName("FK_message_attachments_message")
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(d => d.UploadedByNavigation)
                .WithMany()
                .HasForeignKey(d => d.UploadedBy)
                .HasConstraintName("FK_message_attachments_user")
                .OnDelete(DeleteBehavior.Restrict);
        });

        modelBuilder.Entity<MessageReaction>(entity =>
        {
            entity.ToTable("message_reactions");
            entity.HasKey(e => e.Id);

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.MessageId).HasColumnName("message_id");
            entity.Property(e => e.UserId).HasColumnName("user_id");
            entity.Property(e => e.Reaction).HasColumnName("reaction").HasMaxLength(10);
            entity.Property(e => e.ReactionType).HasColumnName("reaction_type").HasMaxLength(50);
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted").HasDefaultValue(false);

            // إضافة قيد فريد لمنع تكرار التفاعل من نفس المستخدم على نفس الرسالة
            entity.HasIndex(e => new { e.MessageId, e.UserId, e.Reaction })
                .IsUnique()
                .HasDatabaseName("UQ_MessageReaction_Message_User_Reaction")
                .HasFilter("[is_deleted] = 0");

            // تحديد العلاقة مع Message بوضوح
            entity.HasOne(d => d.Message)
                .WithMany(p => p.Reactions)
                .HasForeignKey(d => d.MessageId)
                .HasConstraintName("FK_message_reactions_message")
                .OnDelete(DeleteBehavior.Cascade);

            // تحديد العلاقة مع User بوضوح مع تجنب التعارض
            entity.HasOne(d => d.User)
                .WithMany()
                .HasForeignKey(d => d.UserId)
                .HasConstraintName("FK_message_reactions_user")
                .OnDelete(DeleteBehavior.Restrict);
        });

        // تكوين باقي الكيانات
            modelBuilder.Entity<ArchiveCategory>(entity =>
            {
                entity.HasKey(e => e.Id).HasName("PK__archive___3213E83F1C467069");

                entity.ToTable("archive_categories");

                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.CreatedBy).HasColumnName("created_by");
                entity.Property(e => e.Description).HasColumnName("description");
                entity.Property(e => e.Color)
                    .HasMaxLength(20)
                    .HasColumnName("color");
                entity.Property(e => e.Icon)
                    .HasMaxLength(50)
                    .HasColumnName("icon");
                entity.Property(e => e.IsActive)
                    .HasDefaultValue(true)
                    .HasColumnName("is_active");
                entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
                entity.Property(e => e.Name)
                    .HasMaxLength(100)
                    .HasColumnName("name");
                entity.Property(e => e.ParentId).HasColumnName("parent_id");
                entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");

                entity.HasOne(d => d.CreatedByNavigation).WithMany(p => p.ArchiveCategories)
                    .HasForeignKey(d => d.CreatedBy)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_archive_categories_users");

                entity.HasOne(d => d.Parent).WithMany(p => p.InverseParent)
                    .HasForeignKey(d => d.ParentId)
                    .HasConstraintName("FK_archive_categories_parent");
            });

            modelBuilder.Entity<ArchiveDocument>(entity =>
            {
                entity.HasKey(e => e.Id).HasName("PK__archive___3213E83F98BCFBC5");

                entity.ToTable("archive_documents");

                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.CategoryId).HasColumnName("category_id");
                entity.Property(e => e.Description).HasColumnName("description");
                entity.Property(e => e.FileName)
                    .HasMaxLength(255)
                    .HasColumnName("file_name");
                entity.Property(e => e.FilePath)
                    .HasMaxLength(255)
                    .HasColumnName("file_path");
                entity.Property(e => e.FileSize).HasColumnName("file_size");
                entity.Property(e => e.FileType)
                    .HasMaxLength(50)
                    .HasColumnName("file_type");
                entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
                entity.Property(e => e.Metadata).HasColumnName("metadata");
                entity.Property(e => e.Content).HasColumnName("content");
                entity.Property(e => e.Title)
                    .HasMaxLength(255)
                    .HasColumnName("title");
                entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
                entity.Property(e => e.UploadedAt).HasColumnName("uploaded_at");
                entity.Property(e => e.UploadedBy).HasColumnName("uploaded_by");
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.CreatedBy).HasColumnName("created_by");

                entity.HasOne(d => d.Category).WithMany(p => p.ArchiveDocuments)
                    .HasForeignKey(d => d.CategoryId)
                    .HasConstraintName("FK_archive_documents_categories");

                entity.HasOne(d => d.UploadedByNavigation).WithMany(p => p.ArchiveDocuments)
                    .HasForeignKey(d => d.UploadedBy)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_archive_documents_users");

                entity.HasOne(d => d.CreatedByNavigation).WithMany()
                    .HasForeignKey(d => d.CreatedBy)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_archive_documents_created_by_users");

                entity.HasMany(d => d.Tags).WithMany(p => p.Documents)
                    .UsingEntity<Dictionary<string, object>>(
                        "ArchiveDocumentTag",
                        r => r.HasOne<ArchiveTag>().WithMany()
                            .HasForeignKey("TagId")
                            .HasConstraintName("FK_archive_document_tags_tag"),
                        l => l.HasOne<ArchiveDocument>().WithMany()
                            .HasForeignKey("DocumentId")
                            .HasConstraintName("FK_archive_document_tags_doc"),
                        j =>
                        {
                            j.HasKey("DocumentId", "TagId");
                            j.ToTable("archive_document_tags");
                            j.IndexerProperty<int>("DocumentId").HasColumnName("document_id");
                            j.IndexerProperty<int>("TagId").HasColumnName("tag_id");
                        });
            });

            modelBuilder.Entity<ArchiveTag>(entity =>
            {
                entity.HasKey(e => e.Id).HasName("PK__archive___3213E83FE3B96A3A");

                entity.ToTable("archive_tags");

                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.Color)
                    .HasMaxLength(20)
                    .HasColumnName("color");
                entity.Property(e => e.Description).HasColumnName("description");
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.CreatedBy).HasColumnName("created_by");
                entity.Property(e => e.IsActive)
                    .HasDefaultValue(true)
                    .HasColumnName("is_active");
                entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
                entity.Property(e => e.Name)
                    .HasMaxLength(100)
                    .HasColumnName("name");
                entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");

                entity.HasOne(d => d.CreatedByNavigation).WithMany(p => p.ArchiveTags)
                    .HasForeignKey(d => d.CreatedBy)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_archive_tags_users");
            });

            // ArchiveDocumentTag is configured as a many-to-many relationship above
            // No need for separate entity configuration

            modelBuilder.Entity<Attachment>(entity =>
            {
                entity.HasKey(e => e.Id).HasName("PK__attachme__3213E83F0E56CFD6");

                entity.ToTable("attachments");

                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.FileName)
                    .HasMaxLength(255)
                    .HasColumnName("file_name");
                entity.Property(e => e.FilePath)
                    .HasMaxLength(255)
                    .HasColumnName("file_path");
                entity.Property(e => e.FileSize).HasColumnName("file_size");
                entity.Property(e => e.FileType)
                    .HasMaxLength(50)
                    .HasColumnName("file_type");
                entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
                entity.Property(e => e.TaskId).HasColumnName("task_id");
                entity.Property(e => e.UploadedAt).HasColumnName("uploaded_at");
                entity.Property(e => e.UploadedBy).HasColumnName("uploaded_by");

                entity.HasOne(d => d.Task).WithMany(p => p.Attachments)
                    .HasForeignKey(d => d.TaskId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_attachments_tasks");

                entity.HasOne(d => d.UploadedByNavigation).WithMany(p => p.Attachments)
                    .HasForeignKey(d => d.UploadedBy)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_attachments_users");
            });

            modelBuilder.Entity<Backup>(entity =>
            {
                entity.HasKey(e => e.Id).HasName("PK__backups__3213E83F76D7E979");

                entity.ToTable("backups");

                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.CreatedBy).HasColumnName("created_by");
                entity.Property(e => e.Description).HasColumnName("description");
                entity.Property(e => e.FileName)
                    .HasMaxLength(255)
                    .HasColumnName("file_name");
                entity.Property(e => e.FilePath)
                    .HasMaxLength(255)
                    .HasColumnName("file_path");
                entity.Property(e => e.FileSize).HasColumnName("file_size");
                entity.Property(e => e.IsAutoBackup).HasColumnName("is_auto_backup");
                entity.Property(e => e.IsRestored).HasColumnName("is_restored");
                entity.Property(e => e.RestoredAt).HasColumnName("restored_at");
                entity.Property(e => e.RestoredBy).HasColumnName("restored_by");

                entity.HasOne(d => d.CreatedByNavigation).WithMany(p => p.BackupCreatedByNavigations)
                    .HasForeignKey(d => d.CreatedBy)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_backups_created_by_users");

                entity.HasOne(d => d.RestoredByNavigation).WithMany(p => p.BackupRestoredByNavigations)
                    .HasForeignKey(d => d.RestoredBy)
                    .HasConstraintName("FK_backups_restored_by_users");
            });

            modelBuilder.Entity<CalendarEvent>(entity =>
            {
                entity.HasKey(e => e.Id).HasName("PK__calendar__3213E83FBE2C8E42");

                entity.ToTable("calendar_events");

                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.AllDay).HasColumnName("all_day");
                entity.Property(e => e.Color)
                    .HasMaxLength(20)
                    .HasColumnName("color");
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.Description).HasColumnName("description");
                entity.Property(e => e.Duration).HasColumnName("duration");
                entity.Property(e => e.EndTime).HasColumnName("end_time");
                entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
                entity.Property(e => e.Location)
                    .HasMaxLength(255)
                    .HasColumnName("location");
                entity.Property(e => e.RecurrenceRule).HasColumnName("recurrence_rule");
                entity.Property(e => e.StartTime).HasColumnName("start_time");
                entity.Property(e => e.Title)
                    .HasMaxLength(255)
                    .HasColumnName("title");
                entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
                entity.Property(e => e.UserId).HasColumnName("user_id");
                entity.Property(e => e.TaskId).HasColumnName("task_id");
                entity.Property(e => e.ReminderEnabled).HasColumnName("reminder_enabled");
                entity.Property(e => e.ReminderMinutes).HasColumnName("reminder_minutes");
                entity.Property(e => e.ReminderSent).HasColumnName("reminder_sent");
                entity.Property(e => e.EventType).HasColumnName("event_type");
                entity.Property(e => e.RecurrencePattern).HasColumnName("recurrence_pattern");
                entity.Property(e => e.RecurrenceCount).HasColumnName("recurrence_count");
                entity.Property(e => e.RecurrenceEndDate).HasColumnName("recurrence_end_date");

                entity.HasOne(d => d.Task).WithMany(p => p.CalendarEvents)
                    .HasForeignKey(d => d.TaskId)
                    .HasConstraintName("FK_calendar_events_task_id_tasks");

                entity.HasOne(d => d.User).WithMany(p => p.CalendarEvents)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_calendar_events_user_id_users");
            });

            modelBuilder.Entity<Dashboard>(entity =>
            {
                entity.HasKey(e => e.Id).HasName("PK__dashboar__3213E83F0FAA756D");

                entity.ToTable("dashboards");

                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.Color)
                    .HasMaxLength(20)
                    .HasColumnName("color");
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.Description).HasColumnName("description");
                entity.Property(e => e.GridColumns)
                    .HasDefaultValue(12)
                    .HasColumnName("grid_columns");
                entity.Property(e => e.GridRows)
                    .HasDefaultValue(12)
                    .HasColumnName("grid_rows");
                entity.Property(e => e.Icon)
                    .HasMaxLength(50)
                    .HasColumnName("icon");
                entity.Property(e => e.IsDefault).HasColumnName("is_default");
                entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
                entity.Property(e => e.IsShared).HasColumnName("is_shared");
                entity.Property(e => e.OwnerId).HasColumnName("owner_id");
                entity.Property(e => e.Settings).HasColumnName("settings");
                entity.Property(e => e.Title)
                    .HasMaxLength(255)
                    .HasColumnName("title");
                entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");

                entity.HasOne(d => d.Owner).WithMany(p => p.Dashboards)
                    .HasForeignKey(d => d.OwnerId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_dashboards_owner_id_users");

                entity.HasMany(d => d.Users).WithMany(p => p.DashboardsNavigation)
                    .UsingEntity<Dictionary<string, object>>(
                        "DashboardShare",
                        r => r.HasOne<User>().WithMany()
                            .HasForeignKey("UserId")
                            .HasConstraintName("FK_dashboard_shares_user"),
                        l => l.HasOne<Dashboard>().WithMany()
                            .HasForeignKey("DashboardId")
                            .HasConstraintName("FK_dashboard_shares_dashboard"),
                        j =>
                        {
                            j.HasKey("DashboardId", "UserId");
                            j.ToTable("dashboard_shares");
                            j.IndexerProperty<int>("DashboardId").HasColumnName("dashboard_id");
                            j.IndexerProperty<int>("UserId").HasColumnName("user_id");
                        });
            });

            modelBuilder.Entity<DashboardWidget>(entity =>
            {
                entity.HasKey(e => e.Id).HasName("PK__dashboar__3213E83FDC09D64E");

                entity.ToTable("dashboard_widgets");

                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.Configuration).HasColumnName("configuration");
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.DashboardId).HasColumnName("dashboard_id");
                entity.Property(e => e.DataSource)
                    .HasMaxLength(50)
                    .HasColumnName("data_source");
                entity.Property(e => e.Height).HasColumnName("height");
                entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
                entity.Property(e => e.IsVisible)
                    .HasDefaultValue(true)
                    .HasColumnName("is_visible");
                entity.Property(e => e.OrderIndex).HasColumnName("order_index");
                entity.Property(e => e.Position).HasColumnName("position");
                entity.Property(e => e.PositionX).HasColumnName("position_x");
                entity.Property(e => e.PositionY).HasColumnName("position_y");
                entity.Property(e => e.Query).HasColumnName("query");
                entity.Property(e => e.RefreshInterval).HasColumnName("refresh_interval");
                entity.Property(e => e.Settings).HasColumnName("settings");
                entity.Property(e => e.Size).HasColumnName("size");
                entity.Property(e => e.Title)
                    .HasMaxLength(255)
                    .HasColumnName("title");
                entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
                entity.Property(e => e.WidgetType)
                    .HasMaxLength(50)
                    .HasColumnName("widget_type");
                entity.Property(e => e.Width).HasColumnName("width");

                entity.HasOne(d => d.Dashboard).WithMany(p => p.DashboardWidgets)
                    .HasForeignKey(d => d.DashboardId)
                    .HasConstraintName("FK_dashboard_widgets_dashboards");
            });

            modelBuilder.Entity<Department>(entity =>
            {
                entity.HasKey(e => e.Id).HasName("PK__departme__3213E83FED2D55C0");
                entity.ToTable("departments");
                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.Description).HasColumnName("description");
                entity.Property(e => e.IsActive)
                    .HasDefaultValue(true)
                    .HasColumnName("is_active");
                entity.Property(e => e.ManagerId).HasColumnName("manager_id");
                entity.Property(e => e.Name)
                    .HasMaxLength(100)
                    .HasColumnName("name");
                // الحقول الجديدة
                entity.Property(e => e.ParentId).HasColumnName("parent_id");
                entity.Property(e => e.Level).HasColumnName("level").HasDefaultValue(0);
                entity.Property(e => e.SortOrder).HasColumnName("sort_order").HasDefaultValue(0);

                entity.HasOne(d => d.Manager).WithMany(p => p.Departments)
                    .HasForeignKey(d => d.ManagerId)
                    .HasConstraintName("FK_departments_users");

                entity.HasOne(d => d.Parent)
                    .WithMany(p => p.Children)
                    .HasForeignKey(d => d.ParentId)
                    .HasConstraintName("FK_Departments_Parent");
            });

            modelBuilder.Entity<Notification>(entity =>
            {
                entity.HasKey(e => e.Id).HasName("PK__notifica__3213E83F6CE68BF2");

                entity.ToTable("notifications");

                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.Content).HasColumnName("content");
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.IsRead).HasColumnName("is_read");
                entity.Property(e => e.RelatedId).HasColumnName("related_id");
                entity.Property(e => e.Title)
                    .HasMaxLength(255)
                    .HasColumnName("title");
                entity.Property(e => e.Type)
                    .HasMaxLength(50)
                    .HasColumnName("type");
                entity.Property(e => e.UserId).HasColumnName("user_id");

                entity.HasOne(d => d.User).WithMany(p => p.Notifications)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_notifications_user_id_users");
            });

            modelBuilder.Entity<NotificationSetting>(entity =>
            {
                entity.HasKey(e => e.Id).HasName("PK__notifica__3213E83F7689570C");

                entity.ToTable("notification_settings");

                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.DeliveryMethod)
                    .HasMaxLength(20)
                    .HasDefaultValue("app")
                    .HasColumnName("delivery_method");
                entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
                entity.Property(e => e.IsEmailEnabled)
                    .HasDefaultValue(true)
                    .HasColumnName("is_email_enabled");
                entity.Property(e => e.IsEnabled)
                    .HasDefaultValue(true)
                    .HasColumnName("is_enabled");
                entity.Property(e => e.IsPushEnabled)
                    .HasDefaultValue(true)
                    .HasColumnName("is_push_enabled");
                entity.Property(e => e.IsSmsEnabled).HasColumnName("is_sms_enabled");
                entity.Property(e => e.NotificationType)
                    .HasMaxLength(50)
                    .HasColumnName("notification_type");
                entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
                entity.Property(e => e.UserId).HasColumnName("user_id");

                entity.HasOne(d => d.User).WithMany(p => p.NotificationSettings)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_notification_settings_user_id_users");
            });

            modelBuilder.Entity<Permission>(entity =>
            {
                entity.HasKey(e => e.Id).HasName("PK__permissi__3213E83F722E0A3D");

                entity.ToTable("permissions");

                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.Category)
                    .HasMaxLength(100)
                    .HasColumnName("category");
                entity.Property(e => e.Color)
                    .HasMaxLength(20)
                    .HasColumnName("color");
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.Description).HasColumnName("description");
                entity.Property(e => e.Icon)
                    .HasMaxLength(50)
                    .HasColumnName("icon");
                entity.Property(e => e.IsDefault).HasColumnName("is_default");
                entity.Property(e => e.Level)
                    .HasDefaultValue(1)
                    .HasColumnName("level");
                entity.Property(e => e.Name)
                    .HasMaxLength(100)
                    .HasColumnName("name");
                entity.Property(e => e.PermissionGroup)
                    .HasMaxLength(50)
                    .HasColumnName("permission_group");
                entity.Property(e => e.ScreenId).HasColumnName("screen_id");
                entity.Property(e => e.ActionId).HasColumnName("action_id");

                entity.HasOne(e => e.Screen)
                    .WithMany(s => s.Permissions)
                    .HasForeignKey(e => e.ScreenId)
                    .HasConstraintName("FK_permissions_screens");

                entity.HasOne(e => e.Action)
                    .WithMany(a => a.Permissions)
                    .HasForeignKey(e => e.ActionId)
                    .HasConstraintName("FK_permissions_actions");
            });

            modelBuilder.Entity<Report>(entity =>
            {
                entity.HasKey(e => e.Id).HasName("PK__reports__3213E83F1D27A696");

                entity.ToTable("reports");

                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.CreatedBy).HasColumnName("created_by");
                entity.Property(e => e.Description).HasColumnName("description");
                entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
                entity.Property(e => e.IsPublic).HasColumnName("is_public");
                entity.Property(e => e.Parameters).HasColumnName("parameters");
                entity.Property(e => e.Query).HasColumnName("query");
                entity.Property(e => e.ReportType)
                    .HasMaxLength(50)
                    .HasColumnName("report_type");
                entity.Property(e => e.Title)
                    .HasMaxLength(255)
                    .HasColumnName("title");
                entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");

                entity.HasOne(d => d.CreatedByNavigation).WithMany(p => p.Reports)
                    .HasForeignKey(d => d.CreatedBy)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_reports_created_by_users");
            });

            modelBuilder.Entity<ReportSchedule>(entity =>
            {
                entity.HasKey(e => e.Id).HasName("PK__report_s__3213E83F2D45617B");

                entity.ToTable("report_schedules");

                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.CreatedBy).HasColumnName("created_by");
                entity.Property(e => e.DayOfMonth).HasColumnName("day_of_month");
                entity.Property(e => e.DayOfWeek).HasColumnName("day_of_week");
                entity.Property(e => e.Frequency)
                    .HasMaxLength(20)
                    .HasColumnName("frequency");
                entity.Property(e => e.Hour).HasColumnName("hour");
                entity.Property(e => e.IsActive)
                    .HasDefaultValue(true)
                    .HasColumnName("is_active");
                entity.Property(e => e.LastExecutionAt).HasColumnName("last_execution_at");
                entity.Property(e => e.Minute).HasColumnName("minute");
                entity.Property(e => e.NextExecutionAt).HasColumnName("next_execution_at");
                entity.Property(e => e.Recipients).HasColumnName("recipients");
                entity.Property(e => e.ReportId).HasColumnName("report_id");
                entity.Property(e => e.Title)
                    .HasMaxLength(255)
                    .HasColumnName("title");
                entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");

                entity.HasOne(d => d.CreatedByNavigation).WithMany(p => p.ReportSchedules)
                    .HasForeignKey(d => d.CreatedBy)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_report_schedules_created_by_users");

                entity.HasOne(d => d.Report).WithMany(p => p.ReportSchedules)
                    .HasForeignKey(d => d.ReportId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_report_schedules_report_id_reports");
            });

            modelBuilder.Entity<Subtask>(entity =>
            {
                entity.HasKey(e => e.Id).HasName("PK__subtasks__3213E83F6AB5384A");

                entity.ToTable("subtasks");

                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.CompletedAt).HasColumnName("completed_at");
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.IsCompleted).HasColumnName("is_completed");
                entity.Property(e => e.TaskId).HasColumnName("task_id");
                entity.Property(e => e.Title)
                    .HasMaxLength(255)
                    .HasColumnName("title");

                entity.HasOne(d => d.Task).WithMany(p => p.Subtasks)
                    .HasForeignKey(d => d.TaskId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_subtasks_task_id_tasks");
            });

            modelBuilder.Entity<SystemLog>(entity =>
            {
                entity.HasKey(e => e.Id).HasName("PK__system_l__3213E83FFBDD2D56");

                entity.ToTable("system_logs");

                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.Details).HasColumnName("details");
                entity.Property(e => e.IpAddress)
                    .HasMaxLength(50)
                    .HasColumnName("ip_address");
                entity.Property(e => e.LogLevel)
                    .HasMaxLength(20)
                    .HasColumnName("log_level");
                entity.Property(e => e.LogType)
                    .HasMaxLength(50)
                    .HasColumnName("log_type");
                entity.Property(e => e.Message).HasColumnName("message");
                entity.Property(e => e.UserId).HasColumnName("user_id");

                entity.HasOne(d => d.User).WithMany(p => p.SystemLogs)
                    .HasForeignKey(d => d.UserId)
                    .HasConstraintName("FK_system_logs_user_id_users");
            });

            modelBuilder.Entity<SystemSetting>(entity =>
            {
                entity.HasKey(e => e.Id).HasName("PK__system_s__3213E83FC7ECE2F3");

                entity.ToTable("system_settings");

                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.CreatedBy).HasColumnName("created_by");
                entity.Property(e => e.Description).HasColumnName("description");
                entity.Property(e => e.SettingGroup)
                    .HasMaxLength(50)
                    .HasColumnName("setting_group");
                entity.Property(e => e.SettingKey)
                    .HasMaxLength(100)
                    .HasColumnName("setting_key");
                entity.Property(e => e.SettingValue).HasColumnName("setting_value");
                entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");

                entity.HasOne(d => d.CreatedByNavigation).WithMany(p => p.SystemSettings)
                    .HasForeignKey(d => d.CreatedBy)
                    .HasConstraintName("FK_system_settings_created_by_users");
            });

            modelBuilder.Entity<Task>(entity =>
            {
                entity.HasKey(e => e.Id).HasName("PK__tasks__3213E83F08A84EF4");

                entity.ToTable("tasks");

                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.ActualTime).HasColumnName("actual_time");
                entity.Property(e => e.AssigneeId).HasColumnName("assignee_id");
                entity.Property(e => e.CompletedAt).HasColumnName("completed_at");
                entity.Property(e => e.CompletionPercentage).HasColumnName("completion_percentage");
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.CreatorId).HasColumnName("creator_id");
                entity.Property(e => e.DepartmentId).HasColumnName("department_id");
                entity.Property(e => e.Description).HasColumnName("description");
                entity.Property(e => e.DueDate).HasColumnName("due_date");
                entity.Property(e => e.EstimatedTime).HasColumnName("estimated_time");
                entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
                entity.Property(e => e.Priority).HasColumnName("priority");
                entity.Property(e => e.StartDate).HasColumnName("start_date");
                entity.Property(e => e.Status).HasColumnName("status");
                entity.Property(e => e.TaskTypeId).HasColumnName("task_type_id");
                entity.Property(e => e.Title)
                    .HasMaxLength(255)
                    .HasColumnName("title");
                
                // إضافة تكوين الحقول الجديدة
                // Configuration for new fields
                entity.Property(e => e.Incoming)
                    .HasColumnName("incoming");
                
                entity.Property(e => e.Note)
                    .HasColumnName("note");

                entity.HasOne(d => d.Assignee).WithMany(p => p.TaskAssignees)
                    .HasForeignKey(d => d.AssigneeId)
                    .HasConstraintName("FK_tasks_assignee");

                entity.HasOne(d => d.Creator).WithMany(p => p.TaskCreators)
                    .HasForeignKey(d => d.CreatorId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_tasks_creator");

                entity.HasOne(d => d.Department).WithMany(p => p.Tasks)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_tasks_departments");

                //entity.HasOne(d => d.PriorityNavigation).WithMany(p => p.Tasks)
                //    .HasForeignKey(d => d.Priority)
                //    .OnDelete(DeleteBehavior.ClientSetNull)
                //    .HasConstraintName("FK_tasks_task_priorities");

                //entity.HasOne(d => d.StatusNavigation).WithMany(p => p.Tasks)
                //    .HasForeignKey(d => d.Status)
                //    .OnDelete(DeleteBehavior.ClientSetNull)
                //    .HasConstraintName("FK_tasks_task_statuses");

                entity.HasOne(d => d.TaskType).WithMany(p => p.Tasks)
                    .HasForeignKey(d => d.TaskTypeId)
                    .HasConstraintName("FK_tasks_task_types");

                entity.HasMany(d => d.Users).WithMany(p => p.Tasks)
                    .UsingEntity<TaskAccessUser>(
                        l => l.HasOne(tau => tau.User).WithMany().HasForeignKey(tau => tau.UserId),
                        r => r.HasOne(tau => tau.Task).WithMany().HasForeignKey(tau => tau.TaskId),
                        j =>
                        {
                            j.HasKey(tau => new { tau.TaskId, tau.UserId });
                            j.ToTable("task_access_users");
                            j.Property(tau => tau.TaskId).HasColumnName("task_id");
                            j.Property(tau => tau.UserId).HasColumnName("user_id");
                        });
            });

            modelBuilder.Entity<TaskAccessUser>(entity =>
            {
                entity.HasKey(e => new { e.TaskId, e.UserId });
                entity.ToTable("task_access_users");

                entity.Property(e => e.TaskId).HasColumnName("task_id");
                entity.Property(e => e.UserId).HasColumnName("user_id");

                entity.HasOne(d => d.Task).WithMany()
                    .HasForeignKey(d => d.TaskId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_task_access_users_task");

                entity.HasOne(d => d.User).WithMany()
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_task_access_users_user");
            });

            modelBuilder.Entity<TaskComment>(entity =>
            {
                entity.HasKey(e => e.Id).HasName("PK__task_com__3213E83FB7A32E85");

                entity.ToTable("task_comments");

                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.Content).HasColumnName("content");
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
                entity.Property(e => e.TaskId).HasColumnName("task_id");
                entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
                entity.Property(e => e.UserId).HasColumnName("user_id");

                entity.HasOne(d => d.Task).WithMany(p => p.TaskComments)
                    .HasForeignKey(d => d.TaskId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_comments_tasks");

                entity.HasOne(d => d.User).WithMany(p => p.TaskComments)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_comments_users");
            });

            modelBuilder.Entity<TaskDocument>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.ToTable("task_documents");

                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.TaskId).HasColumnName("task_id");
                entity.Property(e => e.ArchiveDocumentId).HasColumnName("archive_document_id");
                entity.Property(e => e.Type)
                    .HasMaxLength(50)
                    .HasDefaultValue("report")
                    .HasColumnName("type");
                entity.Property(e => e.Description)
                    .HasMaxLength(500)
                    .HasColumnName("description");
                entity.Property(e => e.CreatedBy).HasColumnName("created_by");
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
                entity.Property(e => e.IsDeleted)
                    .HasDefaultValue(false)
                    .HasColumnName("is_deleted");
                entity.Property(e => e.IsShared)
                    .HasDefaultValue(false)
                    .HasColumnName("is_shared");
                entity.Property(e => e.Permission)
                    .HasMaxLength(20)
                    .HasDefaultValue("read")
                    .HasColumnName("permission");

                // إنشاء مفتاح مركب فريد
                entity.HasIndex(e => new { e.TaskId, e.ArchiveDocumentId })
                    .IsUnique()
                    .HasDatabaseName("UK_task_documents_task_archive");

                // العلاقات
                entity.HasOne(d => d.Task).WithMany()
                    .HasForeignKey(d => d.TaskId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_task_documents_tasks");

                entity.HasOne(d => d.ArchiveDocument).WithMany()
                    .HasForeignKey(d => d.ArchiveDocumentId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_task_documents_archive_documents");

                entity.HasOne(d => d.CreatedByUser).WithMany()
                    .HasForeignKey(d => d.CreatedBy)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_task_documents_created_by");
            });

            modelBuilder.Entity<TaskHistory>(entity =>
            {
                entity.HasKey(e => e.Id).HasName("PK__task_his__3213E83F10DB968A");

                entity.ToTable("task_history");

                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.Action)
                    .HasMaxLength(50)
                    .HasColumnName("action");
                entity.Property(e => e.ChangeDescription)
                    .HasMaxLength(500)
                    .HasColumnName("change_description");
                entity.Property(e => e.ChangeType)
                    .HasMaxLength(50)
                    .HasColumnName("change_type");
                entity.Property(e => e.ChangedAt).HasColumnName("changed_at");
                entity.Property(e => e.ChangedBy).HasColumnName("changed_by");
                entity.Property(e => e.Details).HasColumnName("details");
                entity.Property(e => e.NewValue).HasColumnName("new_value");
                entity.Property(e => e.OldValue).HasColumnName("old_value");
                entity.Property(e => e.TaskId).HasColumnName("task_id");
                entity.Property(e => e.Timestamp).HasColumnName("timestamp");
                entity.Property(e => e.UserId).HasColumnName("user_id");

                entity.HasOne(d => d.ChangedByNavigation).WithMany(p => p.TaskHistoryChangedByNavigations)
                    .HasForeignKey(d => d.ChangedBy)
                    .HasConstraintName("FK_task_history_changed_by_users");

                entity.HasOne(d => d.Task).WithMany(p => p.TaskHistories)
                    .HasForeignKey(d => d.TaskId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_history_tasks");

                entity.HasOne(d => d.User).WithMany(p => p.TaskHistoryUsers)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_history_users");
            });

            modelBuilder.Entity<TaskProgressTracker>(entity =>
            {
                entity.HasKey(e => e.Id).HasName("PK__task_pro__3213E83F458A5CEF");

                entity.ToTable("task_progress_trackers");

                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.Notes).HasColumnName("notes");
                entity.Property(e => e.Progress).HasColumnName("progress");
                entity.Property(e => e.ProgressPercentage)
                    .HasColumnType("decimal(5, 2)")
                    .HasColumnName("progress_percentage");
                entity.Property(e => e.TaskId).HasColumnName("task_id");
                entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
                entity.Property(e => e.UpdatedBy).HasColumnName("updated_by");

                entity.HasOne(d => d.Task).WithMany(p => p.TaskProgressTrackers)
                    .HasForeignKey(d => d.TaskId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_progress_trackers_tasks");

                entity.HasOne(d => d.UpdatedByNavigation).WithMany(p => p.TaskProgressTrackers)
                    .HasForeignKey(d => d.UpdatedBy)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_progress_trackers_users");
            });

            modelBuilder.Entity<TaskType>(entity =>
            {
                entity.HasKey(e => e.Id).HasName("PK__task_typ__3213E83F111FA7F0");

                entity.ToTable("task_types");

                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.Color)
                    .HasMaxLength(20)
                    .HasColumnName("color");
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.Description).HasColumnName("description");
                entity.Property(e => e.Icon)
                    .HasMaxLength(50)
                    .HasColumnName("icon");
                entity.Property(e => e.IsDefault).HasColumnName("is_default");
                entity.Property(e => e.Name)
                    .HasMaxLength(100)
                    .HasColumnName("name");
            });

            modelBuilder.Entity<TimeTrackingEntry>(entity =>
            {
                entity.HasKey(e => e.Id).HasName("PK__time_tra__3213E83F4BCA1322");

                entity.ToTable("time_tracking_entries");

                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.Description).HasColumnName("description");
                entity.Property(e => e.Duration).HasColumnName("duration");
                entity.Property(e => e.EndTime).HasColumnName("end_time");
                entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
                entity.Property(e => e.StartTime).HasColumnName("start_time");
                entity.Property(e => e.TaskId).HasColumnName("task_id");
                entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
                entity.Property(e => e.UserId).HasColumnName("user_id");

                entity.HasOne(d => d.Task).WithMany(p => p.TimeTrackingEntries)
                    .HasForeignKey(d => d.TaskId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_time_tracking_entries_task_id_tasks");

                entity.HasOne(d => d.User).WithMany(p => p.TimeTrackingEntries)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_time_tracking_entries_user_id_users");
            });

            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.Id).HasName("PK__users__3213E83FC03D7CC9");

                entity.ToTable("users");

                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.DepartmentId).HasColumnName("department_id");
                entity.Property(e => e.Email)
                    .HasMaxLength(100)
                    .HasColumnName("email");
                entity.Property(e => e.FirstName)
                    .HasMaxLength(50)
                    .HasColumnName("first_name");
                entity.Property(e => e.IsActive)
                    .HasDefaultValue(true)
                    .HasColumnName("is_active");
                entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
                entity.Property(e => e.IsOnline).HasColumnName("is_online");
                entity.Property(e => e.LastLogin).HasColumnName("last_login");
                entity.Property(e => e.LastName)
                    .HasMaxLength(50)
                    .HasColumnName("last_name");
                entity.Property(e => e.LastSeen).HasColumnName("last_seen");
                entity.Property(e => e.Name)
                    .HasMaxLength(100)
                    .HasColumnName("name");
                entity.Property(e => e.Password)
                    .HasMaxLength(255)
                    .HasColumnName("password");
                entity.Property(e => e.ProfileImage)
                    .HasMaxLength(255)
                    .HasColumnName("profile_image");
                entity.Property(e => e.RoleId).HasColumnName("role_id");
                entity.Property(e => e.Username)
                    .HasMaxLength(50)
                    .HasColumnName("username");

                entity.HasOne(d => d.Department).WithMany(p => p.Users)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_users_departments");

                entity.HasOne(d => d.Role)
                    .WithMany(p => p.Users)
                    .HasForeignKey(d => d.RoleId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("FK_users_roles");

                // تجاهل navigation property ChatGroups لمنع إنشاء foreign key تلقائي
                entity.Ignore(e => e.ChatGroups);
            });

            modelBuilder.Entity<UserPermission>(entity =>
            {
                entity.HasKey(e => e.Id).HasName("PK__user_per__3213E83F9769948E");

                entity.ToTable("user_permissions");

                entity.HasIndex(e => new { e.UserId, e.PermissionId }, "UQ_user_permission").IsUnique();

                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.ExpiresAt).HasColumnName("expires_at");
                entity.Property(e => e.GrantedAt).HasColumnName("granted_at");
                entity.Property(e => e.GrantedBy).HasColumnName("granted_by");
                entity.Property(e => e.IsActive)
                    .HasDefaultValue(true)
                    .HasColumnName("is_active");
                entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
                entity.Property(e => e.PermissionId).HasColumnName("permission_id");
                entity.Property(e => e.UserId).HasColumnName("user_id");

                entity.HasOne(d => d.GrantedByNavigation).WithMany(p => p.UserPermissionGrantedByNavigations)
                    .HasForeignKey(d => d.GrantedBy)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_user_permissions_granted_by_users");

                entity.HasOne(d => d.Permission).WithMany(p => p.UserPermissions)
                    .HasForeignKey(d => d.PermissionId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_user_permissions_permission_id_permissions");

                entity.HasOne(d => d.User).WithMany(p => p.UserPermissions)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_user_permissions_user_id_users");
            });

            modelBuilder.Entity<ActivityLog>(entity =>
            {
                entity.HasKey(e => e.Id).HasName("PK_activity_logs");

                entity.ToTable("activity_logs");

                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.TaskId).HasColumnName("task_id");
                entity.Property(e => e.UserId).HasColumnName("user_id");
                entity.Property(e => e.Action)
                    .HasMaxLength(100)
                    .HasColumnName("action");
                entity.Property(e => e.Details).HasColumnName("details");
                entity.Property(e => e.Timestamp).HasColumnName("timestamp");
                entity.Property(e => e.ChangeType)
                    .HasMaxLength(50)
                    .HasColumnName("change_type");
                entity.Property(e => e.ChangeDescription)
                    .HasMaxLength(500)
                    .HasColumnName("change_description");
                entity.Property(e => e.OldValue).HasColumnName("old_value");
                entity.Property(e => e.NewValue).HasColumnName("new_value");
                entity.Property(e => e.ChangedBy).HasColumnName("changed_by");
                entity.Property(e => e.ChangedAt).HasColumnName("changed_at");

                entity.HasOne(d => d.Task).WithMany(p => p.ActivityLogs)
                    .HasForeignKey(d => d.TaskId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_activity_logs_tasks");

                entity.HasOne(d => d.User).WithMany(p => p.ActivityLogs)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_activity_logs_users");

                entity.HasOne(d => d.ChangedByNavigation).WithMany(p => p.ActivityLogsChangedBy)
                    .HasForeignKey(d => d.ChangedBy)
                    .OnDelete(DeleteBehavior.NoAction)
                    .HasConstraintName("FK_activity_logs_changed_by");
            });

            // تكوين نموذج TaskMessage
            modelBuilder.Entity<TaskMessage>(entity =>
            {
                entity.HasKey(e => e.Id).HasName("PK_task_messages");

                entity.ToTable("task_messages");

                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.TaskId).HasColumnName("task_id");
                entity.Property(e => e.SenderId).HasColumnName("sender_id");
                entity.Property(e => e.Content).HasColumnName("content");
                entity.Property(e => e.ContentType).HasColumnName("content_type");
                entity.Property(e => e.ReplyToMessageId).HasColumnName("reply_to_message_id");
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
                entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
                entity.Property(e => e.IsRead).HasColumnName("is_read");
                entity.Property(e => e.IsPinned).HasColumnName("is_pinned");
                entity.Property(e => e.PinnedAt).HasColumnName("pinned_at");
                entity.Property(e => e.PinnedBy).HasColumnName("pinned_by");
                entity.Property(e => e.Priority).HasColumnName("priority");
                entity.Property(e => e.IsMarkedForFollowUp).HasColumnName("is_marked_for_follow_up");
                entity.Property(e => e.FollowUpAt).HasColumnName("follow_up_at");
                entity.Property(e => e.MarkedForFollowUpBy).HasColumnName("marked_for_follow_up_by");
                entity.Property(e => e.IsEdited).HasColumnName("is_edited");
                entity.Property(e => e.MentionedUserIds).HasColumnName("mentioned_user_ids");
                entity.Property(e => e.AttachmentIds).HasColumnName("attachment_ids");

                // العلاقات
                entity.HasOne(d => d.Task).WithMany(p => p.TaskMessages)
                    .HasForeignKey(d => d.TaskId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_task_messages_tasks");

                entity.HasOne(d => d.Sender).WithMany(p => p.TaskMessagesSent)
                    .HasForeignKey(d => d.SenderId)
                    .OnDelete(DeleteBehavior.NoAction)
                    .HasConstraintName("FK_task_messages_sender");

                entity.HasOne(d => d.ReplyToMessage).WithMany(p => p.Replies)
                    .HasForeignKey(d => d.ReplyToMessageId)
                    .OnDelete(DeleteBehavior.NoAction)
                    .HasConstraintName("FK_task_messages_reply_to");

                entity.HasOne(d => d.PinnedByNavigation).WithMany(p => p.TaskMessagesPinned)
                    .HasForeignKey(d => d.PinnedBy)
                    .OnDelete(DeleteBehavior.NoAction)
                    .HasConstraintName("FK_task_messages_pinned_by");

                entity.HasOne(d => d.MarkedForFollowUpByNavigation).WithMany(p => p.TaskMessagesMarkedForFollowUp)
                    .HasForeignKey(d => d.MarkedForFollowUpBy)
                    .OnDelete(DeleteBehavior.NoAction)
                    .HasConstraintName("FK_task_messages_marked_by");
            });

            // تكوين نموذج TaskMessageRead
            modelBuilder.Entity<TaskMessageRead>(entity =>
            {
                entity.HasKey(e => e.Id).HasName("PK_task_message_reads");

                entity.ToTable("task_message_reads");

                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.MessageId).HasColumnName("message_id");
                entity.Property(e => e.UserId).HasColumnName("user_id");
                entity.Property(e => e.ReadAt).HasColumnName("read_at");
                entity.Property(e => e.IsDelivered).HasColumnName("is_delivered");
                entity.Property(e => e.DeliveredAt).HasColumnName("delivered_at");

                // قيد فريد لمنع التكرار
                entity.HasIndex(e => new { e.MessageId, e.UserId })
                    .IsUnique()
                    .HasDatabaseName("UQ_task_message_reads_message_user");

                // العلاقات
                entity.HasOne(d => d.Message).WithMany(p => p.MessageReads)
                    .HasForeignKey(d => d.MessageId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_task_message_reads_message");

                entity.HasOne(d => d.User).WithMany(p => p.TaskMessageReads)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_task_message_reads_user");
            });

        // تكوين جدول CustomRole
        modelBuilder.Entity<CustomRole>(entity =>
        {
            entity.ToTable("custom_roles");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Name).HasColumnName("name").HasMaxLength(100).IsRequired();
            entity.Property(e => e.Description).HasColumnName("description").HasMaxLength(255);
            entity.Property(e => e.ParentRoleId).HasColumnName("parent_role_id");
            entity.Property(e => e.CreatedBy).HasColumnName("created_by");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted").HasDefaultValue(false);

            entity.HasOne(e => e.Creator)
                .WithMany()
                .HasForeignKey(e => e.CreatedBy)
                .HasConstraintName("FK_custom_roles_created_by_users");

            entity.HasOne(e => e.ParentRole)
                .WithMany(e => e.ChildRoles)
                .HasForeignKey(e => e.ParentRoleId)
                .HasConstraintName("FK_custom_roles_parent");
        });

        // تكوين جدول CustomRolePermission
        modelBuilder.Entity<CustomRolePermission>(entity =>
        {
            entity.ToTable("custom_role_permissions");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.CustomRoleId).HasColumnName("custom_role_id");
            entity.Property(e => e.PermissionId).HasColumnName("permission_id");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.CreatedBy).HasColumnName("created_by");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted").HasDefaultValue(false);

            entity.HasOne(e => e.CustomRole)
                .WithMany(e => e.CustomRolePermissions)
                .HasForeignKey(e => e.CustomRoleId)
                .HasConstraintName("FK_custom_role_permissions_custom_role");

            entity.HasOne(e => e.Permission)
                .WithMany()
                .HasForeignKey(e => e.PermissionId)
                .HasConstraintName("FK_custom_role_permissions_permission");

            entity.HasOne(e => e.Creator)
                .WithMany()
                .HasForeignKey(e => e.CreatedBy)
                .HasConstraintName("FK_custom_role_permissions_created_by_users");
        });

        // تكوين جدول UserCustomRole
        modelBuilder.Entity<UserCustomRole>(entity =>
        {
            entity.ToTable("user_custom_roles");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.UserId).HasColumnName("user_id");
            entity.Property(e => e.CustomRoleId).HasColumnName("custom_role_id");
            entity.Property(e => e.AssignedAt).HasColumnName("assigned_at");
            entity.Property(e => e.IsDeleted).HasColumnName("is_deleted").HasDefaultValue(false);

            entity.HasOne(e => e.User)
                .WithMany(u => u.UserCustomRoles)
                .HasForeignKey(e => e.UserId)
                .HasConstraintName("FK_user_custom_roles_user");

            entity.HasOne(e => e.CustomRole)
                .WithMany(e => e.UserCustomRoles)
                .HasForeignKey(e => e.CustomRoleId)
                .HasConstraintName("FK_user_custom_roles_custom_role");
        });

        // تكوين جدول ScreenAction
        modelBuilder.Entity<ScreenAction>(entity =>
        {
            entity.ToTable("screen_actions");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.ScreenId).HasColumnName("screen_id");
            entity.Property(e => e.ActionId).HasColumnName("action_id");
            entity.Property(e => e.IsActive).HasColumnName("is_active").HasDefaultValue(true);
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");

            entity.HasIndex(e => new { e.ScreenId, e.ActionId }).IsUnique().HasDatabaseName("UQ_screen_action");

            entity.HasOne(e => e.Screen)
                .WithMany(s => s.ScreenActions)
                .HasForeignKey(e => e.ScreenId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_screen_actions_screen");

            entity.HasOne(e => e.Action)
                .WithMany(a => a.ScreenActions)
                .HasForeignKey(e => e.ActionId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_screen_actions_action");
        });

        // تكوين جدول Role
        modelBuilder.Entity<Role>(entity =>
        {
            entity.ToTable("roles");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Name).HasColumnName("name").HasMaxLength(100).IsRequired();
            entity.Property(e => e.DisplayName).HasColumnName("display_name").HasMaxLength(150).IsRequired();
            entity.Property(e => e.Description).HasColumnName("description").HasMaxLength(255);
            entity.Property(e => e.Level).HasColumnName("level");
            entity.Property(e => e.IsSystemRole).HasColumnName("is_system_role").HasDefaultValue(false);
            entity.Property(e => e.IsActive).HasColumnName("is_active").HasDefaultValue(true);
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
            entity.Property(e => e.CreatedBy).HasColumnName("created_by");
            entity.Property(e => e.UpdatedBy).HasColumnName("updated_by");

            entity.HasMany(e => e.Users)
                .WithOne(u => u.Role)
                .HasForeignKey(u => u.RoleId)
                .HasConstraintName("FK_users_roles");

            entity.HasMany(e => e.RoleDefaultPermissions)
                .WithOne(rp => rp.Role)
                .HasForeignKey(rp => rp.RoleId)
                .HasConstraintName("FK_role_default_permissions_roles");
        });
        // تكوين جدول RoleDefaultPermission
        modelBuilder.Entity<RoleDefaultPermission>(entity =>
        {
            entity.ToTable("role_default_permissions");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.RoleId).HasColumnName("role_id");
            entity.Property(e => e.PermissionId).HasColumnName("permission_id");
            entity.Property(e => e.IsActive).HasColumnName("is_active");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");

            entity.HasOne(e => e.Role)
                .WithMany(r => r.RoleDefaultPermissions)
                .HasForeignKey(e => e.RoleId)
                .HasConstraintName("FK_role_default_permissions_roles");

            entity.HasOne(e => e.Permission)
                .WithMany()
                .HasForeignKey(e => e.PermissionId)
                .HasConstraintName("FK_role_default_permissions_permissions");
        });
        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
