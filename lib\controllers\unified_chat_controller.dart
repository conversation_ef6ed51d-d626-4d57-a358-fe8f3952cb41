import 'package:flutter/material.dart';
import 'package:flutter_application_2/controllers/auth_controller.dart';
import 'package:get/get.dart';
import '../models/chat_models.dart';
import '../services/api/chat_groups_api_service.dart';
import '../services/api/messages_api_service.dart';
// import '../services/signalr_service.dart'; // معلق مؤقتاً
import '../services/unified_signalr_service.dart'; // الخدمة الموحدة الجديدة
import '../utils/logger.dart'; // استيراد Logger

/// متحكم الدردشة الموحد
/// يجمع بين إدارة المجموعات والرسائل في واجهة واحدة
class UnifiedChatController extends GetxController {
  final ChatGroupsApiService _chatGroupsApiService = ChatGroupsApiService();
  final MessagesApiService _messagesApiService = MessagesApiService();
  final UnifiedSignalRService _signalRService = Get.find<UnifiedSignalRService>(); // الحصول على مثيل الخدمة الموحدة

  // المجموعة الحالية
  final Rx<ChatGroup?> _currentChatGroup = Rx<ChatGroup?>(null);
  
  // قائمة الرسائل
  final RxList<Message> _messages = <Message>[].obs;
  
  // قائمة المجموعات
  final RxList<ChatGroup> _chatGroups = <ChatGroup>[].obs;

  // إدارة الكتابة في المجموعات
  final RxMap<int, RxList<String>> _typingUsers = <int, RxList<String>>{}.obs;

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxBool _isLoadingMessages = false.obs;
  final RxString _error = ''.obs;
  
  // البحث والمرشحات
  final RxString _searchQuery = ''.obs;
  final RxList<Message> _filteredMessages = <Message>[].obs;

  // متغير لتتبع ScrollController من الواجهة
  ScrollController? _scrollController;

  /// تعيين ScrollController من الواجهة
  void setScrollController(ScrollController? controller) {
    _scrollController = controller;
  }

  // Getters
  ChatGroup? get currentChatGroup => _currentChatGroup.value;
  List<Message> get messages => _messages;
  List<Message> get filteredMessages => _filteredMessages;
  List<ChatGroup> get chatGroups => _chatGroups;
  bool get isLoading => _isLoading.value;
  bool get isLoadingMessages => _isLoadingMessages.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;

  @override
  void onInit() {
    super.onInit();
    loadChatGroups();
    _connectSignalR(); // الاتصال بـ SignalR عند تهيئة المتحكم
    // الاشتراك في بث الرسائل الجديدة من SignalR
    _signalRService.newMessageStream.listen(_handleReceivedMessage);
    AppLogger.debug('تم الاشتراك في بث رسائل SignalR');
  }

  @override
  void onClose() {
    _disconnectSignalR(); // قطع الاتصال بـ SignalR عند إغلاق المتحكم
    // لا حاجة لإلغاء الاشتراك يدويًا مع GetX Rx unless using worker
    super.onClose();
  }

  /// الاتصال بخدمة SignalR
  void _connectSignalR() {
    _signalRService.connect();
    AppLogger.debug('تم محاولة الاتصال بخدمة SignalR الموحدة');
  }

  /// قطع الاتصال بخدمة SignalR
  void _disconnectSignalR() {
    _signalRService.disconnect();
    AppLogger.debug('تم قطع الاتصال بخدمة SignalR الموحدة');
  }

  /// معالجة الرسائل الواردة من SignalR
  void _handleReceivedMessage(Message? message) {
    if (message != null && _currentChatGroup.value != null && message.groupId == _currentChatGroup.value!.id) {
      // التحقق من عدم وجود الرسالة مسبقاً لتجنب التكرار
      final existingMessageIndex = _messages.indexWhere((m) => m.id == message.id);
      if (existingMessageIndex == -1) {
        // جدولة تحديث الواجهة ليتم بعد اكتمال إطار البناء الحالي
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // إضافة الرسالة إلى قائمة الرسائل إذا كانت تنتمي للمجموعة الحالية
          _messages.add(message);
          _applyMessageFilters(); // إعادة تطبيق المرشحات لتضمين الرسالة الجديدة

          // التمرير التلقائي إلى أسفل عند وصول رسالة جديدة
          // تأخير إضافي للتأكد من اكتمال تحديث الواجهة
          Future.delayed(const Duration(milliseconds: 50), () {
            _scrollToBottomAfterNewMessage();
          });

          AppLogger.debug('تم استلام رسالة جديدة عبر SignalR للمجموعة ${_currentChatGroup.value!.id}');
        });
      } else {
        AppLogger.debug('تم تجاهل رسالة مكررة: ${message.id}');
      }
    } else if (message != null) {
      AppLogger.debug('تم استلام رسالة لمجموعة غير نشطة: ${message.groupId}');
    }
  }


  /// تحميل قائمة مجموعات الدردشة
  Future<void> loadChatGroups() async {
    // جدولة تحديث حالة التحميل ليتم بعد اكتمال إطار البناء الحالي
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _isLoading.value = true;
    });
    _error.value = '';

    try {
      final groups = await _chatGroupsApiService.getAllGroups();
      // جدولة تحديث قائمة المجموعات ليتم بعد اكتمال إطار البناء الحالي
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _chatGroups.assignAll(groups);
        debugPrint('تم تحميل ${groups.length} مجموعة دردشة');
      });
    } catch (e) {
      _error.value = 'خطأ في تحميل مجموعات الدردشة: $e';
      debugPrint('خطأ في تحميل مجموعات الدردشة: $e');
    } finally {
      // جدولة تحديث حالة التحميل ليتم بعد اكتمال إطار البناء الحالي
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _isLoading.value = false;
      });
    }
  }

  /// تعيين المجموعة الحالية وتحميل رسائلها والانضمام إلى مجموعة SignalR
  Future<void> setCurrentChatGroup(ChatGroup chatGroup) async {
    try {
      // مغادرة المجموعة السابقة في SignalR إذا كانت موجودة
      if (_currentChatGroup.value != null) {
        _signalRService.leaveChatGroup(_currentChatGroup.value!.id.toString());
      }

      _currentChatGroup.value = chatGroup;

      // تحميل الرسائل مع معالجة الأخطاء
      await loadMessages(chatGroup.id);

      // الانضمام إلى مجموعة SignalR الجديدة
      await _signalRService.joinChatGroup(chatGroup.id.toString());

      AppLogger.debug('تم تعيين المجموعة الحالية والانضمام إلى مجموعة SignalR: ${chatGroup.id}');
    } catch (e) {
      AppLogger.error('خطأ في تعيين المجموعة الحالية: $e');
      _error.value = 'خطأ في تحميل المحادثة: $e';
    }
  }

  /// تحميل رسائل المجموعة
  Future<void> loadMessages(int groupId) async {
    // جدولة تحديث حالة التحميل ليتم بعد اكتمال إطار البناء الحالي
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _isLoadingMessages.value = true;
    });
    _error.value = '';

    try {
      final messages = await _messagesApiService.getGroupMessages(groupId);
      // جدولة تحديث قائمة الرسائل ليتم بعد اكتمال إطار البناء الحالي
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _messages.assignAll(messages);
        // جدولة تطبيق المرشحات ليتم بعد اكتمال إطار البناء الحالي
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _applyMessageFilters();
        });
        debugPrint('تم تحميل ${messages.length} رسالة للمجموعة $groupId');
      });
    } catch (e) {
      _error.value = 'خطأ في تحميل الرسائل: $e';
      debugPrint('خطأ في تحميل الرسائل: $e');
    } finally {
      // جدولة تحديث حالة التحميل ليتم بعد اكتمال إطار البناء الحالي
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _isLoadingMessages.value = false;
      });
    }
  }

  /// إرسال رسالة جديدة
  Future<bool> sendMessage({
    required String content,
    int? replyToMessageId,
    List<String> mentionedUserIds = const [],
  }) async {
    if (_currentChatGroup.value == null) {
      _error.value = 'لا توجد مجموعة محددة';
      return false;
    }

    try {
      // الحصول على معرف المستخدم الحالي
      final currentUser = Get.find<AuthController>().currentUser.value;
      if (currentUser == null) {
        _error.value = 'لم يتم تسجيل الدخول';
        return false;
      }

      final message = Message(
        id: 0, // سيتم تعيينه من الخادم
        content: content,
        senderId: currentUser.id, // استخدام معرف المستخدم الحالي
        groupId: _currentChatGroup.value!.id,
        createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        replyToMessageId: replyToMessageId,
        mentionedUserIds: mentionedUserIds,
      );

      // إرسال الرسالة عبر API (للحفظ في قاعدة البيانات)
      final newMessage = await _messagesApiService.sendMessage(message);

      // إرسال الرسالة عبر SignalR لبثها للعملاء الآخرين
      // الباك إند سيقوم ببث الرسالة بعد حفظها، وسنستقبلها عبر SignalR
      _signalRService.sendMessageToHub(_currentChatGroup.value!.id.toString(), newMessage);

      // لا نضيف الرسالة محلياً هنا لتجنب التكرار
      // ستصل الرسالة عبر SignalR وستتم إضافتها في _handleReceivedMessage

      // التمرير التلقائي إلى أسفل بعد إرسال رسالة (دائماً للمرسل)
      Future.delayed(const Duration(milliseconds: 150), () {
        _scrollToBottomAfterSendingMessage();
      });

      debugPrint('تم إرسال الرسالة بنجاح عبر API');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إرسال الرسالة: $e';
      debugPrint('خطأ في إرسال الرسالة: $e');
      return false;
    }
  }

  /// حذف رسالة
  Future<bool> deleteMessage(int messageId) async {
    try {
      await _messagesApiService.deleteMessage(messageId);
      _messages.removeWhere((message) => message.id == messageId);
      _applyMessageFilters();
      debugPrint('تم حذف الرسالة بنجاح');
      // TODO: بث حدث حذف الرسالة عبر SignalR
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف الرسالة: $e';
      debugPrint('خطأ في حذف الرسالة: $e');
      return false;
    }
  }

  /// تحديث رسالة
  Future<bool> updateMessage(int messageId, String newContent) async {
    try {
      final messageIndex = _messages.indexWhere((m) => m.id == messageId);
      if (messageIndex == -1) return false;

      final updatedMessage = _messages[messageIndex].copyWith(
        content: newContent,
        updatedAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      );

      final result = await _messagesApiService.updateMessage(messageId, updatedMessage);
      _messages[messageIndex] = result;
      _applyMessageFilters();
      debugPrint('تم تحديث الرسالة بنجاح');
      // TODO: بث حدث تحديث الرسالة عبر SignalR
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث الرسالة: $e';
      debugPrint('خطأ في تحديث الرسالة: $e');
      return false;
    }
  }

  /// البحث في الرسائل
  void searchMessages(String query) {
    _searchQuery.value = query;
    _applyMessageFilters();
  }

  /// تطبيق مرشحات الرسائل
  void _applyMessageFilters() {
    if (_searchQuery.value.isEmpty) {
      _filteredMessages.assignAll(_messages);
    } else {
      final query = _searchQuery.value.toLowerCase();
      final filtered = _messages.where((message) {
        return message.content.toLowerCase().contains(query);
      }).toList();
      _filteredMessages.assignAll(filtered);
    }
  }

  /// مسح البحث
  void clearSearch() {
    _searchQuery.value = '';
    _applyMessageFilters();
  }

  /// الحصول على المستخدمين الذين يكتبون في مجموعة محددة
  List<String> getTypingUsers(int groupId) {
    return _typingUsers[groupId]?.toList() ?? [];
  }

  /// إضافة مستخدم لقائمة الكتابة في المجموعة
  void addTypingUser(int groupId, String userName) {
    if (!_typingUsers.containsKey(groupId)) {
      _typingUsers[groupId] = <String>[].obs;
    }
    if (!_typingUsers[groupId]!.contains(userName)) {
      _typingUsers[groupId]!.add(userName);
    }
    update(); // تحديث الواجهة فوراً
  }

  /// إزالة مستخدم من قائمة الكتابة في المجموعة
  void removeTypingUser(int groupId, String userName) {
    if (_typingUsers.containsKey(groupId)) {
      _typingUsers[groupId]!.remove(userName);
    }
    update(); // تحديث الواجهة فوراً
  }

  /// تحديد جميع رسائل المجموعة كمقروءة
  Future<bool> markGroupMessagesAsRead(int groupId) async {
    try {
      final currentUser = Get.find<AuthController>().currentUser.value;
      if (currentUser == null) {
        _error.value = 'لم يتم تسجيل الدخول';
        return false;
      }

      // استدعاء API لتحديد جميع الرسائل كمقروءة
      final success = await _messagesApiService.markGroupMessagesAsRead(groupId);
      
      if (success) {
        AppLogger.debug('تم تحديد جميع رسائل المجموعة $groupId كمقروءة');
      }
      
      return success;
    } catch (e) {
      _error.value = 'خطأ في تحديد الرسائل كمقروءة: $e';
      AppLogger.error('خطأ في تحديد الرسائل كمقروءة', e);
      return false;
    }
  }

  /// تحديد رسالة محددة كمقروءة
  Future<bool> markMessageAsRead(int messageId) async {
    try {
      final currentUser = Get.find<AuthController>().currentUser.value;
      if (currentUser == null) {
        _error.value = 'لم يتم تسجيل الدخول';
        return false;
      }

      // استدعاء API لتحديد الرسالة كمقروءة
      final success = await _messagesApiService.markMessageAsRead(messageId);
      
      if (success) {
        AppLogger.debug('تم تحديد الرسالة $messageId كمقروءة');
      }
      
      return success;
    } catch (e) {
      _error.value = 'خطأ في تحديد الرسالة كمقروءة: $e';
      AppLogger.error('خطأ في تحديد الرسالة كمقروءة', e);
      return false;
    }
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// التمرير التلقائي إلى أسفل عند وصول رسالة جديدة
  /// محسن ليعمل مثل محادثات المهام
  void _scrollToBottomAfterNewMessage() {
    if (_scrollController == null || !_scrollController!.hasClients) {
      AppLogger.debug('ScrollController غير متاح للتمرير التلقائي في المحادثات العامة');
      return;
    }

    // تأخير قصير للتأكد من اكتمال تحديث القائمة
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_scrollController != null && _scrollController!.hasClients) {
        try {
          // التحقق من أن المستخدم قريب من أسفل القائمة قبل التمرير التلقائي
          final position = _scrollController!.position;
          final maxExtent = position.maxScrollExtent;
          final currentPosition = position.pixels;
          final threshold = 100.0; // المسافة المسموحة من الأسفل

          // إذا كان المستخدم قريب من الأسفل، قم بالتمرير التلقائي
          if (maxExtent - currentPosition <= threshold) {
            _scrollController!.animateTo(
              maxExtent,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
            AppLogger.debug('تم التمرير التلقائي إلى أسفل بعد وصول رسالة جديدة - المسافة: $maxExtent');
          } else {
            AppLogger.debug('المستخدم بعيد عن الأسفل، لم يتم التمرير التلقائي');
          }
        } catch (e) {
          AppLogger.debug('خطأ في التمرير التلقائي للمحادثات العامة: $e');
          // محاولة بديلة باستخدام jumpTo
          try {
            final maxExtent = _scrollController!.position.maxScrollExtent;
            _scrollController!.jumpTo(maxExtent);
            AppLogger.debug('تم التمرير البديل إلى: $maxExtent');
          } catch (e2) {
            AppLogger.debug('خطأ في التمرير البديل للمحادثات العامة: $e2');
          }
        }
      }
    });
  }

  /// التمرير التلقائي إلى أسفل بعد إرسال رسالة (دائماً للمرسل)
  void _scrollToBottomAfterSendingMessage() {
    if (_scrollController == null || !_scrollController!.hasClients) {
      AppLogger.debug('ScrollController غير متاح للتمرير بعد الإرسال');
      return;
    }

    try {
      // التمرير إلى أسفل دائماً عند إرسال رسالة
      final maxExtent = _scrollController!.position.maxScrollExtent;
      _scrollController!.animateTo(
        maxExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
      AppLogger.debug('تم التمرير التلقائي بعد إرسال رسالة - المسافة: $maxExtent');
    } catch (e) {
      AppLogger.debug('خطأ في التمرير بعد الإرسال: $e');
      // محاولة بديلة باستخدام jumpTo
      try {
        final maxExtent = _scrollController!.position.maxScrollExtent;
        _scrollController!.jumpTo(maxExtent);
      } catch (e2) {
        AppLogger.debug('خطأ في التمرير البديل بعد الإرسال: $e2');
      }
    }
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await loadChatGroups();
    if (_currentChatGroup.value != null) {
      await loadMessages(_currentChatGroup.value!.id);
    }
  }

  /// إنشاء مجموعة دردشة جديدة
  Future<bool> createChatGroup({
    required String name,
    String? description,
    bool isPrivate = false,
  }) async {
    try {
      final chatGroup = ChatGroup(
        id: 0, // سيتم تعيينه من الخادم
        name: name,
        description: description,
        isPrivate: isPrivate,
        createdBy: Get.find<AuthController>().currentUser.value?.id ?? 1,
        createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      );

      final newGroup = await _chatGroupsApiService.createGroup(chatGroup);
      if (newGroup != null) {
        _chatGroups.add(newGroup);
        debugPrint('تم إنشاء مجموعة دردشة جديدة: ${newGroup.name}');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في إنشاء مجموعة الدردشة: $e';
      debugPrint('خطأ في إنشاء مجموعة الدردشة: $e');
      return false;
    }
  }

  /// الانضمام إلى مجموعة
  Future<bool> joinGroup(int groupId) async {
    try {
      await _chatGroupsApiService.joinGroup(groupId, Get.find<AuthController>().currentUser.value?.id ?? 1);
      await loadChatGroups(); // إعادة تحميل المجموعات
      debugPrint('تم الانضمام إلى المجموعة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في الانضمام إلى المجموعة: $e';
      debugPrint('خطأ في الانضمام إلى المجموعة: $e');
      return false;
    }
  }

  /// مغادرة مجموعة
  Future<bool> leaveGroup(int groupId) async {
    try {
      await _chatGroupsApiService.leaveGroup(groupId, Get.find<AuthController>().currentUser.value?.id ?? 1);
      await loadChatGroups(); // إعادة تحميل المجموعات
      
      // إذا كانت المجموعة الحالية هي التي تم مغادرتها، قم بإلغاء تحديدها
      if (_currentChatGroup.value?.id == groupId) {
        _signalRService.leaveChatGroup(groupId.toString()); // مغادرة مجموعة SignalR
        _currentChatGroup.value = null;
        _messages.clear();
        _filteredMessages.clear();
      }
      
      debugPrint('تم مغادرة المجموعة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في مغادرة المجموعة: $e';
      debugPrint('خطأ في مغادرة المجموعة: $e');
      return false;
    }
  }
}
