//===================================================================================
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_application_2/constants/app_colors.dart';
import 'package:flutter_application_2/constants/app_styles.dart';
import 'package:flutter_application_2/controllers/auth_controller.dart';
// تم إزالة استيراد dashboard_controller لأنه غير مستخدم
import 'package:flutter_application_2/controllers/dashboard_layout_controller.dart';
import 'package:flutter_application_2/controllers/department_controller.dart';
import 'package:flutter_application_2/controllers/task_controller.dart';
import 'package:flutter_application_2/controllers/user_controller.dart';
import 'package:flutter_application_2/models/advanced_filter_options.dart';
import 'package:flutter_application_2/models/chart_enums.dart';
import 'package:flutter_application_2/models/dashboard_widget_model.dart' as widget_model;
import 'package:flutter_application_2/screens/widgets/charts/enhanced_line_chart.dart';
import 'package:flutter_application_2/screens/home/<USER>';

import 'package:flutter_application_2/routes/app_routes.dart';
import 'package:flutter_application_2/services/export_services/chart_export_service.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:uuid/uuid.dart';


import '../widgets/charts/enhanced_pie_chart.dart';
import '../widgets/charts/enhanced_radar_chart.dart';
import '../widgets/charts/unified_filter_export_widget.dart';
import '../widgets/charts/enhanced_bar_chart.dart';
import '../widgets/charts/enhanced_bubble_chart.dart';
import '../widgets/charts/enhanced_heatmap_chart.dart';
import '../widgets/charts/enhanced_treemap_chart.dart';
import '../widgets/charts/enhanced_funnel_chart.dart';
import '../widgets/charts/enhanced_sankey_chart.dart';
import '../widgets/charts/enhanced_stacked_bar_chart.dart';
import '../widgets/charts/gantt_chart.dart';
import '../widgets/charts/user_tasks_status_chart.dart';
import '../widgets/charts/chart_customization_panel.dart';
import '../widgets/dashboard/monday_style_add_widget_dialog.dart';
import '../../models/task_model.dart';
import '../../models/user_model.dart';
import '../../models/department_model.dart';



/// نموذج عنصر لوحة المعلومات
class DashboardItem {
  final String id;
  final String title;
  final Widget content;
  final ChartType? chartType;
  final AdvancedFilterOptions? filterOptions;

  const DashboardItem({
    required this.id,
    required this.title,
    required this.content,
    this.chartType,
    this.filterOptions,
  });

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'chartType': chartType?.index,
      'filterOptions': filterOptions?.toJson(),
    };
  }

  /// إنشاء من JSON
  factory DashboardItem.fromJson(Map<String, dynamic> json) {
    return DashboardItem(
      id: json['id'],
      title: json['title'],
      content: Container(), // سيتم استبداله لاحقاً
      chartType: json['chartType'] != null ? ChartType.values[json['chartType']] : null,
      filterOptions: json['filterOptions'] != null
          ? AdvancedFilterOptions.fromJson(json['filterOptions'])
          : null,
    );
  }
}

/// مستودع لوحة المعلومات المؤقت
class DashboardRepository {
  static final Map<String, dynamic> _cache = {};

  Future<List<Map<String, dynamic>>> getDashboardLayout(String userId) async {
    // إرجاع قائمة فارغة مؤقتاً
    return [];
  }

  Future<Map<String, dynamic>?> getChartData(String key) async {
    return _cache[key];
  }

  Future<void> saveChartData(String key, Map<String, dynamic> data) async {
    _cache[key] = data;
  }
}

/// أنواع عناصر لوحة المعلومات
enum DashboardWidgetType {
  barChart,
  lineChart,
  pieChart,
  table,
  kpi,
}

/// حالات المهام (enum مؤقت للتوافق مع dashboard_tab.dart)
enum DashboardTaskStatus {
  pending(1, 'معلقة'),
  inProgress(2, 'قيد التنفيذ'),
  waitingForInfo(3, 'في انتظار معلومات'),
  completed(4, 'مكتملة'),
  cancelled(5, 'ملغاة'),
  news(6, 'جديدة');

  const DashboardTaskStatus(this.id, this.name);
  final int id;
  final String name;
}

// تم إزالة نموذج القسم المؤقت - سيتم استخدام النموذج الأصلي من department_model.dart

/// إضافة خصائص مفقودة لـ AdvancedFilterOptions
extension AdvancedFilterOptionsExtension on AdvancedFilterOptions {
  List<String>? get assigneeIds => null;
  List<String>? get creatorIds => null;
  List<String>? get priorityFilter => null;
  List<DashboardTaskStatus>? get statusFilter => null;

  Map<String, dynamic> toJson() {
    return {
      'departmentIds': departmentIds,
      'assigneeIds': assigneeIds,
      'creatorIds': creatorIds,
      'priorityFilter': priorityFilter,
      'statusFilter': statusFilter?.map((e) => e.index).toList(),
    };
  }

  static AdvancedFilterOptions fromJson(Map<String, dynamic> json) {
    return AdvancedFilterOptions(
      departmentIds: json['departmentIds']?.cast<String>(),
    );
  }
}

/// إضافة خصائص مفقودة للمتحكمات
extension TaskControllerExtension on TaskController {
  List<Task> get tasks => allTasks;

  Future<void> loadTasksByUserPermissions(int userId) async {
    // تحميل المهام الحقيقية للمستخدم
    debugPrint('📋 تحميل المهام للمستخدم: $userId');
    await loadAllTasks(); // استخدام الطريقة الحقيقية
    debugPrint('✅ تم تحميل المهام');
  }
}

extension DepartmentControllerExtension on DepartmentController {
  List<Department> get departments => allDepartments;

  Future<void> loadDepartments() async {
    // تحميل الأقسام الحقيقية
    debugPrint('🏢 تحميل الأقسام');
    await loadAllDepartments(); // استخدام الطريقة الحقيقية
    debugPrint('✅ تم تحميل الأقسام');
  }
}

extension DashboardLayoutControllerExtension on DashboardLayoutController {
  List<DashboardItem> get dashboardItems => [];

  Future<void> loadSavedLayout() async {
    // تنفيذ مؤقت - محاكاة تحميل التخطيط
    debugPrint('💾 محاكاة تحميل التخطيط المحفوظ');
    await Future.delayed(const Duration(milliseconds: 200)); // محاكاة تأخير الشبكة
    debugPrint('✅ تم تحميل التخطيط (محاكاة)');
  }

  Future<void> saveDashboardLayout(List<Map<String, dynamic>> layout) async {
    // تنفيذ مؤقت - محاكاة حفظ التخطيط
    debugPrint('💾 محاكاة حفظ التخطيط: ${layout.length} عنصر');
    await Future.delayed(const Duration(milliseconds: 100)); // محاكاة تأخير الشبكة
    debugPrint('✅ تم حفظ التخطيط (محاكاة)');
  }
}

extension ChartExportServiceExtension on ChartExportService {
  Future<String> exportToImage(String itemId, {String? title, GlobalKey? globalKey}) async {
    // تنفيذ مؤقت
    return '';
  }
}



/// نموذج مهمة لمخطط Gantt
class CustomGanttTask {
  /// معرف المهمة
  final String id;

  /// عنوان المهمة
  final String title;

  /// تاريخ بداية المهمة
  final DateTime startDate;

  /// تاريخ نهاية المهمة
  final DateTime endDate;

  /// نسبة إكمال المهمة (0-100)
  final double completionPercentage;

  /// لون المهمة (اختياري)
  final Color? color;

  /// إنشاء مهمة لمخطط Gantt
  const CustomGanttTask({
    required this.id,
    required this.title,
    required this.startDate,
    required this.endDate,
    this.completionPercentage = 0,
    this.color,
  });

  /// تحويل إلى GanttTask من مكتبة gantt_chart.dart
  GanttTask toGanttTask() {
    return GanttTask(
      id: id,
      title: title,
      startDate: startDate,
      endDate: endDate,
      completionPercentage: completionPercentage,
      color: color,
    );
  }
}



/// مكون التقويم المصغر
class MiniCalendarWidget extends StatelessWidget {
  final DateTime initialDate;
  final DateTime? startDate;
  final DateTime? endDate;
  final Function(DateTime) onDateSelected;

  const MiniCalendarWidget({
    super.key,
    required this.initialDate,
    this.startDate,
    this.endDate,
    required this.onDateSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildCalendarHeader(),
            const SizedBox(height: 8),
            _buildWeekdaysRow(),
            const SizedBox(height: 8),
            _buildCalendarGrid(),
          ],
        ),
      ),
    );
  }

  /// بناء رأس التقويم مع أزرار التنقل
  Widget _buildCalendarHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        IconButton(
          icon: const Icon(Icons.chevron_left),
          onPressed: () {
            // منطق التنقل إلى الشهر السابق
            final previousMonth =
                DateTime(initialDate.year, initialDate.month - 1, 1);
            onDateSelected(previousMonth);
          },
        ),
        Text(
          DateFormat.yMMMM('ar').format(initialDate),
          style: AppStyles.titleMedium,
        ),
        IconButton(
          icon: const Icon(Icons.chevron_right),
          onPressed: () {
            // منطق التنقل إلى الشهر التالي
            final nextMonth =
                DateTime(initialDate.year, initialDate.month + 1, 1);
            onDateSelected(nextMonth);
          },
        ),
      ],
    );
  }

  /// بناء صف أيام الأسبوع
  Widget _buildWeekdaysRow() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: ['أحد', 'اثنين', 'ثلاثاء', 'أربعاء', 'خميس', 'جمعة', 'سبت']
          .map((day) => Expanded(
                child: Text(
                  day,
                  style: AppStyles.caption,
                  textAlign: TextAlign.center,
                ),
              ))
          .toList(),
    );
  }

  /// بناء شبكة التقويم
  Widget _buildCalendarGrid() {
    // حساب عدد أيام الشهر
    final daysInMonth =
        DateTime(initialDate.year, initialDate.month + 1, 0).day;

    // حساب اليوم الأول من الشهر
    final firstDayOfMonth = DateTime(initialDate.year, initialDate.month, 1);
    final firstWeekdayOfMonth =
        firstDayOfMonth.weekday % 7; // تعديل لجعل الأحد هو اليوم 0

    // إجمالي عدد الخلايا المطلوبة (الأيام السابقة + أيام الشهر)
    final totalCells = firstWeekdayOfMonth + daysInMonth;

    // عدد الصفوف المطلوبة
    final rowCount = (totalCells / 7).ceil();

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 7,
        childAspectRatio: 1,
      ),
      itemCount: rowCount * 7, // عدد الخلايا في الشبكة
      itemBuilder: (context, index) {
        // حساب اليوم المقابل للخلية
        final adjustedIndex = index - firstWeekdayOfMonth;

        // التحقق مما إذا كانت الخلية خارج نطاق الشهر الحالي
        if (adjustedIndex < 0 || adjustedIndex >= daysInMonth) {
          return const SizedBox.shrink();
        }

        final day = adjustedIndex + 1;
        final currentDate = DateTime(initialDate.year, initialDate.month, day);

        // التحقق مما إذا كان اليوم ضمن النطاق المحدد
        final inRange =
            (startDate == null || !currentDate.isBefore(startDate!)) &&
                (endDate == null || !currentDate.isAfter(endDate!));

        final isToday = currentDate.year == DateTime.now().year &&
            currentDate.month == DateTime.now().month &&
            currentDate.day == DateTime.now().day;

        final isSelected = currentDate.day == initialDate.day;

        return InkWell(
          onTap: inRange ? () => onDateSelected(currentDate) : null,
          child: Container(
            alignment: Alignment.center,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isSelected
                  ? AppColors.primary
                  : (isToday
                      ? AppColors.primary.withValues(
                          red: AppColors.primary.r.toDouble(),
                          green: AppColors.primary.g.toDouble(),
                          blue: AppColors.primary.b.toDouble(),
                          alpha: 51.0)
                      : null), // 0.2 opacity = 51/255
            ),
            child: Text(
              day.toString(),
              style: TextStyle(
                fontSize: 12,
                color: isSelected
                    ? Colors.white
                    : inRange
                        ? AppColors.textPrimary
                        : AppColors.textSecondary,
                fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
        );
      },
    );
  }
}

// تم نقل نموذج GanttTask إلى ملف gantt_chart.dart

/// مكون مخطط جانت المحسن
class EnhancedGanttChart extends StatelessWidget {
  final List<GanttTask> tasks;
  final DateTime startDate;
  final DateTime endDate;
  final String title;

  const EnhancedGanttChart({
    super.key,
    required this.tasks,
    required this.startDate,
    required this.endDate,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    // حساب إجمالي عدد الأيام في النطاق
    final totalDays = endDate.difference(startDate).inDays + 1;

    // تحديد ارتفاع كل مهمة
    final taskHeight = 40.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان المخطط
        if (title.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(bottom: 16.0),
            child: Text(
              title,
              style: AppStyles.titleMedium,
            ),
          ),

        // شريط التاريخ
        _buildDateHeader(totalDays),

        // قائمة المهام
        Expanded(
          child: _buildTasksList(totalDays, taskHeight),
        ),
      ],
    );
  }

  /// بناء شريط التاريخ
  Widget _buildDateHeader(int totalDays) {
    return SizedBox(
      height: 30,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: totalDays,
        itemBuilder: (context, index) {
          final date = startDate.add(Duration(days: index));
          final isWeekend =
              date.weekday == 5 || date.weekday == 6; // الجمعة والسبت

          return Container(
            width: 60,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              color: isWeekend ? Colors.grey.shade200 : Colors.white,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  DateFormat('d').format(date),
                  style: const TextStyle(fontSize: 12),
                ),
                Text(
                  DateFormat('MMM', 'ar').format(date),
                  style: const TextStyle(fontSize: 10),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// بناء قائمة المهام
  Widget _buildTasksList(int totalDays, double taskHeight) {
    return ListView.builder(
      itemCount: tasks.length,
      itemBuilder: (context, index) {
        final task = tasks[index];

        // حساب موضع وعرض شريط المهمة
        final taskStartDiff = task.startDate.difference(startDate).inDays;
        final taskDuration = task.endDate.difference(task.startDate).inDays + 1;

        // التأكد من أن المهمة ضمن النطاق المعروض
        if (taskStartDiff + taskDuration < 0 || taskStartDiff > totalDays) {
          return const SizedBox.shrink();
        }

        // تعديل الموضع والعرض للمهام التي تبدأ قبل أو تنتهي بعد النطاق
        final adjustedStart = taskStartDiff < 0 ? 0 : taskStartDiff;
        final adjustedWidth = taskStartDiff < 0
            ? taskDuration + taskStartDiff
            : (taskStartDiff + taskDuration > totalDays
                ? totalDays - taskStartDiff
                : taskDuration);

        return _buildTaskItem(task, adjustedStart, adjustedWidth, taskHeight);
      },
    );
  }

  /// بناء عنصر المهمة
  Widget _buildTaskItem(
      GanttTask task, int adjustedStart, int adjustedWidth, double taskHeight) {
    return Container(
      height: taskHeight,
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Stack(
        children: [
          // اسم المهمة
          Positioned(
            left: 0,
            top: 0,
            bottom: 0,
            width: 150,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              alignment: Alignment.centerRight,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                color: Colors.grey.shade100,
              ),
              child: Text(
                task.title,
                style: const TextStyle(fontSize: 12),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),

          // شريط المهمة
          Positioned(
            left: 150 + (adjustedStart * 60),
            top: 0,
            height: taskHeight,
            width: adjustedWidth * 60,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                color: task.color ?? AppColors.primary,
              ),
              child: Center(
                child: Text(
                  task.title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ),

          // مؤشر نسبة الإنجاز
          if (task.completionPercentage > 0)
            Positioned(
              left: 150 + (adjustedStart * 60),
              top: taskHeight - 5,
              height: 3,
              width: adjustedWidth * 60 * (task.completionPercentage / 100),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(2),
                  color: Colors.white.withValues(
                      red: Colors.white.r.toDouble(),
                      green: Colors.white.g.toDouble(),
                      blue: Colors.white.b.toDouble(),
                      alpha: 178.5), // 0.7 opacity = 178.5/255
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// لوحة التحكم الرئيسية
class DashboardTab extends StatefulWidget {
  const DashboardTab({super.key});

  @override
  State<DashboardTab> createState() => _DashboardTabState();
}

class _DashboardTabState extends State<DashboardTab> with RouteAware {
  // المتحكمات
  late final TaskController _taskController;
  late final UserController _userController;
  late final DepartmentController _departmentController;
  // تم إزالة _dashboardController لأنه غير مستخدم
  late final ChartExportService _chartExportService;
  late final RouteObserver<PageRoute<dynamic>> _routeObserver;
  // تم التعليق على هذا المتغير لأنه غير مستخدم حاليًا
  // late final DatabaseHelper _databaseHelper;
  late final DashboardLayoutController _dashboardLayoutController;

  // تم إزالة ScrollController لحل مشكلة التضارب

  // عناصر لوحة التحكم
  List<DashboardItem> _dashboardItems = [];

  // حالة التحميل والخطأ
  bool _isLoading = false;
  String? _errorMessage;

  // تم تعليق هذا المتغير لأنه غير مستخدم حاليًا
  // final int _selectedItemSize = 1;

  // متغيرات التصفية
  final Map<String, DateTime?> _chartStartDates = {};
  final Map<String, DateTime?> _chartEndDates = {};
  final Map<String, TimeFilterType> _chartFilterTypes = {};
  final Map<String, ChartType> _chartTypes = {};
  final Map<String, AdvancedFilterOptions> _advancedFilterOptions = {};

  // مفاتيح عامة للمخططات لاستخدامها في تصدير الصور
  final Map<String, GlobalKey> _chartKeys = {};

  // متغير للتحكم في حالة تهيئة المخططات
  bool _isInitializing = true;

  @override
  void initState() {
    super.initState();

    // تهيئة المتحكمات أولاً
    _initializeControllers();

    // تهيئة الفلاتر الافتراضية
    _initializeDefaultFilters();

    // تهيئة قائمة فارغة لعناصر لوحة المعلومات
    _dashboardItems = [];
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // تسجيل المراقبة للتنقل بين الصفحات
    final route = ModalRoute.of(context);
    if (route is PageRoute) {
      _routeObserver.subscribe(this, route);
    }

    // إنشاء التخطيط وتحميل البيانات بعد أن يصبح السياق جاهز
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // إذا كانت عناصر لوحة المعلومات فارغة ولم يكن التحميل قيد التنفيذ، ابدأ التحميل.
        if (_dashboardItems.isEmpty && !_isLoading) {
          debugPrint('🚀 بدء تهيئة لوحة المعلومات وتحميل البيانات...');
          _createDefaultDashboardLayout();

          // تأخير لتحميل البيانات - فقط إذا لم يكن التحميل قيد التنفيذ
          Future.delayed(const Duration(milliseconds: 500), () {
            if (mounted && !_isLoading) {
              _loadData();
              debugPrint('📊 بدء تحميل البيانات بعد استقرار الواجهة');
            }
          });
        } else if (_dashboardItems.isNotEmpty) {
          // تحديث المحتوى الموجود
          debugPrint('🔄 تحديث المحتوى الموجود...');
          Future.microtask(() {
            if (mounted) {
              _updateDashboardItemsContent();
            }
          });
        }
        // Set _isInitializing to false after the initial check/load is triggered
        if (_isInitializing) {
           setState(() {
             _isInitializing = false;
           });
        }
      }
    });
  }

  @override
  void dispose() {
    _routeObserver.unsubscribe(this);
    // تم إزالة _scrollController.dispose() لحل مشكلة التضارب
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _buildDashboardContent(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// تهيئة المتحكمات
  void _initializeControllers() {
    _taskController = Get.find<TaskController>();
    _userController = Get.find<UserController>();
    _departmentController = Get.find<DepartmentController>();
    _chartExportService = Get.find<ChartExportService>();
    _routeObserver = Get.find<RouteObserver<PageRoute>>();
    // تم تعليق هذا السطر لأن المتغير غير مستخدم حاليًا
    // _databaseHelper = Get.find<DatabaseHelper>();

    // تم إزالة تهيئة متحكم لوحة المعلومات لأنه غير مستخدم

    // تهيئة متحكم تخطيط لوحة المعلومات
    try {
      _dashboardLayoutController = Get.find<DashboardLayoutController>();
    } catch (e) {
      _dashboardLayoutController = Get.put(DashboardLayoutController());
    }


  }

  /// الحصول على مفتاح المخطط أو إنشاؤه إذا لم يكن موجودًا
  /// هذه الطريقة تضمن استقرار المفاتيح وتجنب إعادة إنشائها بشكل متكرر
  GlobalKey getOrCreateChartKey(String itemId) {
    if (!_chartKeys.containsKey(itemId)) {
      _chartKeys[itemId] = GlobalKey(debugLabel: 'chart_${itemId}_fixed');
    }
    return _chartKeys[itemId]!;
  }

  /// تنظيف وتحديث مفاتيح المخططات
  /// تستخدم هذه الدالة في _updateSingleChart لتحديث مفتاح مخطط محدد
  void _cleanupChartKeys() {
    // إنشاء مجموعة من معرفات العناصر الحالية
    final currentItemIds = _dashboardItems.map((item) => item.id).toSet();

    // إزالة المفاتيح التي لم تعد مستخدمة
    _chartKeys.removeWhere((key, _) => !currentItemIds.contains(key));

    debugPrint('تم تنظيف مفاتيح المخططات: ${_chartKeys.length} مفتاح');
  }

  /// تهيئة الفلاتر الافتراضية
  void _initializeDefaultFilters() {
    // تعيين فلاتر افتراضية لجميع المخططات
    final defaultChartKeys = [
      'taskStatus',
      'monthlyTasks',
      'priority',
      'department',
      'departmentPerformance',
      'timeEffort',
      'activityHeatmap',
      'tasksTreemap',
      'userTasksStatus'
      'mostActiveUsers',
      'overdueTasks',
    ];

    for (final key in defaultChartKeys) {
      // تعيين نوع الفلتر الافتراضي (الشهر الحالي)
      _chartFilterTypes[key] = TimeFilterType.month;

      // حساب تواريخ البداية والنهاية بناءً على نوع الفلتر
      final now = DateTime.now();
      final firstDayOfMonth = DateTime(now.year, now.month, 1);
      final lastDayOfMonth = DateTime(now.year, now.month + 1, 0);

      _chartStartDates[key] = firstDayOfMonth;
      _chartEndDates[key] = lastDayOfMonth;

      // تعيين نوع المخطط الافتراضي
      _chartTypes[key] = _getDefaultChartType(key);

      // تهيئة خيارات الفلترة المتقدمة
      _advancedFilterOptions[key] = AdvancedFilterOptions();
    }
  }

  /// الحصول على نوع المخطط الافتراضي بناءً على المفتاح
  ChartType _getDefaultChartType(String key) {
    switch (key) {
      case 'taskStatus':
        return ChartType.bar;
      case 'monthlyTasks':
        return ChartType.bar;
      case 'priority':
        return ChartType.pie;
      case 'department':
        return ChartType.bar;
      case 'departmentPerformance':
        return ChartType.radar;
      case 'timeEffort':
        return ChartType.bubble;
      case 'activityHeatmap':
        return ChartType.heatmap;
      case 'tasksTreemap':
        return ChartType.treemap;
      case 'userTasksStatus':
        return ChartType.bar;
      case 'workflow':
        return ChartType.funnel;
      case 'projectTimeline':
        return ChartType.gantt;
      case 'dataFlow':
        return ChartType.sankey;
         case 'mostActiveUsers':
        return ChartType.radar;
      default:
        return ChartType.bar;
    }
  }

  /// إعادة محاولة تحميل البيانات
  Future<void> _retryLoadData() async {
    debugPrint('🔄 إعادة محاولة تحميل بيانات الداش بورد...');

    setState(() {
      _errorMessage = null;
      _isInitializing = true;
      _isLoading = true;
    });

    // إعادة إنشاء التخطيط الافتراضي
    _createDefaultDashboardLayout();

    // تحميل البيانات مرة أخرى
    await _loadData();
  }

  /// تحميل البيانات بشكل متوازي لتحسين الأداء
  Future<void> _loadData() async {
    if (!mounted) return;

    // تجنب التحميل المتكرر
    if (_isLoading && !_isInitializing) {
      debugPrint('تحميل البيانات قيد التنفيذ بالفعل، تجاهل الطلب');
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _isInitializing = true;
    });

    try {
      debugPrint('🚀 بدء تحميل البيانات...');

      // إضافة timeout لتجنب التعليق
      await _loadDataInternal().timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          throw TimeoutException('انتهت مهلة تحميل البيانات', const Duration(seconds: 30));
        },
      );

    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'فشل في تحميل بيانات لوحة المعلومات: ${e.toString()}';
        });
      }
      debugPrint('❌ خطأ في تحميل البيانات: $e');

      // عرض رسالة خطأ للمستخدم
      if (mounted) {
        Get.snackbar(
          'خطأ في التحميل',
          'حدث خطأ أثناء تحميل البيانات',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
          duration: const Duration(seconds: 3),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isInitializing = false;
        });
        debugPrint('✅ اكتمل تحميل البيانات');
      }
    }
  }

  /// تحميل البيانات الداخلي (بدون timeout)
  Future<void> _loadDataInternal() async {
    try {
      // إعادة تعيين الحالات قبل البدء
      if (mounted) {
        setState(() {
          _errorMessage = '';
          _isLoading = true;
        });
      }

      // الحصول على معرف المستخدم الحالي
      final authController = Get.find<AuthController>();
      final userId = authController.currentUser.value?.id;

      if (userId == null) {
        throw Exception('لم يتم العثور على معرف المستخدم الحالي');
      }

      debugPrint('بدء تحميل البيانات للمستخدم: $userId');

      // المرحلة 1: التأكد من وجود تخطيط افتراضي
      if (mounted) {
        // إنشاء تخطيط افتراضي فقط إذا لم يكن موجود
        if (_dashboardItems.isEmpty) {
          _createDefaultDashboardLayout();
        }
        // لا نغير _isInitializing هنا - سيتم تغييره في النهاية

        // تأخير قصير للسماح بعرض التخطيط
        await Future.delayed(const Duration(milliseconds: 100));
      }

      // المرحلة 2: تحميل المهام في الخلفية
      if (mounted) {
        debugPrint('📋 بدء تحميل المهام...');
        await _taskController.loadTasksByUserPermissions(userId);
        debugPrint('✅ تم تحميل المهام: ${_taskController.tasks.length} مهمة');
      }

      // المرحلة 3: تحميل باقي البيانات بشكل متوازي
      if (mounted) {
        debugPrint('📊 بدء تحميل البيانات الإضافية...');
        await Future.wait([
          _userController.loadAllUsers().catchError((e) {
            debugPrint('⚠️ خطأ في تحميل المستخدمين: $e');
            return null;
          }),
          _departmentController.loadDepartments().catchError((e) {
            debugPrint('⚠️ خطأ في تحميل الأقسام: $e');
            return null;
          }),
        ]);

        debugPrint('✅ تم تحميل البيانات الإضافية:');
        debugPrint('- المستخدمين: ${_userController.users.length} مستخدم');
        debugPrint('- الأقسام: ${_departmentController.departments.length} قسم');
      }

      // المرحلة 4: تحميل التخطيط المحفوظ (إن وجد) - فقط إذا لم يكن هناك عناصر
      if (mounted && _dashboardItems.isEmpty) {
        debugPrint('💾 بدء تحميل التخطيط المحفوظ...');
        await _loadDashboardLayout(userId.toString());
        debugPrint('✅ انتهى تحميل التخطيط المحفوظ');
      } else {
        debugPrint('⏭️ تم تجاهل تحميل التخطيط - العناصر موجودة بالفعل');
      }

      // المرحلة 5: تحديث المحتوى بشكل تدريجي
      if (mounted) {
        debugPrint('🔄 بدء تحديث محتوى العناصر...');

        // تحديث المحتوى بشكل غير متزامن لتجنب التجميد
        debugPrint('🎨 بدء تحديث محتوى العناصر...');
        Future.microtask(() async {
          if (mounted) {
            await _updateDashboardItemsContent();
            debugPrint('✅ تم تحديث محتوى عناصر لوحة المعلومات');
          }
        });

        // إضافة المزيد من العناصر في الخلفية
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            debugPrint('➕ إضافة المزيد من العناصر...');
            _addMoreDashboardItems();
          }
        });
      }

      // التحقق من نجاح التحميل وعرض رسالة مناسبة
      if (mounted) {
        debugPrint('🎯 التحقق من نتائج التحميل...');
        if (_taskController.tasks.isNotEmpty) {
          debugPrint('✅ تم العثور على ${_taskController.tasks.length} مهمة');
          // عرض رسالة نجاح مختصرة
          Future.delayed(const Duration(milliseconds: 500), () {
            if (mounted) {
              Get.snackbar(
                'تم التحميل',
                'تم تحميل ${_taskController.tasks.length} مهمة بنجاح',
                snackPosition: SnackPosition.TOP,
                backgroundColor: Colors.green.shade100,
                colorText: Colors.green.shade800,
                duration: const Duration(seconds: 2),
              );
            }
          });
        } else {
          // لا توجد مهام - هذا ليس خطأ بالضرورة
          debugPrint('ℹ️ لا توجد مهام متاحة للمستخدم الحالي');
          // لا نعرض رسالة خطأ، فقط نتركها فارغة
        }
        debugPrint('🏁 انتهت جميع مراحل التحميل بنجاح');
      }

    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات الداخلي: $e');
      rethrow; // إعادة رمي الخطأ ليتم التعامل معه في الدالة الرئيسية
    }
  }

  /// تحميل تخطيط لوحة المعلومات
  Future<void> _loadDashboardLayout(String userId) async {
    try {
      // محاولة تحميل التخطيط باستخدام المتحكم أولاً
      await _dashboardLayoutController.loadSavedLayout();

      if (_dashboardLayoutController.dashboardItems.isNotEmpty) {
        // فقط إذا لم تكن هناك عناصر موجودة، نحمل من المتحكم
        if (_dashboardItems.isEmpty) {
          // تحديث عناصر لوحة المعلومات من المتحكم
          final dashboardItems =
              _dashboardLayoutController.dashboardItems.map((item) {
            return DashboardItem(
              id: item.id,
              title: item.title,
              content: Container(), // سيتم استبداله لاحقًا
              chartType: item.chartType,
              filterOptions: item.filterOptions,
            );
          }).toList();

          setState(() {
            _dashboardItems = dashboardItems;
          });

          debugPrint(
              'تم تحميل تخطيط لوحة المعلومات من المتحكم: ${dashboardItems.length} عنصر');
        } else {
          debugPrint('تم تجاهل تحميل التخطيط من المتحكم لأن العناصر موجودة بالفعل');
        }
        return;
      }

      // إذا لم يتم العثور على تخطيط في المتحكم، نحاول تحميله من قاعدة البيانات
      final dashboardRepository = Get.find<DashboardRepository>();
      final layoutData = await dashboardRepository.getDashboardLayout(userId);


      if (layoutData.isNotEmpty && _dashboardItems.isEmpty) {
        // تحويل البيانات إلى تنسيق مناسب - فقط إذا لم تكن هناك عناصر موجودة
        final dashboardItems = layoutData.map((item) {
          // استخراج نوع المخطط إذا كان موجودًا
          ChartType? chartType;
          if (item['chartType'] != null) {
            chartType = ChartType.values[item['chartType']];
          }

          // استخراج خيارات التصفية إذا كانت موجودة
          AdvancedFilterOptions? filterOptions;
          if (item['filterOptions'] != null) {
            filterOptions =
                AdvancedFilterOptions.fromJson(item['filterOptions']);
          }

          return DashboardItem(
            id: item['id'],
            title: item['title'],
            content: Container(), // سيتم استبداله لاحقًا
            chartType: chartType,
            filterOptions: filterOptions,
          );
        }).toList();

        setState(() {
          _dashboardItems = dashboardItems;
        });

        // تحديث المتحكم أيضًا
        await _dashboardLayoutController.saveDashboardLayout(
            _dashboardItems.map((item) => item.toJson()).toList());

        // محاولة تحميل معايير التصفية المحفوظة
        await _loadSavedFilterOptions();

        debugPrint(
            'تم تحميل تخطيط لوحة المعلومات من قاعدة البيانات: ${dashboardItems.length} عنصر');
      } else if (_dashboardItems.isEmpty) {
        debugPrint('لم يتم العثور على تخطيط لوحة المعلومات في قاعدة البيانات');
        // إنشاء تخطيط افتراضي
        _createDefaultDashboardLayout();
      } else {
        debugPrint('تم تجاهل تحميل التخطيط من قاعدة البيانات لأن العناصر موجودة بالفعل');
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في تحميل تخطيط لوحة المعلومات: $e';
      });
      debugPrint('خطأ في تحميل تخطيط لوحة المعلومات: $e');

      // إنشاء تخطيط افتراضي في حالة الخطأ
      _createDefaultDashboardLayout();
    }
  }

  /// إنشاء تخطيط افتراضي للوحة المعلومات
  void _createDefaultDashboardLayout() {
    try {
      // التحقق من أن السياق جاهز
      if (!mounted) return;

      // إذا كانت هناك عناصر موجودة، لا نحتاج لإنشاء تخطيط جديد
      if (_dashboardItems.isNotEmpty) {
        debugPrint('تم تجاهل إنشاء التخطيط الافتراضي لأن العناصر موجودة بالفعل');
        return;
      }

      debugPrint('🏗️ إنشاء تخطيط افتراضي للوحة المعلومات...');

      // الحصول على حجم الشاشة
      // لا نحتاج لحساب المواقع والأحجام بعد الآن

      // إنشاء عناصر أساسية فقط للعرض السريع
      final defaultItems = [
        _createDashboardItem('توزيع المهام حسب الحالة', ChartType.pie),
        _createDashboardItem('المهام حسب الشهر', ChartType.bar),
        _createDashboardItem('توزيع المهام حسب الأولوية', ChartType.pie),
        _createDashboardItem('الإنتاجية اليومية', ChartType.line),
        _createDashboardItem('معدل إكمال المهام', ChartType.gauge),
        _createDashboardItem('توزيع المهام حسب المستخدم', ChartType.bar),
        _createDashboardItem('توزيع المهام حسب منشئ المهام', ChartType.pie),
        _createDashboardItem('الحالة والأولوية', ChartType.stackedBar),
      ];

      if (mounted) {
        setState(() {
          _dashboardItems = defaultItems;
          _errorMessage = null; // مسح أي رسائل خطأ سابقة
        });
        debugPrint('✅ تم إنشاء التخطيط الافتراضي: ${_dashboardItems.length} عنصر');

        // تحديث محتوى العناصر بعد الإنشاء (بشكل غير متزامن)
        Future.microtask(() {
          if (mounted) {
            _updateDashboardItemsContent();
          }
        });

        // حفظ التخطيط الافتراضي (بدون انتظار)
        _dashboardLayoutController.saveDashboardLayout(
            _dashboardItems.map((item) => item.toJson()).toList());
      }
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء التخطيط الافتراضي: $e');
      if (mounted) {
        setState(() {
          _errorMessage = 'خطأ في إنشاء تخطيط لوحة المعلومات';
        });
      }
    }
  }

  /// إضافة المزيد من عناصر لوحة المعلومات في الخلفية
  void _addMoreDashboardItems() {
    // تشغيل هذا في الخلفية بدون انتظار
    Future.delayed(const Duration(milliseconds: 500), () {
      if (!mounted) return;

      // التحقق من mounted قبل الوصول للسياق
      if (!mounted) return;

      // لا نحتاج لحساب المواقع والأحجام بعد الآن

      // إضافة المزيد من العناصر
      final additionalItems = [
        _createDashboardItem('المهام حسب القسم', ChartType.bar),
        _createDashboardItem('تحليل أداء الأقسام', ChartType.radar),
        _createDashboardItem('المهام المتأخرة', ChartType.funnel),
        _createDashboardItem('توزيع الوقت المقدر مقابل الفعلي', ChartType.scatter),
        _createDashboardItem('نشاط المستخدمين الأسبوعي', ChartType.heatmap),
        _createDashboardItem('تقدم المشاريع', ChartType.gantt),
        _createDashboardItem('مقارنة المنشئين والمُعيَّنين', ChartType.bar),
        _createDashboardItem('أكثر المستخدمين نشاطاً', ChartType.radar),
      ];

      if (mounted) {
        setState(() {
          _dashboardItems.addAll(additionalItems);
        });
      }
    });
  }



  /// بناء محتوى بطاقة عدد المهام الملغاة
  Widget _buildCancelledTasksContent() {
    return Obx(() {
      // الحصول على عدد المهام الملغاة
      final cancelledTasks = _taskController.tasks
          .where((task) => task.status == 'cancelled')
          .length;

      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withAlpha(25), // 0.1 opacity
              spreadRadius: 1,
              blurRadius: 3,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.cancel_outlined,
              size: 48,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              '$cancelledTasks',
              style: const TextStyle(
                fontSize: 36,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'مهمة ملغاة',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    });
  }

  /// إنشاء عنصر لوحة معلومات جديد
  DashboardItem _createDashboardItem(String title, ChartType chartType) {
    return DashboardItem(
      id: const Uuid().v4(),
      title: title,
      content: Container(), // سيتم استبداله لاحقًا
      chartType: chartType,
      filterOptions: AdvancedFilterOptions(),
    );
  }

  /// تحميل معايير التصفية المحفوظة
  Future<void> _loadSavedFilterOptions() async {
    try {
      final dashboardRepository = Get.find<DashboardRepository>();
      final chartKeys = [
        'taskStatus',
        'monthlyTasks',
        'priority',
        'department',
        'departmentPerformance',
        'timeEffort',
        'activityHeatmap',
        'tasksTreemap',
        'workflow',
        'projectTimeline',
        'dataFlow'
      ];

      for (final chartKey in chartKeys) {
        // تحميل معايير التصفية العادية
        final filterData =
            await dashboardRepository.getChartData('${chartKey}_filter');
        if (filterData != null) {
          // استعادة معايير التصفية
          if (filterData['startDate'] != null) {
            _chartStartDates[chartKey] = DateTime.fromMillisecondsSinceEpoch(
                filterData['startDate'] as int);
          }

          if (filterData['endDate'] != null) {
            _chartEndDates[chartKey] = DateTime.fromMillisecondsSinceEpoch(
                filterData['endDate'] as int);
          }

          if (filterData['filterType'] != null) {
            _chartFilterTypes[chartKey] =
                TimeFilterType.values[filterData['filterType'] as int];
          }

          if (filterData['chartType'] != null) {
            _chartTypes[chartKey] =
                ChartType.values[filterData['chartType'] as int];
          }
        }

        // تحميل معايير التصفية المتقدمة
        final advancedFilterData = await dashboardRepository
            .getChartData('${chartKey}_advanced_filter');
        if (advancedFilterData != null) {
          _advancedFilterOptions[chartKey] =
              AdvancedFilterOptionsExtension.fromJson(advancedFilterData);
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحميل معايير التصفية المحفوظة: $e');
    }
  }



  /// تحديث محتوى عناصر لوحة المعلومات
  Future<void> _updateDashboardItemsContent() async {
    if (_dashboardItems.isEmpty || !mounted) return;

    // تجنب التحديث المتكرر
    if (_isLoading) {
      debugPrint('تحديث المحتوى قيد التنفيذ، تجاهل الطلب');
      return;
    }

    try {
      // استخدام قائمة مؤقتة لتجنب تحديث الحالة أثناء التكرار
      final updatedItems = <DashboardItem>[];

      for (int i = 0; i < _dashboardItems.length; i++) {
        if (!mounted) break; // التحقق من mounted في كل تكرار

        final item = _dashboardItems[i];

        try {
          // التأكد من وجود خيارات التصفية المتقدمة
          final advancedFilterOptions =
              item.filterOptions ?? AdvancedFilterOptions();

          // تحديث محتوى العنصر بناءً على نوع المخطط مع معالجة الأخطاء
          Widget content = _buildSafeChartContent(item);

          updatedItems.add(DashboardItem(
            id: item.id,
            title: item.title,
            content: content,
            chartType: item.chartType,
            filterOptions: advancedFilterOptions,
          ));
        } catch (e) {
          // في حالة حدوث خطأ، استخدم محتوى خطأ مبسط
          debugPrint('خطأ في بناء محتوى المخطط ${item.title}: $e');

          updatedItems.add(DashboardItem(
            id: item.id,
            title: item.title,
            content: _buildErrorContent(item, e.toString()),
            chartType: item.chartType,
            filterOptions: item.filterOptions ?? AdvancedFilterOptions(),
          ));
        }

        // تأخير قصير بين العناصر لتجنب التجميد
        if (i < _dashboardItems.length - 1) {
          await Future.delayed(const Duration(milliseconds: 10));
        }
      }

      // تحديث الحالة مرة واحدة بعد الانتهاء من التكرار
      if (mounted && updatedItems.isNotEmpty) {
        setState(() {
          _dashboardItems = updatedItems;
        });
        debugPrint('Dashboard: تم تحديث العناصر (${_dashboardItems.length}) عنصر بعد التحديث');
      }
    } catch (e) {
      debugPrint('خطأ في تحديث محتوى عناصر لوحة المعلومات: $e');
      if (mounted) {
        setState(() {
          _errorMessage = 'خطأ في تحديث محتوى لوحة المعلومات: $e';
        });
      }
    }
  }

  /// بناء محتوى المخطط بشكل آمن
  Widget _buildSafeChartContent(DashboardItem item) {
    try {
      return _buildChartContent(item);
    } catch (e) {
      debugPrint('خطأ في بناء المخطط ${item.title}: $e');
      return _buildErrorContent(item, e.toString());
    }
  }

  /// بناء محتوى الخطأ للمخططات
  Widget _buildErrorContent(DashboardItem item, String error) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: Colors.red.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'خطأ في تحميل المخطط',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.red.shade700,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'حدث خطأ أثناء تحميل بيانات هذا المخطط',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => _reloadSingleChart(item),
            icon: const Icon(Icons.refresh, size: 16),
            label: const Text('إعادة المحاولة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              textStyle: const TextStyle(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  /// إعادة تحميل مخطط واحد
  Future<void> _reloadSingleChart(DashboardItem item) async {
    try {
      debugPrint('🔄 إعادة تحميل المخطط: ${item.title}');

      // العثور على العنصر في القائمة وتحديثه
      final index = _dashboardItems.indexWhere((element) => element.id == item.id);
      if (index != -1 && mounted) {
        setState(() {
          _dashboardItems[index] = DashboardItem(
            id: item.id,
            title: item.title,
            content: _buildSafeChartContent(item),
            chartType: item.chartType,
            filterOptions: item.filterOptions,
          );
        });

        Get.snackbar(
          'تم التحديث',
          'تم إعادة تحميل المخطط بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.shade100,
          colorText: Colors.green.shade800,
          duration: const Duration(seconds: 2),
        );
      }
    } catch (e) {
      debugPrint('❌ فشل في إعادة تحميل المخطط: $e');
      Get.snackbar(
        'خطأ',
        'فشل في إعادة تحميل المخطط',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
        duration: const Duration(seconds: 2),
      );
    }
  } // _reloadSingleChar
Widget _buildStackedBarChart(
    DashboardItem item,
    String chartKey,
    DateTime? startDate,
    DateTime? endDate,
    TimeFilterType filterType,
    AdvancedFilterOptions advancedFilterOptions) {
  return Column(
    children: [
      // مكون الفلترة والتصدير
      UnifiedFilterExportWidget(
        title: item.title,
        chartKey: chartKey,
        startDate: startDate,
        endDate: endDate,
        filterType: filterType,
        chartType: ChartType.stackedBar,
        advancedFilterOptions: advancedFilterOptions,
        onFilterChanged: (start, end, type, chartKey) {
          _updateChartFilter(chartKey, start, end, type, advancedFilterOptions);
        },
        onChartTypeChanged: (type, chartKey) {
          _updateChartType(item.id, type);
        },
        onExport: (format, title) {
          _exportChart(item.id, title);
        },
      ),
      // المخطط الشريطي المتراكم
      Expanded(
        child: _buildSafeChart(
          item.id,
          () {
            final chartData = _getChartData(
                chartKey, startDate, endDate, advancedFilterOptions);
                 final groups = _convertToStackedBarChartGroups(chartData);
            return EnhancedStackedBarChart(
              data: groups,
              title: item.title,
            );
          },
        ),
      ),
    ],
  );
}

List<StackedBarChartGroup> _convertToStackedBarChartGroups(Map<String, dynamic> data) {
  final List<StackedBarChartGroup> groups = [];
  if (data.containsKey('labels') && data.containsKey('series')) {
    final List labels = data['labels'];
    final Map<String, dynamic> series = Map<String, dynamic>.from(data['series']);
    for (int i = 0; i < labels.length; i++) {
      final Map<String, double> values = {};
      for (final entry in series.entries) {
        final List serieValues = entry.value;
        values[entry.key] = (serieValues[i] as num).toDouble();
      }
      groups.add(StackedBarChartGroup(x: labels[i].toString(), values: values));
    }
  }
  return groups;
}
  /// بناء محتوى المخطط بناءً على نوع المخطط
  Widget _buildChartContent(DashboardItem item) {
    // الحصول على نوع المخطط
    final chartType = item.chartType ?? ChartType.bar;

    // الحصول على معايير التصفية
    final chartKey = _getChartKeyFromTitle(item.title);
    final startDate = _chartStartDates[chartKey];
    final endDate = _chartEndDates[chartKey];
    final filterType = _chartFilterTypes[chartKey] ?? TimeFilterType.month;

    // التأكد من وجود خيارات التصفية المتقدمة
    final advancedFilterOptions = item.filterOptions ??
        _advancedFilterOptions[chartKey] ??
        AdvancedFilterOptions();

    // بناء المخطط بناءً على نوع المخطط
    switch (chartType) {
      case ChartType.pie:
        return _buildPieChart(item, chartKey, startDate, endDate, filterType,
            advancedFilterOptions);
      case ChartType.bar:
        // إذا كان العنوان يشير إلى مخطط مهام المستخدمين حسب الحالة
        if (item.title == 'مهام المستخدمين حسب الحالة') {
          return _buildUserTasksStatusChart(item, chartKey, startDate, endDate,
              filterType, advancedFilterOptions);
        } else {
          return _buildBarChart(item, chartKey, startDate, endDate, filterType,
              advancedFilterOptions);
        }
      case ChartType.line:
        return _buildLineChart(item, chartKey, startDate, endDate, filterType,
            advancedFilterOptions);
      case ChartType.radar:
        return _buildRadarChart(item, chartKey, startDate, endDate, filterType,
            advancedFilterOptions);
      case ChartType.bubble:
        return _buildBubbleChart(item, chartKey, startDate, endDate, filterType,
            advancedFilterOptions);
      case ChartType.heatmap:
        return _buildHeatmapChart(item, chartKey, startDate, endDate,
            filterType, advancedFilterOptions);
      case ChartType.treemap:
        return _buildTreemapChart(item, chartKey, startDate, endDate,
            filterType, advancedFilterOptions);
      case ChartType.funnel:
        return _buildFunnelChart(item, chartKey, startDate, endDate, filterType,
            advancedFilterOptions);
      case ChartType.gantt:
        return _buildGanttChart(item, chartKey, startDate, endDate, filterType,
            advancedFilterOptions);
        case ChartType.sankey:
        return _buildSankeyChart(item, chartKey, startDate, endDate, filterType,
            advancedFilterOptions);
      case ChartType.stackedBar:
        return _buildStackedBarChart(item, chartKey, startDate, endDate, filterType,
            advancedFilterOptions);

      case ChartType.gauge:
        return _buildGaugeChart(item, chartKey, startDate, endDate, filterType,
            advancedFilterOptions);
      case ChartType.scatter:
        return _buildScatterChart(item, chartKey, startDate, endDate, filterType,
            advancedFilterOptions);
      default:
        // عرض رسالة خطأ مع أزرار تغيير نوع المخطط
        return Column(
          children: [
            // مكون الفلترة والتصدير
            UnifiedFilterExportWidget(
              title: item.title,
              chartKey: chartKey,
              startDate: startDate,
              endDate: endDate,
              filterType: filterType,
              chartType: chartType,
              advancedFilterOptions: advancedFilterOptions,
              onFilterChanged: (start, end, type, chartKey) {
                _updateChartFilter(
                    chartKey, start, end, type, advancedFilterOptions);
              },
              onChartTypeChanged: (type, chartKey) {
                _updateChartType(item.id, type);
              },
              onExport: (format, title) {
                _exportChart(item.id, title);
              },
            ),

            // رسالة الخطأ
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      color: Colors.red,
                      size: 48,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'نوع المخطط غير مدعوم',
                      style: TextStyle(color: Colors.red, fontSize: 16),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    const Text(
                      'يرجى اختيار نوع مخطط آخر من الأزرار أعلاه',
                      style: TextStyle(fontSize: 14),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: () {
                        _updateChartType(item.id, ChartType.bar);
                      },
                      icon: const Icon(Icons.bar_chart),
                      label: const Text('تغيير إلى مخطط شريطي'),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
    }
  }

  /// الحصول على مفتاح المخطط من العنوان
  String _getChartKeyFromTitle(String title) {
    switch (title) {
      case 'توزيع المهام حسب الحالة':
        return 'taskStatus';
      case 'المهام حسب الشهر':
        return 'monthlyTasks';
      case 'توزيع المهام حسب الأولوية':
        return 'priority';
      case 'المهام حسب القسم':
        return 'department';
      case 'تحليل أداء الأقسام':
        return 'departmentPerformance';
      case 'تحليل الوقت والجهد':
        return 'timeEffort';
      case 'خريطة حرارية للنشاط':
        return 'activityHeatmap';
      case 'توزيع المهام بالخريطة الشجرية':
        return 'tasksTreemap';
      case 'مهام المستخدمين حسب الحالة':
        return 'userTasksStatus';
      case 'تدفق سير العمل':
        return 'workflow';
      case 'الجدول الزمني للمشاريع':
        return 'projectTimeline';
      case 'تدفق البيانات بين الأقسام':
        return 'dataFlow';
      // المخططات الجديدة
      case 'الإنتاجية اليومية':
        return 'dailyProductivity';
      case 'معدل إكمال المهام':
        return 'completionRate';
      case 'توزيع المهام حسب المستخدم':
        return 'userTasks';
      case 'توزيع المهام حسب منشئ المهام':
        return 'tasksByCreator';
      case 'المهام المتأخرة':
        return 'overdueTasks';
      case 'توزيع الوقت المقدر مقابل الفعلي':
        return 'timeComparison';
      case 'نشاط المستخدمين الأسبوعي':
        return 'weeklyActivity';
      case 'تقدم المشاريع':
        return 'projectProgress';
      case 'مقارنة المنشئين والمُعيَّنين':
        return 'creatorsVsAssignees';
      case 'أكثر المستخدمين نشاطاً':
        return 'mostActiveUsers';
      default:
        return 'unknown';
    }
  }

  /// بناء مخطط دائري
  Widget _buildPieChart(
      DashboardItem item,
      String chartKey,
      DateTime? startDate,
      DateTime? endDate,
      TimeFilterType filterType,
      AdvancedFilterOptions advancedFilterOptions) {
    return Column(
      children: [
        // مكون الفلترة والتصدير
        UnifiedFilterExportWidget(
          title: item.title,
          chartKey: chartKey,
          startDate: startDate,
          endDate: endDate,
          filterType: filterType,
          chartType: ChartType.pie,
          advancedFilterOptions: advancedFilterOptions,
          onFilterChanged: (start, end, type, chartKey) {
            _updateChartFilter(
                chartKey, start, end, type, advancedFilterOptions);
          },
          onChartTypeChanged: (type, chartKey) {
            _updateChartType(item.id, type);
          },
          onExport: (format, title) {
            _exportChart(item.id, title);
          },
        ),

        // المخطط الدائري
        Expanded(
          child: _buildSafeChart(
            item.id,
            () {
              // التحقق من البيانات قبل بناء المخطط
              final chartData = _convertToPieChartData(_getChartData(
                  chartKey, startDate, endDate, advancedFilterOptions));
              debugPrint('بيانات المخطط الدائري: ${chartData.length} عنصر');

              return EnhancedPieChart(
                // استخدام ValueKey بدلاً من GlobalKey
                key: ValueKey('pie_chart_${item.id}'),
                title: '',
                data: chartData,
                showFilterOptions: false, // تعطيل الفلتر المكرر
                showExportOptions: false, // تعطيل التصدير المكرر
                onFilterChanged: (start, end, type) {
                  _updateChartFilter(
                      chartKey, start, end, type, advancedFilterOptions);
                },
                onExport: (format) {
                  _exportChart(item.id, format);
                },
                chartType: ChartType.pie,
                advancedFilterOptions: advancedFilterOptions,
              );
            },
          ),
        ),
      ],
    );
  }

  /// بناء مخطط شريطي
  Widget _buildBarChart(
      DashboardItem item,
      String chartKey,
      DateTime? startDate,
      DateTime? endDate,
      TimeFilterType filterType,
      AdvancedFilterOptions advancedFilterOptions) {
    return Column(
      children: [
        // مكون الفلترة والتصدير
        UnifiedFilterExportWidget(
          title: item.title,
          chartKey: chartKey,
          startDate: startDate,
          endDate: endDate,
          filterType: filterType,
          chartType: ChartType.bar,
          advancedFilterOptions: advancedFilterOptions,
          onFilterChanged: (start, end, type, chartKey) {
            _updateChartFilter(
                chartKey, start, end, type, advancedFilterOptions);
          },
          onChartTypeChanged: (type, chartKey) {
            _updateChartType(item.id, type);
          },
          onExport: (format, title) {
            _exportChart(item.id, title);
          },
        ),

        // المخطط الشريطي
        Expanded(
          child: _buildSafeChart(
            item.id,
            () {
              // التحقق من البيانات قبل بناء المخطط
              final chartData = _convertToPieChartData(_getChartData(
                  chartKey, startDate, endDate, advancedFilterOptions));
              debugPrint('بيانات المخطط الشريطي: ${chartData.length} عنصر');

              return EnhancedBarChart(
                key: ValueKey('bar_chart_${item.id}'),
                title: '',
                data: chartData,
                showGrid: true,
                showValues: true,
                showFilterOptions: false, // تعطيل الفلتر المكرر
                showExportOptions: false, // تعطيل التصدير المكرر
                onFilterChanged: (start, end, type) {
                  _updateChartFilter(
                      chartKey, start, end, type, advancedFilterOptions);
                },
                onExport: (format) {
                  _exportChart(item.id, format);
                },
                chartType: ChartType.bar,
                advancedFilterOptions: advancedFilterOptions,
              );
            },
          ),
        ),
      ],
    );
  }

  /// بناء مخطط خطي
  Widget _buildLineChart(
      DashboardItem item,
      String chartKey,
      DateTime? startDate,
      DateTime? endDate,
      TimeFilterType filterType,
      AdvancedFilterOptions advancedFilterOptions) {
    return Column(
      children: [
        // مكون الفلترة والتصدير
        UnifiedFilterExportWidget(
          title: item.title,
          chartKey: chartKey,
          startDate: startDate,
          endDate: endDate,
          filterType: filterType,
          chartType: ChartType.line,
          advancedFilterOptions: advancedFilterOptions,
          onFilterChanged: (start, end, type, chartKey) {
            _updateChartFilter(
                chartKey, start, end, type, advancedFilterOptions);
          },
          onChartTypeChanged: (type, chartKey) {
            _updateChartType(item.id, type);
          },
          onExport: (format, title) {
            _exportChart(item.id, title);
          },
        ),

        // المخطط الخطي
        Expanded(
          child: _buildSafeChart(
            item.id,
            () {
              // التحقق من البيانات قبل بناء المخطط
              final chartData = _convertToLineChartData(_getChartData(
                  chartKey, startDate, endDate, advancedFilterOptions));
              debugPrint('بيانات المخطط الخطي: ${chartData.length} سلسلة');

              return EnhancedLineChart(
                key: ValueKey('line_chart_${item.id}'),
                title: '',
                data: chartData,
                showDots: true,
                showFilterOptions: false, // تعطيل الفلتر المكرر
                showExportOptions: false, // تعطيل التصدير المكرر
                onFilterChanged: (start, end, type) {
                  _updateChartFilter(
                      chartKey, start, end, type, advancedFilterOptions);
                },
                onExport: (format) {
                  _exportChart(item.id, format);
                },
                chartType: ChartType.line,
                advancedFilterOptions: advancedFilterOptions,
              );
            },
          ),
        ),
      ],
    );
  }

  /// بناء مخطط راداري
  Widget _buildRadarChart(
      DashboardItem item,
      String chartKey,
      DateTime? startDate,
      DateTime? endDate,
      TimeFilterType filterType,
      AdvancedFilterOptions advancedFilterOptions) {
    return Column(
      children: [
        // مكون الفلترة والتصدير
        UnifiedFilterExportWidget(
          title: item.title,
          chartKey: chartKey,
          startDate: startDate,
          endDate: endDate,
          filterType: filterType,
          chartType: ChartType.radar,
          advancedFilterOptions: advancedFilterOptions,
          onFilterChanged: (start, end, type, chartKey) {
            _updateChartFilter(
                chartKey, start, end, type, advancedFilterOptions);
          },
          onChartTypeChanged: (type, chartKey) {
            _updateChartType(item.id, type);
          },
          onExport: (format, title) {
            _exportChart(item.id, title);
          },
        ),

        // المخطط الراداري
        Expanded(
          child: _buildSafeChart(
            item.id,
            () {
              // تحضير البيانات مع التأكد من وجود 3 عناصر على الأقل
              final rawData = _convertToRadarChartData(_getChartData(
                  chartKey, startDate, endDate, advancedFilterOptions));
              final Map<String, List<double>> radarData =
                  _ensureMinimumRadarData(rawData);
              debugPrint('بيانات المخطط الراداري: ${radarData.length} سلسلة');

              return EnhancedRadarChart(
                key: ValueKey('radar_chart_${item.id}'),
                title: '',
                data: radarData,
                axisLabels: ['محور 1', 'محور 2', 'محور 3', 'محور 4', 'محور 5'],
                showFilterOptions: false, // تعطيل الفلتر المكرر
                showExportOptions: false, // تعطيل التصدير المكرر
                onFilterChanged: (start, end, type) {
                  _updateChartFilter(
                      chartKey, start, end, type, advancedFilterOptions);
                },
                onExport: (format) {
                  _exportChart(item.id, format);
                },
                chartType: ChartType.radar,
                advancedFilterOptions: advancedFilterOptions,
              );
            },
          ),
        ),
      ],
    );
  }

  /// بناء مخطط فقاعي
  Widget _buildBubbleChart(
      DashboardItem item,
      String chartKey,
      DateTime? startDate,
      DateTime? endDate,
      TimeFilterType filterType,
      AdvancedFilterOptions advancedFilterOptions) {
    return Column(
      children: [
        // مكون الفلترة والتصدير
        UnifiedFilterExportWidget(
          title: item.title,
          chartKey: chartKey,
          startDate: startDate,
          endDate: endDate,
          filterType: filterType,
          chartType: ChartType.bubble,
          advancedFilterOptions: advancedFilterOptions,
          onFilterChanged: (start, end, type, chartKey) {
            _updateChartFilter(
                chartKey, start, end, type, advancedFilterOptions);
          },
          onChartTypeChanged: (type, chartKey) {
            _updateChartType(item.id, type);
          },
          onExport: (format, title) {
            _exportChart(item.id, title);
          },
        ),

        // المخطط الفقاعي
        Expanded(
          child: _buildSafeChart(
            item.id,
            () {
              // التحقق من البيانات قبل بناء المخطط
              final bubbleData = _convertToBubbleChartData(_getChartData(
                  chartKey, startDate, endDate, advancedFilterOptions));
              debugPrint('بيانات المخطط الفقاعي: ${bubbleData.length} سلسلة');

              return EnhancedBubbleChart(
                key: ValueKey('bubble_chart_${item.id}'),
                title: '',
                data: bubbleData,
                showGrid: true,
                showLabels: true,
                showLegend: true,
                showFilterOptions: false, // تعطيل الفلتر المكرر
                showExportOptions: false, // تعطيل التصدير المكرر
                horizontalInterval: 10.0,
                verticalInterval: 10.0,
                onFilterChanged: (start, end, type) {
                  _updateChartFilter(
                      chartKey, start, end, type, advancedFilterOptions);
                },
                onExport: (format) {
                  _exportChart(item.id, format);
                },
                chartType: ChartType.bubble,
                advancedFilterOptions: advancedFilterOptions,
              );
            },
          ),
        ),
      ],
    );
  }

  /// بناء مخطط حراري
  Widget _buildHeatmapChart(
      DashboardItem item,
      String chartKey,
      DateTime? startDate,
      DateTime? endDate,
      TimeFilterType filterType,
      AdvancedFilterOptions advancedFilterOptions) {
    return Column(
      children: [
        // مكون الفلترة والتصدير
        UnifiedFilterExportWidget(
          title: item.title,
          chartKey: chartKey,
          startDate: startDate,
          endDate: endDate,
          filterType: filterType,
          chartType: ChartType.heatmap,
          advancedFilterOptions: advancedFilterOptions,
          onFilterChanged: (start, end, type, chartKey) {
            _updateChartFilter(
                chartKey, start, end, type, advancedFilterOptions);
          },
          onChartTypeChanged: (type, chartKey) {
            _updateChartType(item.id, type);
          },
          onExport: (format, title) {
            _exportChart(item.id, title);
          },
        ),

        // المخطط الحراري
        Expanded(
          child: _buildSafeChart(
            item.id,
            () {
              // تحضير البيانات مع التأكد من وجود بيانات كافية
              final rawData = _convertToHeatmapData(_getChartData(
                  chartKey, startDate, endDate, advancedFilterOptions));
              final heatmapData = _ensureValidHeatmapData(rawData);
              debugPrint('بيانات المخطط الحراري: ${heatmapData.length} صفوف');

              return EnhancedHeatmapChart(
                key: ValueKey('heatmap_chart_${item.id}'),
                title: '',
                data: heatmapData,
                xLabels: [
                  'الأحد',
                  'الاثنين',
                  'الثلاثاء',
                  'الأربعاء',
                  'الخميس',
                  'الجمعة',
                  'السبت'
                ],
                yLabels: ['الأسبوع 1', 'الأسبوع 2', 'الأسبوع 3', 'الأسبوع 4'],
                xAxisTitle: 'أيام الأسبوع',
                yAxisTitle: 'الأسابيع',
                showValues: true,
                showFilterOptions: false, // تعطيل الفلتر المكرر
                showExportOptions: false, // تعطيل التصدير المكرر
                onFilterChanged: (start, end, type) {
                  _updateChartFilter(
                      chartKey, start, end, type, advancedFilterOptions);
                },
                onExport: (format) {
                  _exportChart(item.id, format);
                },
                chartType: ChartType.heatmap,
                advancedFilterOptions: advancedFilterOptions,
              );
            },
          ),
        ),
      ],
    );
  }

  /// بناء مخطط شجري
  Widget _buildTreemapChart(
      DashboardItem item,
      String chartKey,
      DateTime? startDate,
      DateTime? endDate,
      TimeFilterType filterType,
      AdvancedFilterOptions advancedFilterOptions) {
    return Column(
      children: [
        // مكون الفلترة والتصدير
        UnifiedFilterExportWidget(
          title: item.title,
          chartKey: chartKey,
          startDate: startDate,
          endDate: endDate,
          filterType: filterType,
          chartType: ChartType.treemap,
          advancedFilterOptions: advancedFilterOptions,
          onFilterChanged: (start, end, type, chartKey) {
            _updateChartFilter(
                chartKey, start, end, type, advancedFilterOptions);
          },
          onChartTypeChanged: (type, chartKey) {
            _updateChartType(item.id, type);
          },
          onExport: (format, title) {
            _exportChart(item.id, title);
          },
        ),

        // المخطط الشجري
        Expanded(
          child: _buildSafeChart(
            item.id,
            () {
              // التحقق من البيانات قبل بناء المخطط
              final treemapData = _convertToTreemapData(_getChartData(
                  chartKey, startDate, endDate, advancedFilterOptions));
              debugPrint('بيانات المخطط الشجري: ${treemapData.length} عنصر');

              return EnhancedTreemapChart(
                key: ValueKey('treemap_chart_${item.id}'),
                title: '',
                data: treemapData,
                showValues: true,
                showPercentages: true,
                showFilterOptions: false, // تعطيل الفلتر المكرر
                showExportOptions: false, // تعطيل التصدير المكرر
                onFilterChanged: (start, end, type) {
                  _updateChartFilter(
                      chartKey, start, end, type, advancedFilterOptions);
                },
                onExport: (format) {
                  _exportChart(item.id, format);
                },
                // إضافة المعلمات المطلوبة
                chartType: ChartType.treemap,
                advancedFilterOptions: advancedFilterOptions,
              );
            },
          ),
        ),
      ],
    );
  }

  /// بناء مخطط مهام المستخدمين حسب الحالة
  Widget _buildUserTasksStatusChart(
      DashboardItem item,
      String chartKey,
      DateTime? startDate,
      DateTime? endDate,
      TimeFilterType filterType,
      AdvancedFilterOptions advancedFilterOptions) {
    return Column(
      children: [
        // مكون الفلترة والتصدير
        UnifiedFilterExportWidget(
          title: item.title,
          chartKey: chartKey,
          startDate: startDate,
          endDate: endDate,
          filterType: filterType,
          chartType: ChartType.bar,
          advancedFilterOptions: advancedFilterOptions,
          onFilterChanged: (start, end, type, chartKey) {
            _updateChartFilter(
                chartKey, start, end, type, advancedFilterOptions);
          },
          onChartTypeChanged: (type, chartKey) {
            _updateChartType(item.id, type);
          },
          onExport: (format, title) {
            _exportChart(item.id, title);
          },
        ),

        // مخطط مهام المستخدمين حسب الحالة
        Expanded(
          child: _buildSafeChart(
            item.id,
            () {
              return UserTasksStatusChart(
                key: ValueKey('user_tasks_status_chart_${item.id}'),
                title: '',
                showFilterOptions: false, // تعطيل الفلتر المكرر
                showExportOptions: false, // تعطيل التصدير المكرر
                onFilterChanged: (start, end, type) {
                  _updateChartFilter(chartKey, start, end, TimeFilterType.month,
                      advancedFilterOptions);
                },
                onExport: (format) {
                  _exportChart(item.id, format);
                },
                chartType: ChartType.bar,
                advancedFilterOptions: advancedFilterOptions,
              );
            },
          ),
        ),
      ],
    );
  }

  /// الحصول على بيانات المخطط
  Map<String, dynamic> _getChartData(String chartKey, DateTime? startDate,
      DateTime? endDate, AdvancedFilterOptions advancedFilterOptions) {
    // التأكد من وجود تواريخ البداية والنهاية
    final start =
        startDate ?? DateTime.now().subtract(const Duration(days: 30));
    final end = endDate ?? DateTime.now();

    // الحصول على المهام المصفاة
    final filteredTasks = _taskController.tasks.where((task) {
      // تصفية حسب التاريخ
      final taskDate = task.createdAtDateTime;
      final inDateRange = taskDate.isAfter(start) &&
          taskDate.isBefore(end.add(const Duration(days: 1)));

      // تصفية حسب الخيارات المتقدمة
      bool matchesAdvancedFilters = true;

      if (advancedFilterOptions.departmentIds != null &&
          advancedFilterOptions.departmentIds!.isNotEmpty) {
        matchesAdvancedFilters = matchesAdvancedFilters &&
            advancedFilterOptions.departmentIds!.contains(task.departmentId);
      }

      if (advancedFilterOptions.assigneeIds != null &&
          advancedFilterOptions.assigneeIds!.isNotEmpty) {
        matchesAdvancedFilters = matchesAdvancedFilters &&
            advancedFilterOptions.assigneeIds!.contains(task.assigneeId?.toString());
      }

      if (advancedFilterOptions.creatorIds != null &&
          advancedFilterOptions.creatorIds!.isNotEmpty) {
        matchesAdvancedFilters = matchesAdvancedFilters &&
            advancedFilterOptions.creatorIds!.contains(task.creatorId.toString());
      }

      if (advancedFilterOptions.priorityFilter != null &&
          advancedFilterOptions.priorityFilter!.isNotEmpty) {
        matchesAdvancedFilters = matchesAdvancedFilters &&
            advancedFilterOptions.priorityFilter!.contains(task.priority.toString());
      }

      if (advancedFilterOptions.statusFilter != null &&
          advancedFilterOptions.statusFilter!.isNotEmpty) {
        // تحويل String إلى DashboardTaskStatus enum للمقارنة
        final dashboardTaskStatus = DashboardTaskStatus.values.firstWhere(
          (status) => status.name.toLowerCase() == task.status.toLowerCase(),
          orElse: () => DashboardTaskStatus.pending,
        );
        matchesAdvancedFilters = matchesAdvancedFilters &&
            advancedFilterOptions.statusFilter!.contains(dashboardTaskStatus);
      }

      return inDateRange && matchesAdvancedFilters;
    }).toList();

    // بناء بيانات المخطط بناءً على نوع المخطط
    switch (chartKey) {
      case 'taskStatus':
        return _buildTaskStatusData(filteredTasks);
      case 'monthlyTasks':
        return _buildMonthlyTasksData(filteredTasks, start, end);
      case 'priority':
        return _buildPriorityData(filteredTasks);
      case 'department':
        return _buildDepartmentData(filteredTasks);
      case 'departmentPerformance':
        return _buildDepartmentPerformanceData(filteredTasks);
      case 'timeEffort':
        return _buildTimeEffortData(filteredTasks);
      case 'activityHeatmap':
        return _buildActivityHeatmapData(filteredTasks, start, end);
      case 'tasksTreemap':
        return _buildTasksTreemapData(filteredTasks);
      case 'workflow':
        return _buildWorkflowData(filteredTasks);
      case 'projectTimeline':
        return _buildProjectTimelineData(filteredTasks);
      case 'dataFlow':
        return _buildDataFlowData(filteredTasks);
      // المخططات الجديدة
      case 'dailyProductivity':
        return _buildDailyProductivityData(filteredTasks, start, end);
      case 'completionRate':
        return _buildCompletionRateData(filteredTasks);
      case 'userTasks':
        return _buildUserTasksData(filteredTasks);
      case 'tasksByCreator':
        return _buildTasksByCreatorData(filteredTasks);
      case 'overdueTasks':
        return _buildOverdueTasksData(filteredTasks);
      case 'timeComparison':
        return _buildTimeComparisonData(filteredTasks);
      case 'weeklyActivity':
        return _buildWeeklyActivityData(filteredTasks, start, end);
      case 'projectProgress':
        return _buildProjectProgressData(filteredTasks);
      case 'creatorsVsAssignees':
        return _buildCreatorsVsAssigneesData(filteredTasks);
      case 'mostActiveUsers':
        return _buildMostActiveUsersData(filteredTasks);
      default:
        return {'labels': [], 'values': []};
    }
  }

  /// بناء بيانات توزيع المهام حسب الحالة
  Map<String, dynamic> _buildTaskStatusData(List<dynamic> tasks) {
    // تجميع المهام حسب الحالة
    final Map<String, int> statusCounts = {
      'قيد الانتظار': 0,
      'قيد التنفيذ': 0,
      'مكتملة': 0,
      'ملغاة': 0,
    };

    if (tasks.isNotEmpty) {
      debugPrint('📊 Dashboard _buildTaskStatusData: معالجة ${tasks.length} مهمة');

      for (final task in tasks) {
        // استخدام الحالة مباشرة بدلاً من split
        final status = task.status.toString();
        final statusName = _getStatusName(status);
        statusCounts[statusName] = (statusCounts[statusName] ?? 0) + 1;

        debugPrint('   مهمة "${task.title}": حالة="$status" -> مجموعة="$statusName"');
      }

      debugPrint('📊 Dashboard نتائج التجميع: $statusCounts');
    } else {
      debugPrint('⚠️ Dashboard: لا توجد مهام لمعالجتها في _buildTaskStatusData');
    }

    // تحويل البيانات إلى تنسيق مناسب للمخطط
    final labels = statusCounts.keys.toList();
    final values = statusCounts.values.toList();

    return {
      'labels': labels,
      'values': values,
    };
  }

  /// بناء بيانات المهام حسب الشهر
  Map<String, dynamic> _buildMonthlyTasksData(
      List<dynamic> tasks, DateTime start, DateTime end) {
    // إنشاء قائمة بالأشهر في النطاق
    final months = <DateTime>[];
    DateTime current = DateTime(start.year, start.month, 1);

    while (current.isBefore(end) ||
        current.year == end.year && current.month == end.month) {
      months.add(current);
      current = DateTime(current.year, current.month + 1, 1);
    }

    // التأكد من وجود 6 أشهر على الأقل للعرض
    if (months.length < 6) {
      final lastMonth = months.isNotEmpty ? months.last : DateTime.now();
      for (int i = 1; months.length < 6; i++) {
        months.add(DateTime(lastMonth.year, lastMonth.month + i, 1));
      }
    }

    // تجميع المهام حسب الشهر
    final Map<String, int> monthlyCounts = {};

    for (final month in months) {
      final monthName = DateFormat.yMMM('ar').format(month);
      monthlyCounts[monthName] = 0;
    }

    if (tasks.isNotEmpty) {
      for (final task in tasks) {
        final taskDate = task.createdAtDateTime;
        final monthName = DateFormat.yMMM('ar')
            .format(DateTime(taskDate.year, taskDate.month, 1));

        if (monthlyCounts.containsKey(monthName)) {
          monthlyCounts[monthName] = (monthlyCounts[monthName] ?? 0) + 1;
        }
      }
    }

    // تحويل البيانات إلى تنسيق مناسب للمخطط
    final labels = monthlyCounts.keys.toList();
    final values = monthlyCounts.values.toList();

    return {
      'labels': labels,
      'values': values,
    };
  }

  /// بناء بيانات توزيع المهام حسب الأولوية
  Map<String, dynamic> _buildPriorityData(List<dynamic> tasks) {
    // تجميع المهام حسب الأولوية
    final Map<String, int> priorityCounts = {
      'منخفضة': 0,
      'متوسطة': 0,
      'عالية': 0,
      'عاجلة': 0,
    };

    if (tasks.isNotEmpty) {
      debugPrint('📊 Dashboard _buildPriorityData: معالجة ${tasks.length} مهمة');

      for (final task in tasks) {
        // استخدام الأولوية مباشرة بدلاً من split
        final priority = task.priority.toString();
        final priorityName = _getPriorityName(priority);
        priorityCounts[priorityName] = (priorityCounts[priorityName] ?? 0) + 1;

        debugPrint('   مهمة "${task.title}": أولوية="$priority" -> مجموعة="$priorityName"');
      }

      debugPrint('📊 Dashboard نتائج تجميع الأولويات: $priorityCounts');
    } else {
      // إضافة بيانات افتراضية إذا كانت القائمة فارغة
      priorityCounts['منخفضة'] = 8;
      priorityCounts['متوسطة'] = 15;
      priorityCounts['عالية'] = 10;
      priorityCounts['عاجلة'] = 5;

      debugPrint('تم استخدام بيانات افتراضية لتوزيع المهام حسب الأولوية');
    }

    // تحويل البيانات إلى تنسيق مناسب للمخطط
    final labels = priorityCounts.keys.toList();
    final values = priorityCounts.values.toList();

    return {
      'labels': labels,
      'values': values,
    };
  }

  /// بناء بيانات المهام حسب القسم
  Map<String, dynamic> _buildDepartmentData(List<dynamic> tasks) {
    // تجميع المهام حسب القسم
    final Map<String, int> departmentCounts = {};

    // إضافة جميع الأقسام الموجودة أولاً
    if (_departmentController.departments.isNotEmpty) {
      for (final dept in _departmentController.departments) {
        departmentCounts[dept.name] = 0;
      }
    } else {
      // إضافة أقسام افتراضية إذا لم تكن هناك أقسام
      departmentCounts['قسم الموارد البشرية'] = 0;
      departmentCounts['قسم تقنية المعلومات'] = 0;
      departmentCounts['قسم المالية'] = 0;
      departmentCounts['قسم التسويق'] = 0;
      departmentCounts['قسم المبيعات'] = 0;
    }

    if (tasks.isNotEmpty) {
      for (final task in tasks) {
        final departmentId = task.departmentId;
        final department = _departmentController.departments.firstWhereOrNull(
          (dept) => dept.id == departmentId,
        );

        final departmentName = department?.name ?? 'غير محدد';
        departmentCounts[departmentName] =
            (departmentCounts[departmentName] ?? 0) + 1;
      }
    }

    // تحويل البيانات إلى تنسيق مناسب للمخطط
    final labels = departmentCounts.keys.toList();
    final values = departmentCounts.values.toList();

    return {
      'labels': labels,
      'values': values,
    };
  }

  /// بناء بيانات أداء الأقسام
  Map<String, dynamic> _buildDepartmentPerformanceData(List<dynamic> tasks) {
    // تجميع المهام المكتملة حسب القسم
    final Map<String, int> completedTasks = {};
    final Map<String, int> totalTasks = {};

    // إضافة جميع الأقسام الموجودة أولاً
    if (_departmentController.departments.isNotEmpty) {
      for (final dept in _departmentController.departments) {
        completedTasks[dept.name] = 0;
        totalTasks[dept.name] = 0;
      }
    } else {
      // إضافة أقسام افتراضية إذا لم تكن هناك أقسام
      final defaultDepts = [
        'قسم الموارد البشرية',
        'قسم تقنية المعلومات',
        'قسم المالية',
        'قسم التسويق',
        'قسم المبيعات'
      ];

      for (final dept in defaultDepts) {
        completedTasks[dept] = 0;
        totalTasks[dept] = 0;
      }
    }

    if (tasks.isNotEmpty) {
      for (final task in tasks) {
        final departmentId = task.departmentId;
        final department = _departmentController.departments.firstWhereOrNull(
          (dept) => dept.id == departmentId,
        );

        final departmentName = department?.name ?? 'غير محدد';
        totalTasks[departmentName] = (totalTasks[departmentName] ?? 0) + 1;

        // استخدام تطبيع الحالة بدلاً من enum
        final normalizedStatus = _normalizeTaskStatus(task.status.toString());
        if (normalizedStatus == 'completed') {
          completedTasks[departmentName] =
              (completedTasks[departmentName] ?? 0) + 1;
        }
      }
    }

    // حساب نسبة الإنجاز لكل قسم
    final Map<String, double> completionRates = {};

    for (final department in totalTasks.keys) {
      final completed = completedTasks[department] ?? 0;
      final total = totalTasks[department] ?? 0;
      completionRates[department] = total > 0 ? (completed / total) * 100 : 0;
    }

    // تحويل البيانات إلى تنسيق مناسب للمخطط
    final labels = completionRates.keys.toList();
    final values = completionRates.values.toList();

    return {
      'labels': labels,
      'values': values,
    };
  }

  /// بناء بيانات الوقت والجهد
  Map<String, dynamic> _buildTimeEffortData(List<dynamic> tasks) {
    // تجميع المهام حسب الوقت والجهد
    final List<Map<String, dynamic>> bubbleData = [];

    if (tasks.isNotEmpty) {
      for (final task in tasks) {
        try {
          // استخدام الوقت المقدر كمحور س
          final estimatedTime = task.estimatedTime ?? 0;

          // استخدام الوقت الفعلي كمحور ص
          final actualTime = task.actualTime ?? 0;

          // استخدام الأولوية كحجم الفقاعة
          final priority = task.priority.toString().split('.').last;
          final priorityValue = _getPriorityValue(priority);

          // استخدام الحالة كلون الفقاعة
          final status = task.status.toString();

          bubbleData.add({
            'x': estimatedTime.toDouble(),
            'y': actualTime.toDouble(),
            'size': priorityValue.toDouble(),
            'category': _getStatusName(status),
            'name': task.title,
          });
        } catch (e) {
          debugPrint('خطأ في معالجة المهمة: $e');
          // تجاهل المهمة التي تسبب الخطأ
        }
      }
    }

    // لا نضيف بيانات افتراضية، نعرض رسالة "لا توجد بيانات" بدلاً من ذلك

    return {
      'bubbleData': bubbleData,
    };
  }

  /// بناء بيانات خريطة النشاط الحرارية
  Map<String, dynamic> _buildActivityHeatmapData(
      List<dynamic> tasks, DateTime start, DateTime end) {
    // إنشاء مصفوفة ثنائية الأبعاد لتمثيل النشاط
    final int daysInRange = end.difference(start).inDays + 1;
    final int weeks = (daysInRange / 7).ceil();

    // التأكد من وجود 4 أسابيع على الأقل
    final int finalWeeks = weeks < 4 ? 4 : weeks;

    // تهيئة مصفوفة البيانات
    final List<List<int>> heatmapData = List.generate(
      7, // أيام الأسبوع
      (_) => List.generate(finalWeeks, (_) => 0),
    );

    // إنشاء تسميات الأيام والأسابيع
    final List<String> dayLabels = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد'
    ];

    final List<String> weekLabels = [];
    DateTime currentWeekStart = start;
    for (int i = 0; i < finalWeeks; i++) {
      weekLabels.add(DateFormat.MMMd('ar').format(currentWeekStart));
      currentWeekStart = currentWeekStart.add(const Duration(days: 7));
    }

    if (tasks.isNotEmpty) {
      // تجميع المهام حسب اليوم
      for (final task in tasks) {
        final taskDate = task.createdAtDateTime;

        // التحقق من أن التاريخ ضمن النطاق
        if (taskDate.isBefore(start) || taskDate.isAfter(end)) continue;

        // حساب الموقع في المصفوفة
        final daysSinceStart = taskDate.difference(start).inDays;
        final weekIndex = daysSinceStart ~/ 7;
        final dayOfWeek = taskDate.weekday - 1; // 0 = الاثنين، 6 = الأحد

        // التأكد من أن المؤشرات ضمن النطاق
        if (weekIndex >= 0 &&
            weekIndex < finalWeeks &&
            dayOfWeek >= 0 &&
            dayOfWeek < 7) {
          heatmapData[dayOfWeek][weekIndex]++;
        }
      }
    }

    // تحويل البيانات إلى تنسيق مناسب للمخطط
    final List<List<Map<String, dynamic>>> formattedData = [];

    for (int day = 0; day < 7; day++) {
      final List<Map<String, dynamic>> dayData = [];
      for (int week = 0; week < finalWeeks; week++) {
        dayData.add({
          'value': heatmapData[day][week],
          'day': day,
          'week': week,
        });
      }
      formattedData.add(dayData);
    }

    return {
      'data': heatmapData,
      'formattedData': formattedData,
      'dayLabels': dayLabels,
      'weekLabels': weekLabels,
    };
  }

  /// بناء بيانات الخريطة الشجرية
  Map<String, dynamic> _buildTasksTreemapData(List<dynamic> tasks) {
    // تجميع المهام حسب القسم والحالة
    final Map<String, Map<String, int>> treemapData = {};

    // إضافة جميع الأقسام الموجودة أولاً
    final List<String> departments = [];
    if (_departmentController.departments.isNotEmpty) {
      for (final dept in _departmentController.departments) {
        departments.add(dept.name);
        treemapData[dept.name] = {};
      }
    } else {
      // إضافة أقسام افتراضية إذا لم تكن هناك أقسام
      departments.addAll([
        'قسم الموارد البشرية',
        'قسم تقنية المعلومات',
        'قسم المالية',
        'قسم التسويق',
        'قسم المبيعات'
      ]);

      for (final dept in departments) {
        treemapData[dept] = {};
      }
    }

    // إضافة جميع الحالات لكل قسم
    final List<String> statuses = [
      'قيد الانتظار',
      'قيد التنفيذ',
      'مكتملة',
      'ملغاة'
    ];

    for (final dept in departments) {
      for (final status in statuses) {
        treemapData[dept]![status] = 0;
      }
    }

    if (tasks.isNotEmpty) {
      for (final task in tasks) {
        try {
          final departmentId = task.departmentId;
          final department = _departmentController.departments.firstWhereOrNull(
            (dept) => dept.id == departmentId,
          );

          final departmentName = department?.name ?? 'غير محدد';
          final status = task.status.toString();
          final statusName = _getStatusName(status);

          if (!treemapData.containsKey(departmentName)) {
            treemapData[departmentName] = {};
            for (final s in statuses) {
              treemapData[departmentName]![s] = 0;
            }
          }

          treemapData[departmentName]![statusName] =
              (treemapData[departmentName]![statusName] ?? 0) + 1;
        } catch (e) {
          debugPrint('خطأ في معالجة المهمة للخريطة الشجرية: $e');
        }
      }
    }

    // تحويل البيانات إلى تنسيق مناسب للمخطط
    final List<Map<String, dynamic>> treemapItems = [];

    for (final department in treemapData.keys) {
      for (final status in treemapData[department]!.keys) {
        if (treemapData[department]![status]! > 0) {
          treemapItems.add({
            'name': '$department - $status',
            'parent': department,
            'value': treemapData[department]![status],
            'category': status,
          });
        }
      }
    }

    return {
      'items': treemapItems,
    };
  }

  /// الحصول على اسم الحالة
  String _getStatusName(String status) {
    // تطبيع الحالة للتعامل مع جميع الاحتمالات
    final normalizedStatus = status.toLowerCase().trim();

    debugPrint('🔍 Dashboard _getStatusName: الحالة الأصلية: "$status" -> المطبعة: "$normalizedStatus"');

    switch (normalizedStatus) {
      // حالات الانتظار
      case 'pending':
      case 'new':
      case 'news':
      case 'جديد':
      case 'جديدة':
        return 'قيد الانتظار';

      // حالات التنفيذ
      case 'inprogress':
      case 'in_progress':
      case 'in-progress':
      case 'قيد التنفيذ':
        return 'قيد التنفيذ';

      // حالات انتظار المعلومات
      case 'waitingforinfo':
      case 'waiting_for_info':
      case 'waiting-for-info':
      case 'review':
      case 'في انتظار معلومات':
      case 'مراجعة':
        return 'في انتظار معلومات';

      // حالات الاكتمال
      case 'completed':
      case 'done':
      case 'مكتملة':
        return 'مكتملة';

      // حالات الإلغاء
      case 'cancelled':
      case 'canceled':
      case 'ملغاة':
        return 'ملغاة';

      default:
        debugPrint('⚠️ Dashboard: حالة غير معروفة: "$status"');
        return status; // إرجاع الحالة كما هي
    }
  }

  /// الحصول على اسم الأولوية
  String _getPriorityName(String priority) {
    switch (priority) {
      case 'low':
        return 'منخفضة';
      case 'medium':
        return 'متوسطة';
      case 'high':
        return 'عالية';
      case 'urgent':
        return 'عاجلة';
      default:
        return 'غير محدد';
    }
  }

  /// الحصول على قيمة الأولوية
  int _getPriorityValue(String priority) {
    switch (priority) {
      case 'low':
        return 1;
      case 'medium':
        return 2;
      case 'high':
        return 3;
      case 'urgent':
        return 4;
      default:
        return 1;
    }
  }

  /// تطبيع حالة المهمة للتعامل مع جميع الاحتمالات (نسخة Dashboard)
  String _normalizeTaskStatus(String status) {
    final normalizedStatus = status.toLowerCase().trim();

    switch (normalizedStatus) {
      case 'inprogress':
      case 'in_progress':
      case 'in-progress':
      case 'قيد التنفيذ':
        return 'in_progress';

      case 'waitingforinfo':
      case 'waiting_for_info':
      case 'waiting-for-info':
      case 'review':
      case 'في انتظار معلومات':
      case 'مراجعة':
        return 'waiting_for_info';

      case 'pending':
      case 'new':
      case 'news':
      case 'جديد':
      case 'جديدة':
        return 'pending';

      case 'completed':
      case 'done':
      case 'مكتملة':
        return 'completed';

      case 'cancelled':
      case 'canceled':
      case 'ملغاة':
        return 'cancelled';

      default:
        debugPrint('⚠️ Dashboard: حالة غير معروفة: "$status"');
        return status.toLowerCase(); // إرجاع الحالة كما هي مع تحويلها للأحرف الصغيرة
    }
  }

  /// بناء مخطط آمن مع معالجة الأخطاء
  /// تم تحسين هذه الدالة لتوفير معالجة أخطاء أفضل وتسجيل أكثر تفصيلاً
  Widget _buildSafeChart(String itemId, Widget Function() builder) {
    // التحقق من حالة mounted قبل البناء
    if (!mounted) {
      return const SizedBox.shrink();
    }

    try {
      // محاولة بناء المخطط مع تأخير قصير لتجنب التداخل
      return FutureBuilder<Widget>(
        future: Future.microtask(() {
          try {
            return builder();
          } catch (e) {
            debugPrint('خطأ في بناء المخطط $itemId: $e');
            return _buildChartErrorWidget(itemId, e.toString());
          }
        }),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(
              child: SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            );
          }

          if (snapshot.hasError) {
            debugPrint('خطأ في FutureBuilder للمخطط $itemId: ${snapshot.error}');
            return _buildChartErrorWidget(itemId, snapshot.error.toString());
          }

          return snapshot.data ?? _buildChartErrorWidget(itemId, 'لا توجد بيانات');
        },
      );
    } catch (e, stackTrace) {
      // تسجيل الخطأ بشكل أكثر تفصيلاً
      debugPrint('خطأ في بناء المخطط $itemId: $e');
      debugPrint('تفاصيل الخطأ: $stackTrace');

      return _buildChartErrorWidget(itemId, e.toString());
    }
  }

  /// بناء واجهة خطأ للمخطط
  Widget _buildChartErrorWidget(String itemId, String error) {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 32,
            ),
            const SizedBox(height: 12),
            Text(
              'خطأ في عرض المخطط',
              style: const TextStyle(
                color: Colors.red,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              error.length > 80 ? '${error.substring(0, 80)}...' : error,
              style: const TextStyle(
                color: Colors.grey,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: () {
                    // إعادة تحميل المخطط فقط بدلاً من إعادة تحميل كل المخططات
                    Future.microtask(() {
                      if (mounted) {
                        _updateSingleChart(itemId);
                      }
                    });
                  },
                  icon: const Icon(Icons.refresh, size: 16),
                  label: const Text('إعادة تحميل', style: TextStyle(fontSize: 12)),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  ),
                ),
                OutlinedButton.icon(
                  onPressed: () {
                    // تغيير نوع المخطط إلى النوع الافتراضي (شريطي)
                    Future.microtask(() {
                      if (mounted) {
                        _updateChartType(itemId, ChartType.bar);
                      }
                    });
                  },
                  icon: const Icon(Icons.bar_chart, size: 16),
                  label: const Text('تغيير النوع', style: TextStyle(fontSize: 12)),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// تحديث مخطط واحد فقط بدلاً من تحديث جميع المخططات
  void _updateSingleChart(String itemId) {
    final index = _dashboardItems.indexWhere((item) => item.id == itemId);
    if (index != -1) {
      final item = _dashboardItems[index];

      try {
        // تنظيف المفاتيح غير المستخدمة أولاً
        _cleanupChartKeys();

        // التأكد من وجود مفتاح للمخطط
        getOrCreateChartKey(itemId);

        // تحديث محتوى العنصر بناءً على العنوان
        Widget content;

        // التحقق إذا كانت البطاقة هي بطاقة المهام الملغاة
        if (item.title == 'عدد المهام الملغاة') {
          content = _buildCancelledTasksContent();
        } else {
          // تحديث محتوى العنصر بناءً على نوع المخطط
          content = _buildChartContent(item);
        }

        // تحديث العنصر المحدد فقط
        _dashboardItems[index] = DashboardItem(
          id: item.id,
          title: item.title,
          content: content,
          chartType: item.chartType,
          filterOptions: item.filterOptions,
        );

        debugPrint('تم تحديث المخطط $itemId بنجاح');
      } catch (e) {
        debugPrint('فشل تحديث المخطط $itemId: $e');
      }
    }
  }

  /// تحويل البيانات إلى تنسيق مناسب للمخطط الدائري
  Map<String, double> _convertToPieChartData(Map<String, dynamic> data) {
    final Map<String, double> result = {};

    if (data.containsKey('labels') && data.containsKey('values')) {
      final List<dynamic> labels = data['labels'];
      final List<dynamic> values = data['values'];

      for (int i = 0; i < labels.length && i < values.length; i++) {
        result[labels[i].toString()] = values[i].toDouble();
      }
    }

    return result;
  }

  /// تحويل البيانات إلى تنسيق مناسب للمخطط الخطي (Syncfusion Charts)
  Map<String, Map<String, double>> _convertToLineChartData(Map<String, dynamic> data) {
    final Map<String, Map<String, double>> result = {};

    if (data.containsKey('labels') && data.containsKey('values')) {
      final List<dynamic> labels = data['labels'];
      final List<dynamic> values = data['values'];

      final Map<String, double> dataPoints = {};
      for (int i = 0; i < labels.length && i < values.length; i++) {
        dataPoints[labels[i].toString()] = values[i].toDouble();
      }

      result['البيانات'] = dataPoints;
    }

    // إذا كانت البيانات تحتوي على سلاسل متعددة
    if (data.containsKey('series')) {
      final Map<String, dynamic> series = data['series'];
      for (final entry in series.entries) {
        if (entry.value is Map) {
          final Map<String, double> seriesData = {};
          final Map<String, dynamic> seriesMap = entry.value;

          for (final dataEntry in seriesMap.entries) {
            if (dataEntry.value is num) {
              seriesData[dataEntry.key] = dataEntry.value.toDouble();
            }
          }

          result[entry.key] = seriesData;
        }
      }
    }

    return result;
  }

  /// تحويل البيانات إلى تنسيق مناسب للمخطط الراداري
  Map<String, List<double>> _convertToRadarChartData(
      Map<String, dynamic> data) {
    final Map<String, List<double>> result = {};

    if (data.containsKey('labels') && data.containsKey('values')) {
      final List<dynamic> labels = data['labels'];
      final List<dynamic> values = data['values'];

      for (int i = 0; i < labels.length && i < values.length; i++) {
        if (values[i] is List) {
          // تحويل القائمة إلى قائمة من الأرقام العشرية
          final List<double> doubleList = [];
          for (var v in values[i] as List) {
            doubleList.add(v.toDouble());
          }
          result[labels[i].toString()] = doubleList;
        } else {
          // إذا كانت القيمة ليست قائمة، نضيف قائمة بقيمة واحدة
          result[labels[i].toString()] = [values[i].toDouble()];
        }
      }
    }

    return result;
  }

  /// التأكد من وجود الحد الأدنى من البيانات لمخطط الرادار (3 عناصر على الأقل)
  Map<String, List<double>> _ensureMinimumRadarData(
      Map<String, List<double>> data) {
    // إذا كانت البيانات فارغة، نعيد بيانات فارغة
    if (data.isEmpty) {
      return {};
    }

    // التأكد من أن كل سلسلة تحتوي على 3 قيم على الأقل
    for (final key in data.keys) {
      final List<double> values = data[key]!;
      if (values.length < 3) {
        // إضافة قيم إضافية إذا كان عدد القيم أقل من 3
        while (data[key]!.length < 3) {
          data[key]!.add(0.0); // قيمة صفرية
        }
      }
    }

    return data;
  }

  /// تحويل البيانات إلى تنسيق مناسب للمخطط الفقاعي
  Map<String, List<BubbleData>> _convertToBubbleChartData(
      Map<String, dynamic> data) {
    final Map<String, List<BubbleData>> result = {};

    // إذا كانت البيانات تحتوي على قائمة من البيانات الفقاعية
    if (data.containsKey('bubbleData')) {
      final List<dynamic> bubbleDataList = data['bubbleData'];

      // تجميع البيانات حسب الفئة
      final Map<String, List<BubbleData>> categorizedData = {};

      for (var item in bubbleDataList) {
        final double x = item['x']?.toDouble() ?? 0.0;
        final double y = item['y']?.toDouble() ?? 0.0;
        final double size = item['size']?.toDouble() ?? 5.0;
        final String category = item['category'] ?? 'غير مصنف';
        final String name = item['name'] ?? '';

        if (!categorizedData.containsKey(category)) {
          categorizedData[category] = [];
        }

        categorizedData[category]!.add(
          BubbleData(
            x: x,
            y: y,
            size: size,
            label: name,
          ),
        );
      }

      return categorizedData;
    }

    // إذا كانت البيانات بالتنسيق العادي (labels, values)
    if (data.containsKey('labels') && data.containsKey('values')) {
      final List<dynamic> labels = data['labels'];
      final List<dynamic> values = data['values'];

      // إنشاء سلسلة بيانات واحدة
      final List<BubbleData> bubbles = [];

      for (int i = 0; i < labels.length && i < values.length; i++) {
        bubbles.add(
          BubbleData(
            x: i.toDouble(),
            y: values[i].toDouble(),
            size: 5.0 + (values[i].toDouble() / 10),
            label: labels[i].toString(),
          ),
        );
      }

      result['data'] = bubbles;
    }

    return result;
  }

  /// تحويل البيانات إلى تنسيق مناسب للخريطة الحرارية
  List<List<HeatmapCell>> _convertToHeatmapData(Map<String, dynamic> data) {
    final List<List<HeatmapCell>> result = [];

    // إذا كانت البيانات تحتوي على مصفوفة ثنائية الأبعاد
    if (data.containsKey('data')) {
      final List<dynamic> heatmapRows = data['data'];

      for (var row in heatmapRows) {
        if (row is List) {
          final List<HeatmapCell> cellRow = [];

          for (var cell in row) {
            if (cell is Map) {
              final double value = cell['value']?.toDouble() ?? 0.0;
              final String? label = cell['label'];

              cellRow.add(HeatmapCell(
                value: value,
                label: label,
              ));
            } else {
              // إذا كانت الخلية قيمة بسيطة
              cellRow.add(HeatmapCell(
                value: cell is num ? cell.toDouble() : 0.0,
              ));
            }
          }

          result.add(cellRow);
        }
      }

      return result;
    }

    // إذا كانت البيانات تحتوي على مصفوفة ثنائية الأبعاد بتنسيق آخر
    if (data.containsKey('heatmapData')) {
      final List<dynamic> heatmapRows = data['heatmapData'];

      for (var row in heatmapRows) {
        if (row is List) {
          final List<HeatmapCell> cellRow = [];

          for (var cell in row) {
            if (cell is Map) {
              final double value = cell['value']?.toDouble() ?? 0.0;
              final String? label = cell['label'];

              cellRow.add(HeatmapCell(
                value: value,
                label: label,
              ));
            } else {
              // إذا كانت الخلية قيمة بسيطة
              cellRow.add(HeatmapCell(
                value: cell is num ? cell.toDouble() : 0.0,
              ));
            }
          }

          result.add(cellRow);
        }
      }

      return result;
    }

    // إذا كانت البيانات تحتوي على بيانات منسقة
    if (data.containsKey('formattedData')) {
      final List<dynamic> formattedRows = data['formattedData'];

      for (var row in formattedRows) {
        if (row is List) {
          final List<HeatmapCell> cellRow = [];

          for (var cell in row) {
            if (cell is Map) {
              final double value = cell['value']?.toDouble() ?? 0.0;
              final String? label = cell['label'] ?? '${cell['value']}';

              cellRow.add(HeatmapCell(
                value: value,
                label: label,
              ));
            }
          }

          result.add(cellRow);
        }
      }

      return result;
    }

    // إذا كانت البيانات بالتنسيق العادي (labels, values)
    // نقوم بإنشاء مصفوفة 7×4 (أيام الأسبوع × الأسابيع)
    if (data.containsKey('labels') && data.containsKey('values')) {
      // إنشاء مصفوفة فارغة 4×7
      for (int i = 0; i < 4; i++) {
        final List<HeatmapCell> row = [];
        for (int j = 0; j < 7; j++) {
          row.add(HeatmapCell(value: 0.0));
        }
        result.add(row);
      }

      // توزيع القيم على المصفوفة
      final List<dynamic> values = data['values'];
      int index = 0;

      for (int i = 0; i < 4 && index < values.length; i++) {
        for (int j = 0; j < 7 && index < values.length; j++) {
          final value = values[index];
          result[i][j] = HeatmapCell(
            value: value is num ? value.toDouble() : 0.0,
          );
          index++;
        }
      }
    }

    // لا نضيف بيانات افتراضية، نعيد البيانات كما هي

    return result;
  }

  /// التأكد من صحة بيانات الخريطة الحرارية
  List<List<HeatmapCell>> _ensureValidHeatmapData(
      List<List<HeatmapCell>> data) {
    // إذا كانت البيانات فارغة، نضيف بيانات افتراضية
    if (data.isEmpty) {
      final List<List<HeatmapCell>> defaultData = [];

      // إنشاء مصفوفة 4×7 بقيم افتراضية
      for (int i = 0; i < 4; i++) {
        final List<HeatmapCell> row = [];
        for (int j = 0; j < 7; j++) {
          // إنشاء قيم عشوائية بين 1 و 10
          final double value = 1.0 + (i * 7 + j) % 10;
          row.add(HeatmapCell(value: value));
        }
        defaultData.add(row);
      }

      return defaultData;
    }

    // التأكد من أن كل صف يحتوي على 7 خلايا على الأقل
    for (int i = 0; i < data.length; i++) {
      while (data[i].length < 7) {
        data[i].add(HeatmapCell(value: 0.0));
      }
    }

    // التأكد من وجود 4 صفوف على الأقل
    while (data.length < 4) {
      final List<HeatmapCell> newRow = [];
      for (int j = 0; j < 7; j++) {
        newRow.add(HeatmapCell(value: 0.0));
      }
      data.add(newRow);
    }

    return data;
  }

  /// تحويل البيانات إلى تنسيق مناسب للخريطة الشجرية
  Map<String, double> _convertToTreemapData(Map<String, dynamic> data) {
    final Map<String, double> result = {};

    // إذا كانت البيانات تحتوي على قائمة من عناصر الخريطة الشجرية
    if (data.containsKey('items')) {
      final List<dynamic> treemapItems = data['items'];

      for (var item in treemapItems) {
        if (item is Map) {
          final String name = item['name'] ?? 'غير معروف';
          final double value =
              item['value'] is num ? item['value'].toDouble() : 0.0;

          result[name] = value;
        }
      }

      return result;
    }

    // إذا كانت البيانات تحتوي على قائمة من عناصر الخريطة الشجرية بتنسيق آخر
    if (data.containsKey('treemapData')) {
      final List<dynamic> treemapItems = data['treemapData'];

      for (var item in treemapItems) {
        if (item is Map) {
          final String label = item['label'] ?? item['name'] ?? 'غير معروف';
          final double value =
              item['value'] is num ? item['value'].toDouble() : 0.0;

          result[label] = value;
        }
      }

      return result;
    }

    // إذا كانت البيانات بالتنسيق العادي (labels, values)
    if (data.containsKey('labels') && data.containsKey('values')) {
      final List<dynamic> labels = data['labels'];
      final List<dynamic> values = data['values'];

      for (int i = 0; i < labels.length && i < values.length; i++) {
        final String label = labels[i].toString();
        final double value = values[i] is num ? values[i].toDouble() : 0.0;

        result[label] = value;
      }
    }

    // لا نضيف بيانات افتراضية، نعيد البيانات كما هي

    return result;
  }

  /// تحديث فلتر المخطط
  Future<void> _updateChartFilter(
      String chartKey,
      DateTime? startDate,
      DateTime? endDate,
      TimeFilterType filterType,
      AdvancedFilterOptions? advancedOptions) async {
    setState(() {
      _chartStartDates[chartKey] = startDate;
      _chartEndDates[chartKey] = endDate;
      _chartFilterTypes[chartKey] = filterType;

      if (advancedOptions != null) {
        _advancedFilterOptions[chartKey] = advancedOptions;
      }
    });

    // تحديث محتوى العناصر
    await _updateDashboardItemsContent();

    // حفظ معايير التصفية
    await _saveChartFilterOptions(chartKey);
  }

  /// تحديث نوع المخطط
  void _updateChartType(String itemId, ChartType chartType) {
    final index = _dashboardItems.indexWhere((item) => item.id == itemId);

    if (index != -1) {
      // إعادة تعيين مفتاح المخطط لتجنب مشاكل التكرار
      if (_chartKeys.containsKey(itemId)) {
        _chartKeys.remove(itemId);
        debugPrint('تم إزالة المفتاح القديم للمخطط: $itemId');
      }

      // التأكد من وجود خيارات التصفية المتقدمة
      final item = _dashboardItems[index];
      final advancedFilterOptions =
          item.filterOptions ?? AdvancedFilterOptions();

      setState(() {
        _dashboardItems[index] = DashboardItem(
          id: item.id,
          title: item.title,
          content: item.content,
          chartType: chartType,
          filterOptions: advancedFilterOptions,
        );

        // تحديث نوع المخطط في المتغيرات
        final chartKey = _getChartKeyFromTitle(item.title);
        _chartTypes[chartKey] = chartType;

        // لا نقوم بإنشاء مفتاح جديد عند تغيير نوع المخطط لتجنب مشاكل التكرار
        // نستخدم المفتاح الموجود أو ننشئ مفتاح ثابت إذا لم يكن موجودًا
        if (!_chartKeys.containsKey(itemId)) {
          _chartKeys[itemId] = GlobalKey(debugLabel: 'chart_${itemId}_fixed');
          debugPrint('تم إنشاء مفتاح جديد للمخطط: $itemId');
        } else {
          debugPrint('تم استخدام المفتاح الموجود للمخطط: $itemId');
        }
      });

      // تأخير قصير قبل تحديث المحتوى لضمان تطبيق التغييرات
      Future.delayed(const Duration(milliseconds: 100), () async {
        if (mounted) {
          // تحديث محتوى العناصر
          await _updateDashboardItemsContent();

          // حفظ التخطيط الجديد
          await _saveDashboardLayout();
        }
      });

      // عرض رسالة للمستخدم
      Get.snackbar(
        'تم تغيير نوع المخطط',
        'تم تغيير نوع المخطط بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    }
  }

  /// حفظ معايير التصفية
  Future<void> _saveChartFilterOptions(String chartKey) async {
    try {
      final dashboardRepository = Get.find<DashboardRepository>();

      // حفظ معايير التصفية العادية
      final filterData = {
        'startDate': _chartStartDates[chartKey]?.millisecondsSinceEpoch,
        'endDate': _chartEndDates[chartKey]?.millisecondsSinceEpoch,
        'filterType': _chartFilterTypes[chartKey]?.index,
        'chartType': _chartTypes[chartKey]?.index,
      };

      await dashboardRepository.saveChartData('${chartKey}_filter', filterData);

      // حفظ معايير التصفية المتقدمة
      if (_advancedFilterOptions.containsKey(chartKey)) {
        await dashboardRepository.saveChartData(
          '${chartKey}_advanced_filter',
          _advancedFilterOptions[chartKey]!.toJson(),
        );
      }

      debugPrint('تم حفظ معايير التصفية للمخطط: $chartKey');
    } catch (e) {
      debugPrint('خطأ في حفظ معايير التصفية: $e');
    }
  }

  /// حفظ تخطيط لوحة المعلومات
  Future<void> _saveDashboardLayout() async {
    try {
      // تحويل العناصر إلى JSON
      final layoutData = _dashboardItems.map((item) => item.toJson()).toList();

      // حفظ التخطيط باستخدام المتحكم
      await _dashboardLayoutController.saveDashboardLayout(layoutData);

      debugPrint('تم حفظ تخطيط لوحة المعلومات: ${layoutData.length} عنصر');
    } catch (e) {
      debugPrint('خطأ في حفظ تخطيط لوحة المعلومات: $e');
    }
  }

  /// بناء بيانات تدفق سير العمل
  Map<String, dynamic> _buildWorkflowData(List<dynamic> tasks) {
    // تحليل المهام حسب مراحل سير العمل
    final Map<String, int> workflowStages = {
      'جديدة': 0,
      'قيد التنفيذ': 0,
      'مراجعة': 0,
      'مكتملة': 0,
      'ملغاة': 0,
    };

    if (tasks.isNotEmpty) {
      for (final task in tasks) {
        try {
          final status = task.status.toString().split('.').last;
          final statusName = _getStatusName(status);

          // تحديث العدد في المرحلة المناسبة
          if (workflowStages.containsKey(statusName)) {
            workflowStages[statusName] = workflowStages[statusName]! + 1;
          } else if (statusName == 'معلقة') {
            // إضافة المهام المعلقة إلى مرحلة المراجعة
            workflowStages['مراجعة'] = workflowStages['مراجعة']! + 1;
          }
        } catch (e) {
          debugPrint('خطأ في معالجة المهمة لتدفق سير العمل: $e');
        }
      }
    }

    // ترتيب المراحل حسب تسلسل سير العمل
    final List<String> orderedStages = [
      'جديدة',
      'قيد التنفيذ',
      'مراجعة',
      'مكتملة',
      'ملغاة',
    ];

    final List<String> labels = [];
    final List<double> values = [];

    for (final stage in orderedStages) {
      if (workflowStages[stage]! > 0) {
        labels.add(stage);
        values.add(workflowStages[stage]!.toDouble());
      }
    }

    return {
      'labels': labels,
      'values': values,
    };
  }

  /// بناء بيانات الجدول الزمني للمشاريع
  Map<String, dynamic> _buildProjectTimelineData(List<dynamic> tasks) {
    // تجميع المهام حسب الأقسام والتواريخ
    final Map<String, List<Map<String, dynamic>>> departmentTasks = {};

    if (tasks.isNotEmpty) {
      for (final task in tasks) {
        try {
          final departmentId = task.departmentId;
          final department = _departmentController.departments.firstWhereOrNull(
            (dept) => dept.id == departmentId,
          );

          final departmentName = department?.name ?? 'غير محدد';
          final startDate = task.createdAtDateTime;
          final endDate =
              task.dueDateDateTime ?? DateTime.now().add(const Duration(days: 7));

          if (!departmentTasks.containsKey(departmentName)) {
            departmentTasks[departmentName] = [];
          }

          departmentTasks[departmentName]!.add({
            'id': task.id,
            'title': task.title,
            'start': startDate,
            'end': endDate,
            'status': _getStatusName(task.status.toString().split('.').last),
          });
        } catch (e) {
          debugPrint('خطأ في معالجة المهمة للجدول الزمني: $e');
        }
      }
    }

    // تحويل البيانات إلى تنسيق مناسب للجدول الزمني
    final List<Map<String, dynamic>> timelineItems = [];

    departmentTasks.forEach((department, tasksList) {
      for (final taskData in tasksList) {
        timelineItems.add({
          'department': department,
          'task': taskData['title'],
          'start': taskData['start'].millisecondsSinceEpoch,
          'end': taskData['end'].millisecondsSinceEpoch,
          'status': taskData['status'],
        });
      }
    });

    return {
      'items': timelineItems,
      'departments': departmentTasks.keys.toList(),
    };
  }

  /// بناء بيانات تدفق البيانات بين الأقسام
  Map<String, dynamic> _buildDataFlowData(List<dynamic> tasks) {
    // تحليل تدفق المهام بين الأقسام والحالات
    final Map<String, Map<String, int>> departmentToStatus = {};

    if (tasks.isNotEmpty) {
      for (final task in tasks) {
        try {
          final departmentId = task.departmentId;
          final department = _departmentController.departments.firstWhereOrNull(
            (dept) => dept.id == departmentId,
          );

          final departmentName = department?.name ?? 'غير محدد';
          final status = task.status.toString().split('.').last;
          final statusName = _getStatusName(status);

          if (!departmentToStatus.containsKey(departmentName)) {
            departmentToStatus[departmentName] = {};
          }

          departmentToStatus[departmentName]![statusName] =
              (departmentToStatus[departmentName]![statusName] ?? 0) + 1;
        } catch (e) {
          debugPrint('خطأ في معالجة المهمة لتدفق البيانات: $e');
        }
      }
    }

    // تحويل البيانات إلى تنسيق مناسب لمخطط سانكي
    final List<Map<String, dynamic>> links = [];

    departmentToStatus.forEach((department, statusMap) {
      statusMap.forEach((status, count) {
        links.add({
          'source': department,
          'target': status,
          'value': count,
        });
      });
    });

    return {
      'links': links,
      'departments': departmentToStatus.keys.toList(),
      'statuses': ['جديدة', 'قيد التنفيذ', 'مكتملة', 'ملغاة', 'معلقة'],
    };
  }

  /// تصدير المخطط كصورة
  Future<void> _exportChart(String itemId, String title) async {
    try {
      // الحصول على المفتاح العام للمخطط
      final chartKey = _chartKeys[itemId];

      if (chartKey != null) {
        // تصدير المخطط كصورة
        final imagePath = await _chartExportService.exportToImage(
          itemId,
          title: title,
          globalKey: _chartKeys[itemId]!,
        );

        if (imagePath.isNotEmpty) {
          // عرض رسالة نجاح
          Get.snackbar(
            'تم التصدير بنجاح',
            'تم تصدير المخطط إلى: $imagePath',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
            duration: const Duration(seconds: 3),
          );
        }
      }
    } catch (e) {
      // عرض رسالة خطأ
      Get.snackbar(
        'خطأ في التصدير',
        'حدث خطأ أثناء تصدير المخطط: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );

      debugPrint('خطأ في تصدير المخطط: $e');
    }
  }

  /// بناء قسم الترحيب والبطاقات الإحصائية
  Widget _buildWelcomeSection() {
    // الحصول على اسم المستخدم الحالي
    final authController = Get.find<AuthController>();
    final currentUser = authController.currentUser.value;
    final userName = currentUser?.name ?? 'المستخدم';

    // حساب الإحصائيات مع تطبيع الحالات
    final totalTasks =
        _taskController.tasks.where((task) => !task.isDeleted).length;

    final completedTasks = _taskController.tasks.where((task) {
      if (task.isDeleted) return false;
      final normalizedStatus = task.status.toLowerCase().trim();
      return normalizedStatus == 'completed' || normalizedStatus == 'done' || normalizedStatus == 'مكتملة';
    }).length;

    final inProgressTasks = _taskController.tasks.where((task) {
      if (task.isDeleted) return false;
      final normalizedStatus = task.status.toLowerCase().trim();
      return normalizedStatus == 'in_progress' || normalizedStatus == 'inprogress' || normalizedStatus == 'in-progress' || normalizedStatus == 'قيد التنفيذ';
    }).length;

    debugPrint('📊 Dashboard إحصائيات: إجمالي=$totalTasks، مكتملة=$completedTasks، قيد التنفيذ=$inProgressTasks');
    final totalUsers = _userController.users.length;
    final totalDepartments = _departmentController.departments.length;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade200,
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // قسم الترحيب
          Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withValues(
                        red: Theme.of(context).primaryColor.r.toDouble(),
                        green: Theme.of(context).primaryColor.g.toDouble(),
                        blue: Theme.of(context).primaryColor.b.toDouble(),
                        alpha: 25.5, // 0.1 opacity = 25.5/255
                      ),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Icon(
                  Icons.dashboard,
                  color: Theme.of(context).primaryColor,
                  size: 30,
                ),
              ),
              const SizedBox(width: 16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'مرحباً، $userName',
                    style: AppStyles.titleLarge.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'مرحباً بك في لوحة القيادة، هنا يمكنك متابعة أداء العمل',
                    style: AppStyles.bodyMedium.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    currentUser?.role == UserRole.admin
                        ? 'الإحصائيات تعكس جميع مهام النظام'
                        : 'الإحصائيات تعكس المهام المخصصة لك فقط',
                    style: AppStyles.bodySmall.copyWith(
                      color: Colors.blue[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 24),

          // بطاقات الإحصائيات
          LayoutBuilder(
            builder: (context, constraints) {
              // تحديد عدد البطاقات في الصف بناءً على عرض الشاشة
              final screenWidth = constraints.maxWidth;
              int cardsPerRow = 5;

              if (screenWidth < 600) {
                cardsPerRow = 1;
              } else if (screenWidth < 900) {
                cardsPerRow = 2;
              } else if (screenWidth < 1200) {
                cardsPerRow = 3;
              } else if (screenWidth < 1500) {
                cardsPerRow = 4;
              }

              return Wrap(
                spacing: 16,
                runSpacing: 16,
                children: [
                  _buildStatCard(
                    'إجمالي المهام',
                    totalTasks.toString(),
                    Icons.task,
                    Colors.blue,
                    constraints.maxWidth / cardsPerRow - 20,
                  ),
                  _buildStatCard(
                    'المهام المكتملة',
                    completedTasks.toString(),
                    Icons.check_circle,
                    Colors.green,
                    constraints.maxWidth / cardsPerRow - 20,
                  ),
                  _buildStatCard(
                    'المهام قيد التنفيذ',
                    inProgressTasks.toString(),
                    Icons.pending_actions,
                    Colors.orange,
                    constraints.maxWidth / cardsPerRow - 20,
                  ),
                  _buildStatCard(
                    'المستخدمين',
                    totalUsers.toString(),
                    Icons.people,
                    Colors.purple,
                    constraints.maxWidth / cardsPerRow - 20,
                  ),
                  _buildStatCard(
                    'الأقسام',
                    totalDepartments.toString(),
                    Icons.business,
                    Colors.teal,
                    constraints.maxWidth / cardsPerRow - 20,
                  ),
                  GestureDetector(
                    onTap: () {
                      Get.back();
                      Get.toNamed(AppRoutes.userDashboard);
                    },
                    child: _buildStatCard(
                      'لوحه تحكم المستخدم',
                      "         ---->",
                      Icons.analytics_outlined,
                      Colors.deepOrangeAccent,
                      constraints.maxWidth / cardsPerRow - 20,
                    ),
                  ),
                ],
              );
            },
          ),

          const SizedBox(height: 24),
          const Divider(),
          const SizedBox(height: 8),
        ],
      ),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(
      String title, String value, IconData icon, Color color, double width) {
    return Container(
      width: width,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(
              red: Colors.grey.r.toDouble(),
              green: Colors.grey.g.toDouble(),
              blue: Colors.grey.b.toDouble(),
              alpha: 25.5, // 0.1 opacity = 25.5/255
            ),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
        border: Border.all(
          color: color.withValues(
            red: color.r.toDouble(),
            green: color.g.toDouble(),
            blue: color.b.toDouble(),
            alpha: 76.5, // 0.3 opacity = 76.5/255
          ),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: color.withValues(
                red: color.r.toDouble(),
                green: color.g.toDouble(),
                blue: color.b.toDouble(),
                alpha: 25.5, // 0.1 opacity = 25.5/255
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: color,
              size: 30,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppStyles.bodyMedium.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: AppStyles.titleLarge.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء محتوى لوحة المعلومات
  Widget _buildDashboardContent() {
    // عرض مؤشر التحميل إذا كانت البيانات قيد التحميل الأولي
    if (_isInitializing) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              'جاري تحميل لوحة المعلومات...',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'يرجى الانتظار قليلاً',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    // عرض رسالة خطأ مع إمكانية إعادة المحاولة
    if (_errorMessage != null && _errorMessage!.isNotEmpty && _dashboardItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ في تحميل البيانات',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.red.shade700,
              ),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _errorMessage!,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _retryLoadData,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      );
    }

    // المسار الرئيسي لعرض لوحة المعلومات
    if (_dashboardItems.isNotEmpty && (_errorMessage == null || _errorMessage!.isEmpty)) {
      return Stack(
        children: [
          // المحتوى الرئيسي للوحة المعلومات مع جزء ثابت وجزء قابل للتمرير
          Column(
            children: [
              // الجزء الثابت - قسم الترحيب والبطاقات
              _buildWelcomeSection(),

              // الجزء القابل للتمرير - المخططات
              Expanded(
                child: SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        // حساب عدد الأعمدة بناءً على عرض الشاشة
                        int crossAxisCount = 1;
                        if (constraints.maxWidth >= 1200) {
                          crossAxisCount = 3;
                        } else if (constraints.maxWidth >= 800) {
                          crossAxisCount = 2;
                        }

                        return GridView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: crossAxisCount,
                            childAspectRatio: 1.2,
                            crossAxisSpacing: 16,
                            mainAxisSpacing: 16,
                          ),
                          itemCount: _dashboardItems.length,
                          itemBuilder: (context, index) {
                            final item = _dashboardItems[index];
                            return _buildSimpleChartCard(item);
                          },
                        );
                      },
                    ),
                  ),
                ),
              ),
            ],
          ),
          // عرض مؤشر تحميل إضافي إذا كانت هناك عمليات تحميل في الخلفية
          if (_isLoading)
            Positioned(
              top: 16,
              right: 16,
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.shade100.withValues(alpha: 0.8),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.blue.shade700),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'تحديث البيانات...',
                      style: TextStyle(fontSize: 12, color: Colors.blue.shade900),
                    ),
                  ],
                ),
              ),
            ),
        ],
      );
    }
    // إذا كان هناك رسالة خطأ، عرضها للمستخدم
    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: const TextStyle(
                color: Colors.red,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadData,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    // عرض رسالة إذا كانت قائمة العناصر فارغة
    if (_dashboardItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.dashboard_outlined,
              color: Colors.grey,
              size: 48,
            ),
            const SizedBox(height: 16),
            const Text(
              'لا توجد عناصر في لوحة المعلومات',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _createDefaultDashboardLayout,
              child: const Text('إنشاء لوحة معلومات افتراضية'),
            ),
          ],
        ),
      );
    }

    // إذا لم تكن هناك حالات أخرى، عرض رسالة خطأ
    return const Center(
      child: Text('حدث خطأ غير متوقع'),
    );
  }

  /// بناء بطاقة مخطط بسيطة
  Widget _buildSimpleChartCard(DashboardItem item) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // رأس البطاقة
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    item.title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.fullscreen, color: Colors.white),
                  onPressed: () => _showChartDetails(item),
                  tooltip: 'عرض التفاصيل',
                ),
              ],
            ),
          ),
          // محتوى المخطط
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: _buildChartContent(item),
            ),
          ),
        ],
      ),
    );
  }



  // تم إزالة دالة _handleLayoutChanged لأنها لم تعد مستخدمة

  // تم إزالة الدوال التي لم تعد مستخدمة بعد تنفيذ المكون الجديد
  // _buildDashboardItemWidget
  // _buildEditableDashboardItem
  // _buildStaticDashboardItem

  /// بناء زر الإجراء العائم
  Widget _buildFloatingActionButton() {
    // زر إضافة عنصر جديد
    return FloatingActionButton(
      heroTag: 'addItem',
      onPressed: _addNewDashboardItem,
      backgroundColor: Colors.green,
      tooltip: 'إضافة عنصر جديد',
      child: const Icon(Icons.add),
    );
  }

  /// إضافة عنصر جديد إلى لوحة المعلومات
  void _addNewDashboardItem() async {
    // عرض مربع حوار Monday.com لإضافة عنصر جديد
    final result = await Get.dialog<widget_model.DashboardWidget>(
      MondayStyleAddWidgetDialog(
        dashboardId: 'main_dashboard', // معرف لوحة المعلومات الرئيسية
      ),
    );

    if (result != null) {
      // تحويل DashboardWidgetModel إلى DashboardItem
      final newItem = DashboardItem(
        id: result.id.toString(),
        title: result.title,
        content: Container(
          alignment: Alignment.center,
          child: const Text('جاري تحميل البيانات...'),
        ),
        chartType: _convertWidgetTypeToChartType(result.type),
        filterOptions: AdvancedFilterOptions(),
      );

      // إضافة العنصر إلى القائمة
      setState(() {
        _dashboardItems.add(newItem);
      });

      // تحديث محتوى العنصر الجديد
      await _updateDashboardItemsContent();

      // حفظ التخطيط الجديد
      _saveDashboardLayout();

      // اهتزاز عند إضافة عنصر جديد
      HapticFeedback.heavyImpact();

      // عرض رسالة تأكيد
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تمت إضافة "${result.title}" بنجاح'),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            width: 250,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  // تم إزالة دالة _deleteDashboardItem لأنها لم تعد مستخدمة

  /// تحويل نوع العنصر من DashboardWidgetType إلى ChartType
  ChartType _convertWidgetTypeToChartType(widget_model.DashboardWidgetType widgetType) {
    switch (widgetType) {
      case widget_model.DashboardWidgetType.barChart:
        return ChartType.bar;
      case widget_model.DashboardWidgetType.lineChart:
        return ChartType.line;
      case widget_model.DashboardWidgetType.pieChart:
        return ChartType.pie;
      case widget_model.DashboardWidgetType.table:
        return ChartType.table;
      case widget_model.DashboardWidgetType.kpiCard:
        return ChartType.gauge;
      case widget_model.DashboardWidgetType.areaChart:
        return ChartType.area;
      case widget_model.DashboardWidgetType.gaugeChart:
        return ChartType.gauge;
      case widget_model.DashboardWidgetType.heatMap:
        return ChartType.heatmap;
      case widget_model.DashboardWidgetType.radarChart:
        return ChartType.radar;
      case widget_model.DashboardWidgetType.bubbleChart:
        return ChartType.bubble;
      case widget_model.DashboardWidgetType.ganttChart:
        return ChartType.gantt;
      case widget_model.DashboardWidgetType.treeMap:
        return ChartType.treemap;
      default:
        return ChartType.bar; // افتراضي
    }
  }

  // تم حذف دالة _showChartTypeSelector لأنها لم تعد مستخدمة
  // يتم الآن استخدام مربع حوار Monday.com بدلاً منها

  // تم حذف دالة _showEditTitleDialog لأنها لم تعد مستخدمة
  // يتم الآن تعديل العنوان من خلال مربع حوار Monday.com

  /// عرض تفاصيل المخطط
  // void _showChartDetails(DashboardItem item) {
  //   // عرض مربع حوار التفاصيل
  //   Get.dialog(
  //     Dialog(
  //       child: Container(
  //         width: MediaQuery.of(context).size.width * 0.8,
  //         height: MediaQuery.of(context).size.height * 0.8,
  //         padding: const EdgeInsets.all(16),
  //         child: Container(
  //           padding: const EdgeInsets.all(16),
  //           child: Column(
  //             children: [
  //               Text(item.title,
  //                   style: const TextStyle(
  //                       fontSize: 18, fontWeight: FontWeight.bold)),
  //               const SizedBox(height: 16),
  //               Expanded(
  //                 child: _buildChartContent(item),
  //               ),
  //               const SizedBox(height: 16),
  //               Row(
  //                 mainAxisAlignment: MainAxisAlignment.end,
  //                 children: [
  //                   TextButton(
  //                     onPressed: () => Get.back(),
  //                     child: const Text('إغلاق'),
  //                   ),
  //                   const SizedBox(width: 8),
  //                   ElevatedButton(
  //                     onPressed: () => _exportChart(item.id, item.title),
  //                     child: const Text('تصدير'),
  //                   ),
  //                 ],
  //               ),
  //             ],
  //           ),
  //         ),
  //       ),
  //     ),
  //   );
  // }

void _showChartDetails(DashboardItem item) {
  // متغيرات محلية لحالة الفلتر ونوع المخطط
  ChartType chartType = item.chartType ?? ChartType.bar;
  DateTime? startDate;
  DateTime? endDate;
  TimeFilterType filterType = TimeFilterType.month;
  AdvancedFilterOptions advancedFilterOptions = item.filterOptions ?? AdvancedFilterOptions();

  Get.dialog(
    Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.8,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: StatefulBuilder(
          builder: (context, setState) {
            // بناء محتوى المخطط بناءً على الحالة المحلية
            Widget chartContent = _buildChartContent(
              DashboardItem(
                 id: item.id,
                title: item.title,
                chartType: chartType,
                 filterOptions: advancedFilterOptions,
                content: item.content,
               ),
            );

            return Column(
              children: [
                // Text(item.title,
                //     style: const TextStyle(
                //         fontSize: 18, fontWeight: FontWeight.bold)),
                const SizedBox(height: 16),
                // شريط الفلترة وتغيير نوع المخطط (مثال: UnifiedFilterExportWidget)
                UnifiedFilterExportWidget(
                  title: item.title,
                  chartKey: _getChartKeyFromTitle(item.title),
                  startDate: startDate,
                  endDate: endDate,
                  filterType: filterType,
                  chartType: chartType,
                  advancedFilterOptions: advancedFilterOptions,
                  onFilterChanged: (start, end, type, chartKey) {
                    setState(() {
                      startDate = start;
                      endDate = end;
                      filterType = type;
                    });
                  },
                  onChartTypeChanged: (type, chartKey) {
                    setState(() {
                      chartType = type;
                    });
                  },
                  onExport: (format, title) {
                    _exportChart(item.id, title);
                  },
                  onCustomizeChart: () {
                    _showChartCustomizationDialog(
                      context: context,
                      chartType: chartType,
                      currentColors: _getChartColors(item.id),
                      currentDisplayOptions: _getChartDisplayOptions(item.id),
                      onColorsChanged: (colors) {},
                      onDisplayOptionsChanged: (options) {},
                    );
                  },
                ),
                const SizedBox(height: 16),
                Expanded(child: chartContent),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Get.back(),
                      child: const Text('إغلاق'),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: () => _exportChart(item.id, item.title),
                      child: const Text('تصدير'),
                    ),
                  ],
                ),
              ],
            );
          },
        ),
      ),
    ),
  );
}



  /// بناء مخطط قمعي (Funnel Chart)
  Widget _buildFunnelChart(
    DashboardItem item,
    String chartKey,
    DateTime? startDate,
    DateTime? endDate,
    TimeFilterType filterType,
    AdvancedFilterOptions advancedFilterOptions,
  ) {
    // تعيين ألوان مخصصة لكل مرحلة
    final Map<String, Color> stageColors = {
      'جديدة': Colors.blue,
      'قيد التنفيذ': Colors.orange,
      'مراجعة': Colors.purple,
      'مكتملة': Colors.green,
      'ملغاة': Colors.red,
      'معلقة': Colors.amber,
    };

    // خيارات العرض
    final Map<String, bool> displayOptions = {
      'showValues': true,
      'showPercentages': true,
    };

    // استرجاع الإعدادات المخزنة إذا كانت موجودة
    final savedColors = _getChartColors(item.id);
    final savedOptions = _getChartDisplayOptions(item.id);

    if (savedColors.isNotEmpty) {
      savedColors.forEach((key, value) {
        stageColors[key] = value;
      });
    }

    if (savedOptions.isNotEmpty) {
      savedOptions.forEach((key, value) {
        displayOptions[key] = value;
      });
    }

    return Column(
      children: [
        // مكون الفلترة والتصدير
        UnifiedFilterExportWidget(
          title: item.title,
          chartKey: chartKey,
          startDate: startDate,
          endDate: endDate,
          filterType: filterType,
          chartType: ChartType.funnel,
          advancedFilterOptions: advancedFilterOptions,
          onFilterChanged: (start, end, type, chartKey) {
            _updateChartFilter(
                chartKey, start, end, type, advancedFilterOptions);
          },
          onChartTypeChanged: (type, chartKey) {
            _updateChartType(item.id, type);
          },
          onExport: (format, title) {
            _exportChart(item.id, title);
          },
          onCustomizeChart: () {
            _showChartCustomizationDialog(
              context: context,
              chartType: ChartType.funnel,
              currentColors: stageColors,
              currentDisplayOptions: displayOptions,
              onColorsChanged: (colors) {
                _saveChartColors(item.id, colors);
                setState(() {});
              },
              onDisplayOptionsChanged: (options) {
                _saveChartDisplayOptions(item.id, options);
                setState(() {});
              },
            );
          },
        ),

        // المخطط القمعي
        Expanded(
          child: _buildSafeChart(
            item.id,
            () {
              // الحصول على بيانات تدفق سير العمل من قاعدة البيانات
              final workflowData = _getChartData(
                  chartKey, startDate, endDate, advancedFilterOptions);

              // تحويل البيانات إلى تنسيق مناسب للمخطط القمعي
              final Map<String, double> funnelData = {};

              if (workflowData.containsKey('labels') &&
                  workflowData.containsKey('values') &&
                  workflowData['labels'] is List &&
                  workflowData['values'] is List) {
                final labels = workflowData['labels'] as List;
                final values = workflowData['values'] as List;

                for (int i = 0; i < labels.length && i < values.length; i++) {
                  funnelData[labels[i].toString()] = values[i] is double
                      ? values[i]
                      : (values[i] as num).toDouble();
                }
              }

              debugPrint('بيانات المخطط القمعي: ${funnelData.length} عنصر');

              // التحقق من وجود بيانات
              if (funnelData.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.filter_alt,
                        size: 48,
                        color: Colors.grey,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد بيانات للعرض',
                        style: TextStyle(
                          fontSize: 16,
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                        ),
                      ),
                    ],
                  ),
                );
              }

              return EnhancedFunnelChart(
                data: funnelData,
                title: '',
                itemColors: stageColors,
                showValues: displayOptions['showValues'] ?? true,
                showPercentages: displayOptions['showPercentages'] ?? true,
              );
            },
          ),
        ),
      ],
    );
  }

  /// بناء مخطط جانت (Gantt Chart)
  Widget _buildGanttChart(
    DashboardItem item,
    String chartKey,
    DateTime? startDate,
    DateTime? endDate,
    TimeFilterType filterType,
    AdvancedFilterOptions advancedFilterOptions,
  ) {
    // تعيين ألوان مخصصة لكل حالة مهمة
    final Map<String, Color> taskStatusColors = {
      'جديدة': Colors.blue,
      'قيد التنفيذ': Colors.orange,
      'مراجعة': Colors.purple,
      'مكتملة': Colors.green,
      'ملغاة': Colors.red,
      'معلقة': Colors.amber,
    };

    // خيارات العرض
    final Map<String, bool> displayOptions = {
      'showCompletionPercentage': true,
      'showDates': true,
    };

    // استرجاع الإعدادات المخزنة إذا كانت موجودة
    final savedColors = _getChartColors(item.id);
    final savedOptions = _getChartDisplayOptions(item.id);

    if (savedColors.isNotEmpty) {
      savedColors.forEach((key, value) {
        taskStatusColors[key] = value;
      });
    }

    if (savedOptions.isNotEmpty) {
      savedOptions.forEach((key, value) {
        displayOptions[key] = value;
      });
    }

    return Column(
      children: [
        // مكون الفلترة والتصدير
        UnifiedFilterExportWidget(
          title: item.title,
          chartKey: chartKey,
          startDate: startDate,
          endDate: endDate,
          filterType: filterType,
          chartType: ChartType.gantt,
          advancedFilterOptions: advancedFilterOptions,
          onFilterChanged: (start, end, type, chartKey) {
            _updateChartFilter(
                chartKey, start, end, type, advancedFilterOptions);
          },
          onChartTypeChanged: (type, chartKey) {
            _updateChartType(item.id, type);
          },
          onExport: (format, title) {
            _exportChart(item.id, title);
          },
          onCustomizeChart: () {
            _showChartCustomizationDialog(
              context: context,
              chartType: ChartType.gantt,
              currentColors: taskStatusColors,
              currentDisplayOptions: displayOptions,
              onColorsChanged: (colors) {
                _saveChartColors(item.id, colors);
                setState(() {});
              },
              onDisplayOptionsChanged: (options) {
                _saveChartDisplayOptions(item.id, options);
                setState(() {});
              },
            );
          },
        ),

        // مخطط جانت
        Expanded(
          child: _buildSafeChart(
            item.id,
            () {
              // الحصول على بيانات الجدول الزمني من قاعدة البيانات
              final timelineData = _getChartData(
                  chartKey, startDate, endDate, advancedFilterOptions);

              // قائمة المهام لمخطط جانت
              final List<GanttTask> ganttTasks = [];

              // التحقق من وجود بيانات
              if (timelineData.isEmpty ||
                  !timelineData.containsKey('items') ||
                  timelineData['items'] is! List ||
                  (timelineData['items'] as List).isEmpty) {
                // إذا لم تكن هناك بيانات، نحاول الحصول على المهام مباشرة من المتحكم
                final tasks = _taskController.tasks.where((task) {
                  // تصفية المهام حسب التاريخ
                  final taskDate = task.createdAtDateTime;
                  if (startDate != null && taskDate.isBefore(startDate)) {
                    return false;
                  }
                  if (endDate != null && taskDate.isAfter(endDate)) {
                    return false;
                  }
                  // تصفية المهام حسب القسم
                  if (advancedFilterOptions.departmentIds != null &&
                      advancedFilterOptions.departmentIds!.isNotEmpty &&
                      !advancedFilterOptions.departmentIds!
                          .contains(task.departmentId)) {
                    return false;
                  }
                  return true;
                }).toList();

                debugPrint('عدد المهام لمخطط جانت: ${tasks.length}');

                if (tasks.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.event_busy,
                          size: 48,
                          color: Colors.grey,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'لا توجد مهام للعرض',
                          style: TextStyle(
                            fontSize: 16,
                            color: Theme.of(context).textTheme.bodyLarge?.color,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                // تحويل المهام إلى نموذج GanttTask
                for (final task in tasks) {
                  // تحديد تاريخ البداية والنهاية
                  final taskStartDate = task.startDateDateTime ?? task.createdAtDateTime;
                  final taskEndDate = task.completedAt != null
                      ? DateTime.fromMillisecondsSinceEpoch(task.completedAt! * 1000)
                      : task.dueDateDateTime ?? taskStartDate.add(const Duration(days: 3));

                  // تحديد لون المهمة بناءً على الحالة
                  final statusName = task.status.toString().split('.').last;
                  final statusKey = _getStatusName(statusName);
                  final taskColor = taskStatusColors[statusKey] ??
                      _getTaskStatusColor(task.status );

                  // إضافة مهمة جانت
                  ganttTasks.add(GanttTask(
                    id: task.id.toString(),
                    title: task.title,
                    startDate: taskStartDate,
                    endDate: taskEndDate,
                    completionPercentage: task.completionPercentage.toDouble(),
                    color: taskColor,
                  ));
                }
              } else {
                // استخدام البيانات من قاعدة البيانات
                final items = timelineData['items'] as List;

                for (final item in items) {
                  try {
                    // تحويل التواريخ من timestamp إلى DateTime
                    final startTimestamp = item['start'] as int;
                    final endTimestamp = item['end'] as int;
                    final taskStartDate =
                        DateTime.fromMillisecondsSinceEpoch(startTimestamp);
                    final taskEndDate =
                        DateTime.fromMillisecondsSinceEpoch(endTimestamp);

                    // تحديد لون المهمة بناءً على الحالة
                    final status = item['status'] as String? ?? 'قيد التنفيذ';
                    final taskColor = taskStatusColors[status] ?? Colors.blue;

                    // إضافة مهمة جانت
                    ganttTasks.add(GanttTask(
                      id: item['id'] as String? ?? 'task_${ganttTasks.length}',
                      title: item['task'] as String? ?? 'مهمة بدون عنوان',
                      startDate: taskStartDate,
                      endDate: taskEndDate,
                      completionPercentage:
                          item['completion'] as double? ?? 0.0,
                      color: taskColor,
                    ));
                  } catch (e) {
                    debugPrint('خطأ في معالجة مهمة جانت: $e');
                  }
                }
              }

              // التحقق من وجود مهام بعد المعالجة
              if (ganttTasks.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.event_busy,
                        size: 48,
                        color: Colors.grey,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد مهام للعرض',
                        style: TextStyle(
                          fontSize: 16,
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                        ),
                      ),
                    ],
                  ),
                );
              }

              // عرض مخطط جانت
              return GanttChart(
                tasks: ganttTasks,
                title: '',
                startDate: startDate,
                endDate: endDate,
                titleColumnWidth: 200,
                rowHeight: 40,
                showCompletionPercentage:
                    displayOptions['showCompletionPercentage'] ?? true,
                showDates: displayOptions['showDates'] ?? true,
                onTaskTap: (ganttTask) {
                  // عرض تفاصيل المهمة
                  _showTaskDetailsFromGanttTask(ganttTask);
                },
              );
            },
          ),
        ),
      ],
    );
  }

  /// عرض تفاصيل المهمة من مهمة جانت
  void _showTaskDetailsFromGanttTask(GanttTask ganttTask) {
    // محاولة العثور على المهمة الأصلية في قائمة المهام
    final task = _taskController.tasks.firstWhereOrNull(
      (task) => task.id.toString() == ganttTask.id,
    );

    if (task != null) {
      // إذا وجدنا المهمة، نعرض تفاصيلها
      _showTaskDetailsDialog(task);
    } else {
      // إذا لم نجد المهمة، نعرض تفاصيل مهمة جانت فقط
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(ganttTask.title),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // معلومات المهمة
                _buildInfoRow(Icons.calendar_today, 'تاريخ البدء',
                    DateFormat('yyyy-MM-dd').format(ganttTask.startDate)),
                const SizedBox(height: 8),
                _buildInfoRow(Icons.event, 'تاريخ الانتهاء',
                    DateFormat('yyyy-MM-dd').format(ganttTask.endDate)),
                const SizedBox(height: 8),
                _buildInfoRow(Icons.percent, 'نسبة الإنجاز',
                    '${ganttTask.completionPercentage.toStringAsFixed(1)}%'),

                // شريط التقدم
                const SizedBox(height: 16),
                const Text('التقدم:',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: ganttTask.completionPercentage / 100,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    _getProgressColor(ganttTask.completionPercentage / 100),
                  ),
                  minHeight: 10,
                  borderRadius: BorderRadius.circular(5),
                ),

                // معلومات إضافية
                if (ganttTask.additionalData != null &&
                    ganttTask.additionalData!.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  const Text('معلومات إضافية:',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  ...ganttTask.additionalData!.entries.map((entry) =>
                      _buildInfoRow(Icons.info_outline, entry.key,
                          entry.value.toString())),
                ],
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إغلاق'),
            ),
          ],
        ),
      );
    }
  }

  /// بناء صف معلومات مع لون اختياري للقيمة
  Widget _buildInfoRow(IconData icon, String label, String value,
      {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Icon(icon, size: 18, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Text('$label:', style: const TextStyle(fontWeight: FontWeight.bold)),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: valueColor ?? Colors.black87,
                fontWeight:
                    valueColor != null ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على لون التقدم بناءً على النسبة
  Color _getProgressColor(double progress) {
    if (progress < 0.3) {
      return Colors.red;
    } else if (progress < 0.7) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }

  /// عرض تفاصيل المهمة في مربع حوار
  void _showTaskDetailsDialog(Task task) {
    // الحصول على اسم القسم
    final department = _departmentController.departments.firstWhereOrNull(
      (dept) => dept.id == task.departmentId,
    );
    final departmentName = department?.name ?? 'غير محدد';

    // الحصول على اسم المستخدم المسؤول
    final assignedUser = _userController.users.firstWhereOrNull(
      (user) => user.id == task.assigneeId,
    );
    final assignedUserName = assignedUser?.name ?? 'غير محدد';

    // الحصول على اسم الحالة
    final statusName = _getStatusName(task.status.toString().split('.').last);

    // الحصول على لون الحالة
    final statusColor = _getTaskStatusColor(task.status);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: statusColor,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(child: Text(task.title)),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // الوصف
              if (task.description != null && task.description!.isNotEmpty) ...[
                const Text('الوصف:',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(task.description!),
                ),
                const SizedBox(height: 16),
              ],

              // معلومات المهمة
              _buildInfoRow(Icons.category, 'القسم', departmentName),
              const SizedBox(height: 8),
              _buildInfoRow(Icons.person, 'المسؤول', assignedUserName),
              const SizedBox(height: 8),
              _buildInfoRow(Icons.flag, 'الحالة', statusName,
                  valueColor: statusColor),
              const SizedBox(height: 8),
              _buildInfoRow(Icons.percent, 'نسبة الإكمال',
                  '${task.completionPercentage.toStringAsFixed(0)}%'),

              // شريط التقدم
              const SizedBox(height: 8),
              LinearProgressIndicator(
                value: task.completionPercentage / 100,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                  _getProgressColor(task.completionPercentage / 100),
                ),
                minHeight: 10,
                borderRadius: BorderRadius.circular(5),
              ),

              // التواريخ
              const SizedBox(height: 16),
              const Text('التواريخ:',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              _buildInfoRow(Icons.date_range, 'تاريخ الإنشاء',
                  DateFormat('yyyy-MM-dd').format(task.createdAtDateTime)),
              if (task.startDate != null) ...[
                const SizedBox(height: 8),
                _buildInfoRow(Icons.calendar_today, 'تاريخ البدء',
                    DateFormat('yyyy-MM-dd').format(task.startDateDateTime!)),
              ],
              if (task.dueDate != null) ...[
                const SizedBox(height: 8),
                _buildInfoRow(Icons.event, 'تاريخ الاستحقاق',
                    DateFormat('yyyy-MM-dd').format(task.dueDateDateTime!)),
              ],
              if (task.completedAt != null) ...[
                const SizedBox(height: 8),
                _buildInfoRow(Icons.check_circle, 'تاريخ الإكمال',
                    DateFormat('yyyy-MM-dd').format(DateTime.fromMillisecondsSinceEpoch(task.completedAt! * 1000))),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// الحصول على لون حالة المهمة
  Color _getTaskStatusColor(String statusId) {
    final status = DashboardTaskStatus.values.firstWhere(
      (s) => s.name == statusId,
      orElse: () => DashboardTaskStatus.pending,
    );

    switch (status) {
      case DashboardTaskStatus.pending:
        return Colors.blue;
      case DashboardTaskStatus.inProgress:
        return Colors.orange;
      case DashboardTaskStatus.waitingForInfo:
        return Colors.purple;
      case DashboardTaskStatus.completed:
        return Colors.green;
      case DashboardTaskStatus.cancelled:
        return Colors.red;
      case DashboardTaskStatus.news:
        return Colors.teal;
    }
  }

  /// عرض مربع حوار تخصيص المخطط
  void _showChartCustomizationDialog({
    required BuildContext context,
    required ChartType chartType,
    required Map<String, Color> currentColors,
    required Map<String, bool> currentDisplayOptions,
    required Function(Map<String, Color>) onColorsChanged,
    required Function(Map<String, bool>) onDisplayOptionsChanged,
  }) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.8,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: const EdgeInsets.all(16),
          child: ChartCustomizationPanel(
            chartType: chartType,
            currentColors: currentColors,
            currentDisplayOptions: currentDisplayOptions,
            onColorsChanged: onColorsChanged,
            onDisplayOptionsChanged: onDisplayOptionsChanged,
          ),
        ),
      ),
    );
  }

  /// الحصول على ألوان المخطط المخزنة
  Map<String, Color> _getChartColors(String chartId) {
    final Map<String, Color> colors = {};

    // في التطبيق الحقيقي، يمكن استرجاع الألوان من التخزين المحلي أو قاعدة البيانات
    // هنا نستخدم مثالاً بسيطاً

    return colors;
  }

  /// حفظ ألوان المخطط
  void _saveChartColors(String chartId, Map<String, Color> colors) {
    // في التطبيق الحقيقي، يمكن حفظ الألوان في التخزين المحلي أو قاعدة البيانات
    debugPrint('تم حفظ ألوان المخطط $chartId: $colors');
  }

  /// الحصول على خيارات عرض المخطط المخزنة
  Map<String, bool> _getChartDisplayOptions(String chartId) {
    final Map<String, bool> options = {};

    // في التطبيق الحقيقي، يمكن استرجاع الخيارات من التخزين المحلي أو قاعدة البيانات
    // هنا نستخدم مثالاً بسيطاً

    return options;
  }

  /// حفظ خيارات عرض المخطط
  void _saveChartDisplayOptions(String chartId, Map<String, bool> options) {
    // في التطبيق الحقيقي، يمكن حفظ الخيارات في التخزين المحلي أو قاعدة البيانات
    debugPrint('تم حفظ خيارات عرض المخطط $chartId: $options');
  }

  /// بناء مخطط سانكي (Sankey Chart)
  Widget _buildSankeyChart(
    DashboardItem item,
    String chartKey,
    DateTime? startDate,
    DateTime? endDate,
    TimeFilterType filterType,
    AdvancedFilterOptions advancedFilterOptions,
  ) {
    // تعيين ألوان مخصصة للأقسام
    final Map<String, Color> departmentColors = {
      'الإدارة': Colors.blue,
      'المبيعات': Colors.green,
      'التسويق': Colors.orange,
      'تطوير المنتجات': Colors.purple,
      'خدمة العملاء': Colors.teal,
      'الموارد البشرية': Colors.amber,
    };

    // خيارات العرض
    final Map<String, bool> displayOptions = {
      'showLinkValues': false,
      'showNodeNames': true,
    };

    // استرجاع الإعدادات المخزنة إذا كانت موجودة
    final savedColors = _getChartColors(item.id);
    final savedOptions = _getChartDisplayOptions(item.id);

    if (savedColors.isNotEmpty) {
      savedColors.forEach((key, value) {
        departmentColors[key] = value;
      });
    }

    if (savedOptions.isNotEmpty) {
      savedOptions.forEach((key, value) {
        displayOptions[key] = value;
      });
    }

    return Column(
      children: [
        // مكون الفلترة والتصدير
        UnifiedFilterExportWidget(
          title: item.title,
          chartKey: chartKey,
          startDate: startDate,
          endDate: endDate,
          filterType: filterType,
          chartType: ChartType.sankey,
          advancedFilterOptions: advancedFilterOptions,
          onFilterChanged: (start, end, type, chartKey) {
            _updateChartFilter(
                chartKey, start, end, type, advancedFilterOptions);
          },
          onChartTypeChanged: (type, chartKey) {
            _updateChartType(item.id, type);
          },
          onExport: (format, title) {
            _exportChart(item.id, title);
          },
          onCustomizeChart: () {
            _showChartCustomizationDialog(
              context: context,
              chartType: ChartType.sankey,
              currentColors: departmentColors,
              currentDisplayOptions: displayOptions,
              onColorsChanged: (colors) {
                _saveChartColors(item.id, colors);
                setState(() {});
              },
              onDisplayOptionsChanged: (options) {
                _saveChartDisplayOptions(item.id, options);
                setState(() {});
              },
            );
          },
        ),

        // مخطط سانكي
        Expanded(
          child: _buildSafeChart(
            item.id,
            () {
              // الحصول على بيانات تدفق البيانات من قاعدة البيانات
              final flowData = _getChartData(
                  chartKey, startDate, endDate, advancedFilterOptions);
              debugPrint('بيانات مخطط سانكي: ${flowData.length} عنصر');

              // تحويل البيانات إلى روابط سانكي
              final links = <SankeyLink>[];

              // التحقق من وجود بيانات
              if (flowData.isEmpty || !flowData.containsKey('links')) {
                // إذا لم تكن هناك بيانات، نحاول إنشاء بيانات من المهام
                final tasks = _taskController.tasks.where((task) {
                  // تصفية المهام حسب التاريخ
                  final taskDate = task.createdAtDateTime;
                  if (startDate != null && taskDate.isBefore(startDate)) {
                    return false;
                  }
                  if (endDate != null && taskDate.isAfter(endDate)) {
                    return false;
                  }
                  return true;
                }).toList();

                if (tasks.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.account_tree,
                          size: 48,
                          color: Colors.grey,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'لا توجد بيانات للعرض',
                          style: TextStyle(
                            fontSize: 16,
                            color: Theme.of(context).textTheme.bodyLarge?.color,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                // تحليل تدفق المهام بين الأقسام والحالات
                final Map<String, Map<String, int>> departmentToStatus = {};

                for (final task in tasks) {
                  try {
                    final departmentId = task.departmentId;
                    final department =
                        _departmentController.departments.firstWhereOrNull(
                      (dept) => dept.id == departmentId,
                    );

                    final departmentName = department?.name ?? 'غير محدد';
                    final status = task.status.toString().split('.').last;
                    final statusName = _getStatusName(status);

                    if (!departmentToStatus.containsKey(departmentName)) {
                      departmentToStatus[departmentName] = {};
                    }

                    departmentToStatus[departmentName]![statusName] =
                        (departmentToStatus[departmentName]![statusName] ?? 0) +
                            1;
                  } catch (e) {
                    debugPrint('خطأ في معالجة المهمة لمخطط سانكي: $e');
                  }
                }

                // تحويل البيانات إلى روابط سانكي
                departmentToStatus.forEach((department, statusMap) {
                  statusMap.forEach((status, count) {
                    links.add(SankeyLink(
                      source: department,
                      target: status,
                      value: count.toDouble(),
                    ));
                  });
                });
              } else {
                // استخراج الروابط من البيانات
                if (flowData.containsKey('links')) {
                  final linksData = flowData['links'] as List<dynamic>;
                  for (final link in linksData) {
                    links.add(SankeyLink(
                      source: link['source'] as String,
                      target: link['target'] as String,
                      value: (link['value'] as num).toDouble(),
                    ));
                  }
                }
              }

              // التحقق من وجود روابط بعد المعالجة
              if (links.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.account_tree,
                        size: 48,
                        color: Colors.grey,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد بيانات للعرض',
                        style: TextStyle(
                          fontSize: 16,
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                        ),
                      ),
                    ],
                  ),
                );
              }

              // تحديد ألوان العقد
              final Map<String, Color> nodeColors = {};

              // استخراج أسماء العقد من الروابط
              final Set<String> nodeNames = {};
              for (final link in links) {
                nodeNames.add(link.source);
                nodeNames.add(link.target);
              }

              // تعيين ألوان للعقد
              for (final nodeName in nodeNames) {
                nodeColors[nodeName] = departmentColors[nodeName] ??
                    Colors.primaries[nodeNames.toList().indexOf(nodeName) %
                        Colors.primaries.length];
              }

              return EnhancedSankeyChart(
                links: links,
                title: '',
                chartType: ChartType.sankey,
                advancedFilterOptions: advancedFilterOptions,
                nodeColors: nodeColors,
                showLinkValues: displayOptions['showLinkValues'] ?? false,
                showNodeNames: displayOptions['showNodeNames'] ?? true,
              );
            },
          ),
        ),
      ],
    );
  }

  /// بناء بيانات الإنتاجية اليومية
  Map<String, dynamic> _buildDailyProductivityData(List<dynamic> tasks, DateTime start, DateTime end) {
    // إنشاء قائمة بالأيام في النطاق
    final Map<String, int> dailyCompletedTasks = {};

    // تهيئة جميع الأيام بصفر
    for (int i = 0; i <= end.difference(start).inDays; i++) {
      final date = start.add(Duration(days: i));
      final dateKey = DateFormat('yyyy-MM-dd').format(date);
      dailyCompletedTasks[dateKey] = 0;
    }

    // تجميع المهام المكتملة حسب اليوم
    for (final task in tasks) {
      if (task.status.toString().toLowerCase() == 'completed' && task.completedAt != null) {
        final completedDate = DateTime.fromMillisecondsSinceEpoch(task.completedAt * 1000);
        final dateKey = DateFormat('yyyy-MM-dd').format(completedDate);

        if (dailyCompletedTasks.containsKey(dateKey)) {
          dailyCompletedTasks[dateKey] = (dailyCompletedTasks[dateKey] ?? 0) + 1;
        }
      }
    }

    final labels = dailyCompletedTasks.keys.map((date) => DateFormat('MM/dd').format(DateTime.parse(date))).toList();
    final values = dailyCompletedTasks.values.toList();

    return {
      'labels': labels,
      'values': values,
    };
  }

  /// بناء بيانات معدل إكمال المهام
  Map<String, dynamic> _buildCompletionRateData(List<dynamic> tasks) {
    if (tasks.isEmpty) {
      return {
        'value': 0.0,
        'total': 0,
        'completed': 0,
      };
    }

    final completedTasks = tasks.where((task) =>
      task.status.toString().toLowerCase() == 'completed').length;
    final totalTasks = tasks.length;
    final completionRate = (completedTasks / totalTasks) * 100;

    return {
      'value': completionRate,
      'total': totalTasks,
      'completed': completedTasks,
    };
  }

  /// بناء بيانات توزيع المهام حسب المستخدم
  Map<String, dynamic> _buildUserTasksData(List<dynamic> tasks) {
    final Map<String, int> userTaskCounts = {};

    for (final task in tasks) {
      final assigneeId = task.assigneeId;
      if (assigneeId != null) {
        final user = _userController.users.firstWhereOrNull(
          (user) => user.id == assigneeId,
        );
        final userName = user?.name ?? 'مستخدم غير معروف';
        userTaskCounts[userName] = (userTaskCounts[userName] ?? 0) + 1;
      } else {
        userTaskCounts['غير مُعيَّن'] = (userTaskCounts['غير مُعيَّن'] ?? 0) + 1;
      }
    }

    // إذا لم تكن هناك مهام، أضف بيانات افتراضية
    if (userTaskCounts.isEmpty) {
      userTaskCounts['لا توجد مهام'] = 0;
    }

    final labels = userTaskCounts.keys.toList();
    final values = userTaskCounts.values.toList();

    return {
      'labels': labels,
      'values': values,
    };
  }

  /// بناء بيانات توزيع المهام حسب منشئ المهام
  Map<String, dynamic> _buildTasksByCreatorData(List<dynamic> tasks) {
    final Map<String, int> creatorTaskCounts = {};

    for (final task in tasks) {
      final creatorId = task.creatorId;
      if (creatorId != null) {
        final user = _userController.users.firstWhereOrNull(
          (user) => user.id == creatorId,
        );
        final creatorName = user?.name ?? 'مستخدم غير معروف';
        creatorTaskCounts[creatorName] = (creatorTaskCounts[creatorName] ?? 0) + 1;
      } else {
        creatorTaskCounts['غير محدد'] = (creatorTaskCounts['غير محدد'] ?? 0) + 1;
      }
    }

    // إذا لم تكن هناك مهام، أضف بيانات افتراضية
    if (creatorTaskCounts.isEmpty) {
      creatorTaskCounts['لا توجد مهام'] = 0;
    }

    // ترتيب المنشئين حسب عدد المهام (تنازلي)
    final sortedEntries = creatorTaskCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    // أخذ أفضل 10 منشئين فقط لتجنب الازدحام
    final topCreators = sortedEntries.take(10).toList();

    final labels = topCreators.map((e) => e.key).toList();
    final values = topCreators.map((e) => e.value).toList();

    debugPrint('📊 Dashboard _buildTasksByCreatorData: ${labels.length} منشئ');
    for (int i = 0; i < labels.length; i++) {
      debugPrint('   ${labels[i]}: ${values[i]} مهمة');
    }

    return {
      'labels': labels,
      'values': values,
      'totalCreators': creatorTaskCounts.length,
      'totalTasks': tasks.length,
    };
  }

  /// بناء بيانات مقارنة المنشئين والمُعيَّنين
  Map<String, dynamic> _buildCreatorsVsAssigneesData(List<dynamic> tasks) {
    final Map<String, Map<String, int>> comparisonData = {};

    // تجميع البيانات حسب المستخدم
    for (final task in tasks) {
      final creatorId = task.creatorId;
      final assigneeId = task.assigneeId;

      // معالجة المنشئ
      if (creatorId != null) {
        final creator = _userController.users.firstWhereOrNull(
          (user) => user.id == creatorId,
        );
        final creatorName = creator?.name ?? 'مستخدم غير معروف';

        if (!comparisonData.containsKey(creatorName)) {
          comparisonData[creatorName] = {'created': 0, 'assigned': 0};
        }
        comparisonData[creatorName]!['created'] =
          (comparisonData[creatorName]!['created'] ?? 0) + 1;
      }

      // معالجة المُعيَّن
      if (assigneeId != null) {
        final assignee = _userController.users.firstWhereOrNull(
          (user) => user.id == assigneeId,
        );
        final assigneeName = assignee?.name ?? 'مستخدم غير معروف';

        if (!comparisonData.containsKey(assigneeName)) {
          comparisonData[assigneeName] = {'created': 0, 'assigned': 0};
        }
        comparisonData[assigneeName]!['assigned'] =
          (comparisonData[assigneeName]!['assigned'] ?? 0) + 1;
      }
    }

    // إذا لم تكن هناك بيانات، أضف بيانات افتراضية
    if (comparisonData.isEmpty) {
      comparisonData['لا توجد بيانات'] = {'created': 0, 'assigned': 0};
    }

    // تحويل البيانات إلى تنسيق مناسب للمخطط الشريطي المجمع
    final labels = comparisonData.keys.toList();
    final createdValues = labels.map((name) => comparisonData[name]!['created'] ?? 0).toList();
    final assignedValues = labels.map((name) => comparisonData[name]!['assigned'] ?? 0).toList();

    debugPrint('📊 Dashboard _buildCreatorsVsAssigneesData: ${labels.length} مستخدم');
    for (int i = 0; i < labels.length; i++) {
      debugPrint('   ${labels[i]}: أنشأ ${createdValues[i]}, عُيِّن له ${assignedValues[i]}');
    }

    return {
      'labels': labels,
      'series': [
        {
          'name': 'المهام المُنشأة',
          'values': createdValues,
          'color': Colors.blue,
        },
        {
          'name': 'المهام المُعيَّنة',
          'values': assignedValues,
          'color': Colors.green,
        },
      ],
    };
  }

  /// بناء بيانات أكثر المستخدمين نشاطاً
  Map<String, dynamic> _buildMostActiveUsersData(List<dynamic> tasks) {
    final Map<String, Map<String, int>> userActivity = {};

    // تجميع النشاط حسب المستخدم
    for (final task in tasks) {
      final creatorId = task.creatorId;
      final assigneeId = task.assigneeId;
      final isCompleted = task.status.toString().toLowerCase() == 'completed';

      // نشاط المنشئ
      if (creatorId != null) {
        final creator = _userController.users.firstWhereOrNull(
          (user) => user.id == creatorId,
        );
        final creatorName = creator?.name ?? 'مستخدم غير معروف';

        if (!userActivity.containsKey(creatorName)) {
          userActivity[creatorName] = {
            'created': 0,
            'assigned': 0,
            'completed': 0,
            'total': 0,
          };
        }
        userActivity[creatorName]!['created'] =
          (userActivity[creatorName]!['created'] ?? 0) + 1;
        userActivity[creatorName]!['total'] =
          (userActivity[creatorName]!['total'] ?? 0) + 1;
      }

      // نشاط المُعيَّن
      if (assigneeId != null) {
        final assignee = _userController.users.firstWhereOrNull(
          (user) => user.id == assigneeId,
        );
        final assigneeName = assignee?.name ?? 'مستخدم غير معروف';

        if (!userActivity.containsKey(assigneeName)) {
          userActivity[assigneeName] = {
            'created': 0,
            'assigned': 0,
            'completed': 0,
            'total': 0,
          };
        }
        userActivity[assigneeName]!['assigned'] =
          (userActivity[assigneeName]!['assigned'] ?? 0) + 1;
        userActivity[assigneeName]!['total'] =
          (userActivity[assigneeName]!['total'] ?? 0) + 1;

        // إذا كانت المهمة مكتملة
        if (isCompleted) {
          userActivity[assigneeName]!['completed'] =
            (userActivity[assigneeName]!['completed'] ?? 0) + 1;
        }
      }
    }

    // إذا لم تكن هناك بيانات، أضف بيانات افتراضية
    if (userActivity.isEmpty) {
      userActivity['لا توجد بيانات'] = {
        'created': 0,
        'assigned': 0,
        'completed': 0,
        'total': 0,
      };
    }

    // ترتيب المستخدمين حسب إجمالي النشاط
    final sortedUsers = userActivity.entries.toList()
      ..sort((a, b) => (b.value['total'] ?? 0).compareTo(a.value['total'] ?? 0));

    // أخذ أفضل 8 مستخدمين للمخطط الراداري
    final topUsers = sortedUsers.take(8).toList();

    // تحويل البيانات إلى تنسيق مناسب للمخطط الراداري
    final labels = topUsers.map((e) => e.key).toList();
    final createdData = topUsers.map((e) => (e.value['created'] ?? 0).toDouble()).toList();
    final assignedData = topUsers.map((e) => (e.value['assigned'] ?? 0).toDouble()).toList();
    final completedData = topUsers.map((e) => (e.value['completed'] ?? 0).toDouble()).toList();

    debugPrint('📊 Dashboard _buildMostActiveUsersData: ${labels.length} مستخدم نشط');
    debugPrint('📊 5555555555====> _buildMostActiveUsersData: ${labels} مستخدم نشط');
    for (int i = 0; i < labels.length; i++) {
      debugPrint('   ${labels[i]}: أنشأ ${createdData[i]}, عُيِّن ${assignedData[i]}, أكمل ${completedData[i]}');
    }

    return {
      'labels': labels,
      'datasets': [
        {
          'label': 'المهام المُنشأة',
          'data': createdData,
          'color': Colors.blue,
        },
        {
          'label': 'المهام المُعيَّنة',
          'data': assignedData,
          'color': Colors.green,
        },
        {
          'label': 'المهام المكتملة',
          'data': completedData,
          'color': Colors.orange,
        },
      ],
    };
  }

  /// بناء بيانات المهام المتأخرة
  Map<String, dynamic> _buildOverdueTasksData(List<dynamic> tasks) {
    final now = DateTime.now();
    final Map<String, int> overdueData = {
      'متأخرة': 0,
      'في الموعد': 0,
      'مكتملة في الوقت': 0,
      'مكتملة متأخرة': 0,
    };

    for (final task in tasks) {
      final dueDate = task.dueDate != null
        ? DateTime.fromMillisecondsSinceEpoch(task.dueDate * 1000)
        : null;
      final isCompleted = task.status.toString().toLowerCase() == 'completed';
      final completedAt = task.completedAt != null
        ? DateTime.fromMillisecondsSinceEpoch(task.completedAt * 1000)
        : null;

      if (dueDate != null) {
        if (isCompleted) {
          if (completedAt != null && completedAt.isAfter(dueDate)) {
            overdueData['مكتملة متأخرة'] = (overdueData['مكتملة متأخرة'] ?? 0) + 1;
          } else {
            overdueData['مكتملة في الوقت'] = (overdueData['مكتملة في الوقت'] ?? 0) + 1;
          }
        } else {
          if (now.isAfter(dueDate)) {
            overdueData['متأخرة'] = (overdueData['متأخرة'] ?? 0) + 1;
          } else {
            overdueData['في الموعد'] = (overdueData['في الموعد'] ?? 0) + 1;
          }
        }
      }
    }

    final labels = overdueData.keys.toList();
    final values = overdueData.values.toList();

    return {
      'labels': labels,
      'values': values,
    };
  }

  /// بناء بيانات مقارنة الوقت المقدر مقابل الفعلي
  Map<String, dynamic> _buildTimeComparisonData(List<dynamic> tasks) {
    final List<Map<String, dynamic>> scatterData = [];

    for (final task in tasks) {
      final estimatedTime = task.estimatedTime ?? 0;
      final actualTime = task.actualTime ?? 0;

      if (estimatedTime > 0 && actualTime > 0) {
        scatterData.add({
          'x': estimatedTime.toDouble(),
          'y': actualTime.toDouble(),
          'name': task.title,
          'status': task.status.toString(),
        });
      }
    }

    return {
      'scatterData': scatterData,
    };
  }

  /// بناء بيانات النشاط الأسبوعي للمستخدمين
  Map<String, dynamic> _buildWeeklyActivityData(List<dynamic> tasks, DateTime start, DateTime end) {
    // إنشاء مصفوفة للأسابيع والمستخدمين
    final Map<String, Map<int, int>> userWeeklyActivity = {};

    // الحصول على قائمة المستخدمين
    final users = _userController.users;
    for (final user in users) {
      userWeeklyActivity[user.name] = {};
    }

    // تجميع النشاط حسب الأسبوع والمستخدم
    for (final task in tasks) {
      final taskDate = task.createdAtDateTime;
      final weekNumber = ((taskDate.difference(start).inDays) / 7).floor();

      final assigneeId = task.assigneeId;
      if (assigneeId != null) {
        final user = users.firstWhereOrNull((u) => u.id == assigneeId);
        if (user != null) {
          userWeeklyActivity[user.name]![weekNumber] =
            (userWeeklyActivity[user.name]![weekNumber] ?? 0) + 1;
        }
      }
    }

    return {
      'userWeeklyActivity': userWeeklyActivity,
      'startDate': start,
      'endDate': end,
    };
  }

  /// بناء بيانات تقدم المشاريع
  Map<String, dynamic> _buildProjectProgressData(List<dynamic> tasks) {
    // تجميع المهام حسب القسم (كمشاريع)
    final Map<String, Map<String, int>> projectProgress = {};

    for (final task in tasks) {
      final departmentId = task.departmentId;
      final department = _departmentController.departments.firstWhereOrNull(
        (dept) => dept.id == departmentId,
      );
      final projectName = department?.name ?? 'مشروع غير محدد';

      if (!projectProgress.containsKey(projectName)) {
        projectProgress[projectName] = {
          'total': 0,
          'completed': 0,
          'inProgress': 0,
          'pending': 0,
        };
      }

      projectProgress[projectName]!['total'] =
        (projectProgress[projectName]!['total'] ?? 0) + 1;

      final status = task.status.toString().toLowerCase();
      if (status == 'completed') {
        projectProgress[projectName]!['completed'] =
          (projectProgress[projectName]!['completed'] ?? 0) + 1;
      } else if (status == 'in_progress') {
        projectProgress[projectName]!['inProgress'] =
          (projectProgress[projectName]!['inProgress'] ?? 0) + 1;
      } else {
        projectProgress[projectName]!['pending'] =
          (projectProgress[projectName]!['pending'] ?? 0) + 1;
      }
    }

    // تحويل إلى قائمة مهام جانت
    final List<GanttTask> ganttTasks = [];
    int index = 0;

    for (final entry in projectProgress.entries) {
      final projectName = entry.key;
      final data = entry.value;
      final total = data['total'] ?? 0;
      final completed = data['completed'] ?? 0;
      final completionPercentage = total > 0 ? (completed / total) * 100 : 0;

      ganttTasks.add(GanttTask(
        id: 'project_$index',
        title: projectName,
        startDate: DateTime.now().subtract(Duration(days: 30)),
        endDate: DateTime.now().add(Duration(days: 30)),
        completionPercentage: completionPercentage.toDouble(),
        color: Colors.primaries[index % Colors.primaries.length],
      ));
      index++;
    }

    return {
      'ganttTasks': ganttTasks,
      'projectProgress': projectProgress,
    };
  }

  /// بناء مخطط المقياس (Gauge Chart)
  Widget _buildGaugeChart(DashboardItem item, String chartKey, DateTime? startDate,
      DateTime? endDate, TimeFilterType filterType, AdvancedFilterOptions filterOptions) {
    try {
      final data = _getChartData(chartKey, startDate, endDate, filterOptions);
      final value = data['value'] ?? 0.0;
      final total = data['total'] ?? 0;
      final completed = data['completed'] ?? 0;

      return Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Expanded(
              child: Center(
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    SizedBox(
                      width: 150,
                      height: 150,
                      child: CircularProgressIndicator(
                        value: value / 100,
                        strokeWidth: 12,
                        backgroundColor: Colors.grey.shade300,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          value >= 80 ? Colors.green :
                          value >= 60 ? Colors.orange :
                          value >= 40 ? Colors.amber : Colors.red,
                        ),
                      ),
                    ),
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          '${value.toStringAsFixed(1)}%',
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'معدل الإكمال',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Column(
                  children: [
                    Text(
                      '$completed',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    const Text('مكتملة', style: TextStyle(fontSize: 12)),
                  ],
                ),
                Column(
                  children: [
                    Text(
                      '$total',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                    const Text('إجمالي', style: TextStyle(fontSize: 12)),
                  ],
                ),
              ],
            ),
          ],
        ),
      );
    } catch (e) {
      return _buildErrorContent(item, e.toString());
    }
  }

  /// بناء مخطط التشتت (Scatter Chart)
  Widget _buildScatterChart(DashboardItem item, String chartKey, DateTime? startDate,
      DateTime? endDate, TimeFilterType filterType, AdvancedFilterOptions filterOptions) {
    try {
      final data = _getChartData(chartKey, startDate, endDate, filterOptions);
      final scatterData = data['scatterData'] as List<Map<String, dynamic>>? ?? [];

      if (scatterData.isEmpty) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.scatter_plot,
                  size: 48,
                  color: Colors.grey,
                ),
                SizedBox(height: 16),
                Text(
                  'لا توجد بيانات للمقارنة',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        );
      }

      return Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const Text(
              'الوقت المقدر مقابل الفعلي (بالساعات)',
              style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: CustomPaint(
                painter: ScatterChartPainter(scatterData),
                child: Container(),
              ),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Row(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: const BoxDecoration(
                        color: Colors.blue,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 4),
                    const Text('مكتملة', style: TextStyle(fontSize: 10)),
                  ],
                ),
                Row(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: const BoxDecoration(
                        color: Colors.orange,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 4),
                    const Text('قيد التنفيذ', style: TextStyle(fontSize: 10)),
                  ],
                ),
              ],
            ),
          ],
        ),
      );
    } catch (e) {
      return _buildErrorContent(item, e.toString());
    }
  }
}

/// رسام مخطط التشتت
class ScatterChartPainter extends CustomPainter {
  final List<Map<String, dynamic>> data;

  ScatterChartPainter(this.data);

  @override
  void paint(Canvas canvas, Size size) {
    if (data.isEmpty) return;

    final paint = Paint()
      ..style = PaintingStyle.fill;

    // العثور على القيم القصوى والدنيا
    double maxX = 0, maxY = 0;
    for (final point in data) {
      final x = point['x'] as double;
      final y = point['y'] as double;
      if (x > maxX) maxX = x;
      if (y > maxY) maxY = y;
    }

    // رسم المحاور
    final axisPaint = Paint()
      ..color = Colors.grey
      ..strokeWidth = 1;

    // المحور السيني
    canvas.drawLine(
      Offset(40, size.height - 40),
      Offset(size.width - 20, size.height - 40),
      axisPaint,
    );

    // المحور الصادي
    canvas.drawLine(
      Offset(40, 20),
      Offset(40, size.height - 40),
      axisPaint,
    );

    // رسم النقاط
    for (final point in data) {
      final x = point['x'] as double;
      final y = point['y'] as double;
      final status = point['status'] as String;

      // تحويل القيم إلى إحداثيات الشاشة
      final screenX = 40 + (x / maxX) * (size.width - 60);
      final screenY = size.height - 40 - (y / maxY) * (size.height - 60);

      // اختيار اللون حسب الحالة
      paint.color = status.toLowerCase() == 'completed'
        ? Colors.green
        : Colors.orange;

      canvas.drawCircle(Offset(screenX, screenY), 4, paint);
    }

    // رسم خط المرجع (الوقت المقدر = الوقت الفعلي)
    final referencePaint = Paint()
      ..color = Colors.red.withValues(alpha: 0.5)
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    canvas.drawLine(
      Offset(40, size.height - 40),
      Offset(size.width - 20, 20),
      referencePaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
