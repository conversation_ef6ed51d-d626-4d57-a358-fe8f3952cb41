import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../controllers/admin_controller.dart';
import '../../../services/unified_permission_service.dart';
import '../shared/admin_card_widget.dart';
import '../shared/admin_dialog_widget.dart';

/// شاشة إعدادات النظام
class SystemSettingsScreen extends StatefulWidget {
  const SystemSettingsScreen({super.key});

  @override
  State<SystemSettingsScreen> createState() => _SystemSettingsScreenState();
}

class _SystemSettingsScreenState extends State<SystemSettingsScreen> {
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();
  final AdminController _adminController = Get.find<AdminController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات النظام'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // قسم الأمان
            _buildSecuritySection(),
            
            const SizedBox(height: 24),
            
            // قسم النسخ الاحتياطية
            _buildBackupSection(),
            
            const SizedBox(height: 24),
            
            // قسم قاعدة البيانات
            _buildDatabaseSection(),
            
            const SizedBox(height: 24),
            
            // قسم التقارير
            _buildReportsSection(),
            
            const SizedBox(height: 24),
            
            // قسم النظام العام
            _buildGeneralSection(),
          ],
        ),
      ),
    );
  }

  /// بناء قسم الأمان
  Widget _buildSecuritySection() {
    return _buildSection(
      title: 'الأمان والحماية',
      subtitle: 'إعدادات الأمان وحماية النظام',
      icon: Icons.security,
      children: [
        AdminCardWidget(
          title: 'إعدادات كلمات المرور',
          subtitle: 'تحديد متطلبات كلمات المرور وسياسات الأمان',
          icon: Icons.lock,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _showPasswordSettings(),
        ),
        AdminCardWidget(
          title: 'جلسات المستخدمين',
          subtitle: 'إدارة الجلسات النشطة ومدة انتهاء الصلاحية',
          icon: Icons.access_time,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _showSessionSettings(),
        ),
        AdminCardWidget(
          title: 'سجل النشاط',
          subtitle: 'عرض وإدارة سجل أنشطة المستخدمين',
          icon: Icons.history,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _showActivityLogs(),
        ),
      ],
    );
  }

  /// بناء قسم النسخ الاحتياطية
  Widget _buildBackupSection() {
    return _buildSection(
      title: 'النسخ الاحتياطية',
      subtitle: 'إنشاء واستعادة النسخ الاحتياطية',
      icon: Icons.backup,
      children: [
        AdminCardWidget(
          title: 'إنشاء نسخة احتياطية',
          subtitle: 'إنشاء نسخة احتياطية من قاعدة البيانات',
          icon: Icons.save,
          iconColor: Colors.green,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _createBackup(),
        ),
        AdminCardWidget(
          title: 'استعادة نسخة احتياطية',
          subtitle: 'استعادة البيانات من نسخة احتياطية سابقة',
          icon: Icons.restore,
          iconColor: Colors.orange,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _restoreBackup(),
        ),
        AdminCardWidget(
          title: 'إدارة النسخ الاحتياطية',
          subtitle: 'عرض وحذف النسخ الاحتياطية المحفوظة',
          icon: Icons.folder,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _manageBackups(),
        ),
      ],
    );
  }

  /// بناء قسم قاعدة البيانات
  Widget _buildDatabaseSection() {
    return _buildSection(
      title: 'قاعدة البيانات',
      subtitle: 'إدارة وصيانة قاعدة البيانات',
      icon: Icons.storage,
      children: [
        AdminCardWidget(
          title: 'معلومات قاعدة البيانات',
          subtitle: 'عرض معلومات وإحصائيات قاعدة البيانات',
          icon: Icons.info,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _showDatabaseInfo(),
        ),
        AdminCardWidget(
          title: 'تحسين قاعدة البيانات',
          subtitle: 'تشغيل عمليات التحسين والصيانة',
          icon: Icons.tune,
          iconColor: Colors.blue,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _optimizeDatabase(),
        ),
        AdminCardWidget(
          title: 'إصلاح قاعدة البيانات',
          subtitle: 'إصلاح المشاكل والأخطاء في قاعدة البيانات',
          icon: Icons.build,
          iconColor: Colors.red,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _repairDatabase(),
        ),
      ],
    );
  }

  /// بناء قسم التقارير
  Widget _buildReportsSection() {
    return _buildSection(
      title: 'التقارير والإحصائيات',
      subtitle: 'تقارير النظام والاستخدام',
      icon: Icons.analytics,
      children: [
        AdminCardWidget(
          title: 'تقرير استخدام النظام',
          subtitle: 'إحصائيات استخدام النظام والمستخدمين',
          icon: Icons.bar_chart,
          isEnabled: _permissionService.canViewReports(),
          onTap: () => _showSystemUsageReport(),
        ),
        AdminCardWidget(
          title: 'تقرير الأخطاء',
          subtitle: 'عرض الأخطاء والمشاكل المسجلة',
          icon: Icons.error_outline,
          iconColor: Colors.red,
          isEnabled: _permissionService.canViewReports(),
          onTap: () => _showErrorReport(),
        ),
        AdminCardWidget(
          title: 'تقرير الأداء',
          subtitle: 'مراقبة أداء النظام والخادم',
          icon: Icons.speed,
          iconColor: Colors.green,
          isEnabled: _permissionService.canViewReports(),
          onTap: () => _showPerformanceReport(),
        ),
      ],
    );
  }

  /// بناء قسم النظام العام
  Widget _buildGeneralSection() {
    return _buildSection(
      title: 'الإعدادات العامة',
      subtitle: 'إعدادات النظام العامة',
      icon: Icons.settings,
      children: [
        AdminCardWidget(
          title: 'إعدادات التطبيق',
          subtitle: 'إعدادات عامة للتطبيق والواجهة',
          icon: Icons.app_settings_alt,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _showAppSettings(),
        ),
        AdminCardWidget(
          title: 'إعدادات الإشعارات',
          subtitle: 'تكوين إعدادات الإشعارات والتنبيهات',
          icon: Icons.notifications_active,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _showNotificationSettings(),
        ),
        AdminCardWidget(
          title: 'إعادة تشغيل النظام',
          subtitle: 'إعادة تشغيل الخدمات والتطبيق',
          icon: Icons.restart_alt,
          iconColor: Colors.orange,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _restartSystem(),
        ),
      ],
    );
  }

  /// بناء قسم
  Widget _buildSection({
    required String title,
    required String subtitle,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // رأس القسم
        Row(
          children: [
            Icon(icon, color: Theme.of(context).primaryColor),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        // محتوى القسم
        ...children,
      ],
    );
  }

  /// عرض إعدادات كلمات المرور
  void _showPasswordSettings() {
    Get.snackbar('قريباً', 'ستتوفر هذه الميزة قريباً');
  }

  /// عرض إعدادات الجلسات
  void _showSessionSettings() {
    Get.snackbar('قريباً', 'ستتوفر هذه الميزة قريباً');
  }

  /// عرض سجل النشاط
  void _showActivityLogs() {
    Get.snackbar('قريباً', 'ستتوفر هذه الميزة قريباً');
  }

  /// إنشاء نسخة احتياطية
  Future<void> _createBackup() async {
    final confirmed = await AdminConfirmDialog.show(
      title: 'إنشاء نسخة احتياطية',
      message: 'هل تريد إنشاء نسخة احتياطية من قاعدة البيانات؟',
      confirmText: 'إنشاء',
      icon: Icons.backup,
      confirmColor: Colors.green,
    );

    if (confirmed) {
      AdminLoadingDialog.show(message: 'جاري إنشاء النسخة الاحتياطية...');
      
      try {
        // استدعاء API لإنشاء النسخة الاحتياطية
        await _adminController.createBackup('نسخة احتياطية من إعدادات النظام');
        await _adminController.loadBackups(); // إعادة تحميل النسخ الاحتياطية
        
        AdminLoadingDialog.hide();
        AdminMessageDialog.showSuccess(
          title: 'نجح العملية',
          message: 'تم إنشاء النسخة الاحتياطية بنجاح',
        );
      } catch (e) {
        AdminLoadingDialog.hide();
        AdminMessageDialog.showError(
          title: 'خطأ',
          message: 'فشل في إنشاء النسخة الاحتياطية: $e',
        );
      }
    }
  }

  /// استعادة نسخة احتياطية
  void _restoreBackup() {
    Get.snackbar('قريباً', 'ستتوفر هذه الميزة قريباً');
  }

  /// إدارة النسخ الاحتياطية
  void _manageBackups() {
    Get.snackbar('قريباً', 'ستتوفر هذه الميزة قريباً');
  }

  /// عرض معلومات قاعدة البيانات
  void _showDatabaseInfo() {
    Get.snackbar('قريباً', 'ستتوفر هذه الميزة قريباً');
  }

  /// تحسين قاعدة البيانات
  Future<void> _optimizeDatabase() async {
    final confirmed = await AdminConfirmDialog.show(
      title: 'تحسين قاعدة البيانات',
      message: 'هل تريد تشغيل عملية تحسين قاعدة البيانات؟\nقد تستغرق هذه العملية بعض الوقت.',
      confirmText: 'تحسين',
      icon: Icons.tune,
      confirmColor: Colors.blue,
    );

    if (confirmed) {
      AdminLoadingDialog.show(message: 'جاري تحسين قاعدة البيانات...');
      
      try {
        // استدعاء API لتحسين قاعدة البيانات
        await _adminController.optimizeDatabase();
        await _adminController.refreshAllData(); // تحديث البيانات
        
        AdminLoadingDialog.hide();
        AdminMessageDialog.showSuccess(
          title: 'نجح العملية',
          message: 'تم تحسين قاعدة البيانات بنجاح',
        );
      } catch (e) {
        AdminLoadingDialog.hide();
        AdminMessageDialog.showError(
          title: 'خطأ',
          message: 'فشل في تحسين قاعدة البيانات: $e',
        );
      }
    }
  }

  /// إصلاح قاعدة البيانات
  Future<void> _repairDatabase() async {
    final confirmed = await AdminConfirmDialog.show(
      title: 'إصلاح قاعدة البيانات',
      message: 'هل تريد تشغيل عملية إصلاح قاعدة البيانات؟\nتحذير: قد تؤثر هذه العملية على البيانات.',
      confirmText: 'إصلاح',
      icon: Icons.build,
      confirmColor: Colors.red,
    );

    if (confirmed) {
      AdminLoadingDialog.show(message: 'جاري إصلاح قاعدة البيانات...');
      
      try {
        // استدعاء API لإصلاح قاعدة البيانات
        await _adminController.repairDatabase();
        await _adminController.refreshAllData(); // تحديث البيانات
        
        AdminLoadingDialog.hide();
        AdminMessageDialog.showSuccess(
          title: 'نجح العملية',
          message: 'تم إصلاح قاعدة البيانات بنجاح',
        );
      } catch (e) {
        AdminLoadingDialog.hide();
        AdminMessageDialog.showError(
          title: 'خطأ',
          message: 'فشل في إصلاح قاعدة البيانات: $e',
        );
      }
    }
  }

  /// عرض تقرير استخدام النظام
  void _showSystemUsageReport() {
    Get.snackbar('قريباً', 'ستتوفر هذه الميزة قريباً');
  }

  /// عرض تقرير الأخطاء
  void _showErrorReport() {
    Get.snackbar('قريباً', 'ستتوفر هذه الميزة قريباً');
  }

  /// عرض تقرير الأداء
  void _showPerformanceReport() {
    Get.snackbar('قريباً', 'ستتوفر هذه الميزة قريباً');
  }

  /// عرض إعدادات التطبيق
  void _showAppSettings() {
    Get.snackbar('قريباً', 'ستتوفر هذه الميزة قريباً');
  }

  /// عرض إعدادات الإشعارات
  void _showNotificationSettings() {
    Get.snackbar('قريباً', 'ستتوفر هذه الميزة قريباً');
  }

  /// إعادة تشغيل النظام
  Future<void> _restartSystem() async {
    final confirmed = await AdminConfirmDialog.show(
      title: 'إعادة تشغيل النظام',
      message: 'هل تريد إعادة تشغيل النظام؟\nسيتم قطع الاتصال مع جميع المستخدمين.',
      confirmText: 'إعادة تشغيل',
      icon: Icons.restart_alt,
      confirmColor: Colors.orange,
    );

    if (confirmed) {
      AdminLoadingDialog.show(message: 'جاري إعادة تشغيل النظام...');
      
      try {
        // استدعاء API لإعادة تشغيل النظام
        await _adminController.restartSystem();
        await _adminController.refreshAllData(); // تحديث البيانات
        
        AdminLoadingDialog.hide();
        AdminMessageDialog.showSuccess(
          title: 'نجح العملية',
          message: 'تم إعادة تشغيل النظام بنجاح',
        );
      } catch (e) {
        AdminLoadingDialog.hide();
        AdminMessageDialog.showError(
          title: 'خطأ',
          message: 'فشل في إعادة تشغيل النظام: $e',
        );
      }
    }
  }
}
