import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../controllers/admin_controller.dart';
import '../../../services/unified_permission_service.dart';
import '../shared/admin_card_widget.dart';
import '../shared/admin_dialog_widget.dart';
import 'backup_management_screen.dart';

/// شاشة إعدادات النظام
class SystemSettingsScreen extends StatefulWidget {
  const SystemSettingsScreen({super.key});

  @override
  State<SystemSettingsScreen> createState() => _SystemSettingsScreenState();
}

class _SystemSettingsScreenState extends State<SystemSettingsScreen> {
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();
  final AdminController _adminController = Get.find<AdminController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات النظام'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // قسم الأمان
            _buildSecuritySection(),
            
            const SizedBox(height: 24),
            
            // قسم النسخ الاحتياطية
            _buildBackupSection(),
            
            const SizedBox(height: 24),
            
            // قسم قاعدة البيانات
            _buildDatabaseSection(),
            
            const SizedBox(height: 24),
            
            // قسم التقارير
            _buildReportsSection(),
            
            const SizedBox(height: 24),
            
            // قسم النظام العام
            _buildGeneralSection(),
          ],
        ),
      ),
    );
  }

  /// بناء قسم الأمان
  Widget _buildSecuritySection() {
    return _buildSection(
      title: 'الأمان والحماية',
      subtitle: 'إعدادات الأمان وحماية النظام',
      icon: Icons.security,
      children: [
        AdminCardWidget(
          title: 'إعدادات كلمات المرور',
          subtitle: 'تحديد متطلبات كلمات المرور وسياسات الأمان',
          icon: Icons.lock,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _showPasswordSettings(),
        ),
        AdminCardWidget(
          title: 'جلسات المستخدمين',
          subtitle: 'إدارة الجلسات النشطة ومدة انتهاء الصلاحية',
          icon: Icons.access_time,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _showSessionSettings(),
        ),
        AdminCardWidget(
          title: 'سجل النشاط',
          subtitle: 'عرض وإدارة سجل أنشطة المستخدمين',
          icon: Icons.history,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _showActivityLogs(),
        ),
      ],
    );
  }

  /// بناء قسم النسخ الاحتياطية
  Widget _buildBackupSection() {
    return _buildSection(
      title: 'النسخ الاحتياطية',
      subtitle: 'إنشاء واستعادة النسخ الاحتياطية',
      icon: Icons.backup,
      children: [
        AdminCardWidget(
          title: 'إنشاء نسخة احتياطية',
          subtitle: 'إنشاء نسخة احتياطية من قاعدة البيانات',
          icon: Icons.save,
          iconColor: Colors.green,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _createBackup(),
        ),
        AdminCardWidget(
          title: 'استعادة نسخة احتياطية',
          subtitle: 'استعادة البيانات من نسخة احتياطية سابقة',
          icon: Icons.restore,
          iconColor: Colors.orange,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _restoreBackup(),
        ),
        AdminCardWidget(
          title: 'إدارة النسخ الاحتياطية',
          subtitle: 'عرض وحذف النسخ الاحتياطية المحفوظة',
          icon: Icons.folder,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _manageBackups(),
        ),
      ],
    );
  }

  /// بناء قسم قاعدة البيانات
  Widget _buildDatabaseSection() {
    return _buildSection(
      title: 'قاعدة البيانات',
      subtitle: 'إدارة وصيانة قاعدة البيانات',
      icon: Icons.storage,
      children: [
        AdminCardWidget(
          title: 'معلومات قاعدة البيانات',
          subtitle: 'عرض معلومات وإحصائيات قاعدة البيانات',
          icon: Icons.info,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _showDatabaseInfo(),
        ),
        AdminCardWidget(
          title: 'تحسين قاعدة البيانات',
          subtitle: 'تشغيل عمليات التحسين والصيانة',
          icon: Icons.tune,
          iconColor: Colors.blue,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _optimizeDatabase(),
        ),
        AdminCardWidget(
          title: 'إصلاح قاعدة البيانات',
          subtitle: 'إصلاح المشاكل والأخطاء في قاعدة البيانات',
          icon: Icons.build,
          iconColor: Colors.red,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _repairDatabase(),
        ),
      ],
    );
  }

  /// بناء قسم التقارير
  Widget _buildReportsSection() {
    return _buildSection(
      title: 'التقارير والإحصائيات',
      subtitle: 'تقارير النظام والاستخدام',
      icon: Icons.analytics,
      children: [
        AdminCardWidget(
          title: 'تقرير استخدام النظام',
          subtitle: 'إحصائيات استخدام النظام والمستخدمين',
          icon: Icons.bar_chart,
          isEnabled: _permissionService.canViewReports(),
          onTap: () => _showSystemUsageReport(),
        ),
        AdminCardWidget(
          title: 'تقرير الأخطاء',
          subtitle: 'عرض الأخطاء والمشاكل المسجلة',
          icon: Icons.error_outline,
          iconColor: Colors.red,
          isEnabled: _permissionService.canViewReports(),
          onTap: () => _showErrorReport(),
        ),
        AdminCardWidget(
          title: 'تقرير الأداء',
          subtitle: 'مراقبة أداء النظام والخادم',
          icon: Icons.speed,
          iconColor: Colors.green,
          isEnabled: _permissionService.canViewReports(),
          onTap: () => _showPerformanceReport(),
        ),
      ],
    );
  }

  /// بناء قسم النظام العام
  Widget _buildGeneralSection() {
    return _buildSection(
      title: 'الإعدادات العامة',
      subtitle: 'إعدادات النظام العامة',
      icon: Icons.settings,
      children: [
        AdminCardWidget(
          title: 'إعدادات التطبيق',
          subtitle: 'إعدادات عامة للتطبيق والواجهة',
          icon: Icons.app_settings_alt,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _showAppSettings(),
        ),
        AdminCardWidget(
          title: 'إعدادات الإشعارات',
          subtitle: 'تكوين إعدادات الإشعارات والتنبيهات',
          icon: Icons.notifications_active,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _showNotificationSettings(),
        ),
        AdminCardWidget(
          title: 'إعادة تشغيل النظام',
          subtitle: 'إعادة تشغيل الخدمات والتطبيق',
          icon: Icons.restart_alt,
          iconColor: Colors.orange,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _restartSystem(),
        ),
      ],
    );
  }

  /// بناء قسم
  Widget _buildSection({
    required String title,
    required String subtitle,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // رأس القسم
        Row(
          children: [
            Icon(icon, color: Theme.of(context).primaryColor),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        // محتوى القسم
        ...children,
      ],
    );
  }

  /// عرض إعدادات كلمات المرور
  void _showPasswordSettings() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.lock, color: Colors.orange),
            SizedBox(width: 8),
            Text('إعدادات كلمات المرور'),
          ],
        ),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // الحد الأدنى لطول كلمة المرور
              _buildPasswordSettingRow(
                'الحد الأدنى لطول كلمة المرور',
                '8 أحرف',
                Icons.straighten,
                () => _editPasswordMinLength(),
              ),
              const Divider(),

              // تعقيد كلمة المرور
              _buildPasswordSettingRow(
                'تعقيد كلمة المرور',
                'أحرف كبيرة وصغيرة ورقم ورمز',
                Icons.security,
                () => _editPasswordComplexity(),
              ),
              const Divider(),

              // انتهاء صلاحية كلمة المرور
              _buildPasswordSettingRow(
                'انتهاء صلاحية كلمة المرور',
                '90 يوم',
                Icons.schedule,
                () => _editPasswordExpiry(),
              ),
              const Divider(),

              // تاريخ كلمات المرور
              _buildPasswordSettingRow(
                'تذكر كلمات المرور السابقة',
                '5 كلمات مرور',
                Icons.history,
                () => _editPasswordHistory(),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// عرض إعدادات الجلسات
  void _showSessionSettings() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.timer, color: Colors.blue),
            SizedBox(width: 8),
            Text('إعدادات الجلسات'),
          ],
        ),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // مهلة انتهاء الجلسة
              _buildSessionSettingRow(
                'مهلة انتهاء الجلسة',
                '30 دقيقة',
                Icons.timer_off,
                () => _editSessionTimeout(),
              ),
              const Divider(),

              // الحد الأقصى للجلسات المتزامنة
              _buildSessionSettingRow(
                'الحد الأقصى للجلسات المتزامنة',
                '3 جلسات',
                Icons.devices,
                () => _editMaxConcurrentSessions(),
              ),
              const Divider(),

              // تذكر تسجيل الدخول
              _buildSessionSettingRow(
                'تذكر تسجيل الدخول',
                'مفعل لمدة 7 أيام',
                Icons.remember_me,
                () => _editRememberLogin(),
              ),
              const Divider(),

              // إنهاء الجلسات النشطة
              _buildSessionSettingRow(
                'إنهاء جميع الجلسات النشطة',
                'إنهاء فوري',
                Icons.logout,
                () => _terminateAllSessions(),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// عرض سجل النشاط
  void _showActivityLogs() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.history, color: Colors.green),
            SizedBox(width: 8),
            Text('سجل النشاط'),
          ],
        ),
        content: SizedBox(
          width: 600,
          height: 400,
          child: Obx(() {
            final logs = _adminController.activityLogs;
            if (logs.isEmpty) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.info_outline, size: 64, color: Colors.grey),
                    SizedBox(height: 16),
                    Text('لا توجد سجلات نشاط'),
                  ],
                ),
              );
            }

            return ListView.builder(
              itemCount: logs.length,
              itemBuilder: (context, index) {
                final log = logs[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: _getLogTypeColor(log.action),
                      child: Icon(
                        _getLogTypeIcon(log.action),
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    title: Text(log.action),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(log.details ?? log.changeDescription ?? 'لا يوجد وصف'),
                        Text(
                          'المستخدم: ${log.userId} | ${_formatDateTime(log.timestamp)}',
                          style: const TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                      ],
                    ),
                    trailing: IconButton(
                      icon: const Icon(Icons.info_outline),
                      onPressed: () => _showLogDetails(log),
                    ),
                  ),
                );
              },
            );
          }),
        ),
        actions: [
          TextButton(
            onPressed: () => _adminController.loadActivityLogs(),
            child: const Text('تحديث'),
          ),
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// إنشاء نسخة احتياطية
  Future<void> _createBackup() async {
    final confirmed = await AdminConfirmDialog.show(
      title: 'إنشاء نسخة احتياطية',
      message: 'هل تريد إنشاء نسخة احتياطية من قاعدة البيانات؟',
      confirmText: 'إنشاء',
      icon: Icons.backup,
      confirmColor: Colors.green,
    );

    if (confirmed) {
      AdminLoadingDialog.show(message: 'جاري إنشاء النسخة الاحتياطية...');
      
      try {
        // استدعاء API لإنشاء النسخة الاحتياطية
        await _adminController.createBackup('نسخة احتياطية من إعدادات النظام');
        await _adminController.loadBackups(); // إعادة تحميل النسخ الاحتياطية
        
        AdminLoadingDialog.hide();
        AdminMessageDialog.showSuccess(
          title: 'نجح العملية',
          message: 'تم إنشاء النسخة الاحتياطية بنجاح',
        );
      } catch (e) {
        AdminLoadingDialog.hide();
        AdminMessageDialog.showError(
          title: 'خطأ',
          message: 'فشل في إنشاء النسخة الاحتياطية: $e',
        );
      }
    }
  }

  /// استعادة نسخة احتياطية
  void _restoreBackup() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.restore, color: Colors.orange),
            SizedBox(width: 8),
            Text('استعادة نسخة احتياطية'),
          ],
        ),
        content: SizedBox(
          width: 500,
          height: 300,
          child: Obx(() {
            final backups = _adminController.backups;
            if (backups.isEmpty) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.backup_outlined, size: 64, color: Colors.grey),
                    SizedBox(height: 16),
                    Text('لا توجد نسخ احتياطية متاحة'),
                  ],
                ),
              );
            }

            return ListView.builder(
              itemCount: backups.length,
              itemBuilder: (context, index) {
                final backup = backups[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: ListTile(
                    leading: const CircleAvatar(
                      backgroundColor: Colors.blue,
                      child: Icon(Icons.backup, color: Colors.white),
                    ),
                    title: Text(backup.description ?? 'نسخة احتياطية'),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('الحجم: ${_formatFileSize(backup.size ?? 0)}'),
                        Text('التاريخ: ${_formatDateTime(backup.createdAt)}'),
                      ],
                    ),
                    trailing: ElevatedButton(
                      onPressed: () => _confirmRestoreBackup(backup),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('استعادة'),
                    ),
                  ),
                );
              },
            );
          }),
        ),
        actions: [
          TextButton(
            onPressed: () => _adminController.loadBackups(),
            child: const Text('تحديث'),
          ),
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// إدارة النسخ الاحتياطية
  void _manageBackups() {
    Get.to(() => const BackupManagementScreen());
  }

  /// عرض معلومات قاعدة البيانات
  void _showDatabaseInfo() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.storage, color: Colors.blue),
            SizedBox(width: 8),
            Text('معلومات قاعدة البيانات'),
          ],
        ),
        content: SizedBox(
          width: 500,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDatabaseInfoRow('نوع قاعدة البيانات', 'SQL Server'),
              _buildDatabaseInfoRow('إصدار الخادم', '2019 Express'),
              _buildDatabaseInfoRow('اسم قاعدة البيانات', 'TaskManagementDB'),
              _buildDatabaseInfoRow('حجم قاعدة البيانات', '125.5 MB'),
              _buildDatabaseInfoRow('عدد الجداول', '25'),
              _buildDatabaseInfoRow('عدد السجلات', '15,432'),
              _buildDatabaseInfoRow('آخر نسخة احتياطية', 'منذ 2 ساعة'),
              _buildDatabaseInfoRow('حالة الاتصال', 'متصل', isStatus: true),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => _testDatabaseConnection(),
            child: const Text('اختبار الاتصال'),
          ),
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// تحسين قاعدة البيانات
  Future<void> _optimizeDatabase() async {
    final confirmed = await AdminConfirmDialog.show(
      title: 'تحسين قاعدة البيانات',
      message: 'هل تريد تشغيل عملية تحسين قاعدة البيانات؟\nقد تستغرق هذه العملية بعض الوقت.',
      confirmText: 'تحسين',
      icon: Icons.tune,
      confirmColor: Colors.blue,
    );

    if (confirmed) {
      AdminLoadingDialog.show(message: 'جاري تحسين قاعدة البيانات...');
      
      try {
        // استدعاء API لتحسين قاعدة البيانات
        await _adminController.optimizeDatabase();
        await _adminController.refreshAllData(); // تحديث البيانات
        
        AdminLoadingDialog.hide();
        AdminMessageDialog.showSuccess(
          title: 'نجح العملية',
          message: 'تم تحسين قاعدة البيانات بنجاح',
        );
      } catch (e) {
        AdminLoadingDialog.hide();
        AdminMessageDialog.showError(
          title: 'خطأ',
          message: 'فشل في تحسين قاعدة البيانات: $e',
        );
      }
    }
  }

  /// إصلاح قاعدة البيانات
  Future<void> _repairDatabase() async {
    final confirmed = await AdminConfirmDialog.show(
      title: 'إصلاح قاعدة البيانات',
      message: 'هل تريد تشغيل عملية إصلاح قاعدة البيانات؟\nتحذير: قد تؤثر هذه العملية على البيانات.',
      confirmText: 'إصلاح',
      icon: Icons.build,
      confirmColor: Colors.red,
    );

    if (confirmed) {
      AdminLoadingDialog.show(message: 'جاري إصلاح قاعدة البيانات...');
      
      try {
        // استدعاء API لإصلاح قاعدة البيانات
        await _adminController.repairDatabase();
        await _adminController.refreshAllData(); // تحديث البيانات
        
        AdminLoadingDialog.hide();
        AdminMessageDialog.showSuccess(
          title: 'نجح العملية',
          message: 'تم إصلاح قاعدة البيانات بنجاح',
        );
      } catch (e) {
        AdminLoadingDialog.hide();
        AdminMessageDialog.showError(
          title: 'خطأ',
          message: 'فشل في إصلاح قاعدة البيانات: $e',
        );
      }
    }
  }

  /// عرض تقرير استخدام النظام
  void _showSystemUsageReport() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.analytics, color: Colors.green),
            SizedBox(width: 8),
            Text('تقرير استخدام النظام'),
          ],
        ),
        content: SizedBox(
          width: 500,
          height: 400,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildUsageStatCard('المستخدمون النشطون', '45', Icons.people, Colors.blue),
                _buildUsageStatCard('المهام المكتملة اليوم', '23', Icons.task_alt, Colors.green),
                _buildUsageStatCard('الرسائل المرسلة', '156', Icons.message, Colors.orange),
                _buildUsageStatCard('الملفات المرفوعة', '12', Icons.file_upload, Colors.purple),
                _buildUsageStatCard('وقت الاستجابة المتوسط', '1.2s', Icons.speed, Colors.red),
                _buildUsageStatCard('استخدام الذاكرة', '68%', Icons.memory, Colors.teal),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// عرض تقرير الأخطاء
  void _showErrorReport() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            SizedBox(width: 8),
            Text('تقرير الأخطاء'),
          ],
        ),
        content: SizedBox(
          width: 600,
          height: 400,
          child: Column(
            children: [
              _buildErrorSummary(),
              const SizedBox(height: 16),
              Expanded(
                child: _buildErrorList(),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => _clearErrorLogs(),
            child: const Text('مسح السجلات'),
          ),
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// عرض تقرير الأداء
  void _showPerformanceReport() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.speed, color: Colors.blue),
            SizedBox(width: 8),
            Text('تقرير الأداء'),
          ],
        ),
        content: SizedBox(
          width: 500,
          height: 400,
          child: SingleChildScrollView(
            child: Column(
              children: [
                _buildPerformanceMetric('وقت الاستجابة', '1.2s', Icons.timer, Colors.green),
                _buildPerformanceMetric('استخدام المعالج', '45%', Icons.memory, Colors.blue),
                _buildPerformanceMetric('استخدام الذاكرة', '68%', Icons.storage, Colors.orange),
                _buildPerformanceMetric('استخدام القرص', '23%', Icons.storage_outlined, Colors.purple),
                _buildPerformanceMetric('الطلبات في الثانية', '125', Icons.trending_up, Colors.teal),
                _buildPerformanceMetric('معدل الأخطاء', '0.1%', Icons.error_outline, Colors.red),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// عرض إعدادات التطبيق
  void _showAppSettings() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.settings, color: Colors.blue),
            SizedBox(width: 8),
            Text('إعدادات التطبيق'),
          ],
        ),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildAppSettingRow('اسم التطبيق', 'نظام إدارة المهام', Icons.apps),
              _buildAppSettingRow('إصدار التطبيق', '1.0.0', Icons.info),
              _buildAppSettingRow('اللغة الافتراضية', 'العربية', Icons.language),
              _buildAppSettingRow('المنطقة الزمنية', 'GMT+3', Icons.schedule),
              _buildAppSettingRow('تنسيق التاريخ', 'dd/MM/yyyy', Icons.date_range),
              _buildAppSettingRow('العملة الافتراضية', 'ريال سعودي', Icons.attach_money),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// عرض إعدادات الإشعارات
  void _showNotificationSettings() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.notifications, color: Colors.orange),
            SizedBox(width: 8),
            Text('إعدادات الإشعارات'),
          ],
        ),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildNotificationSettingRow('إشعارات البريد الإلكتروني', true, Icons.email),
              _buildNotificationSettingRow('الإشعارات الفورية', true, Icons.notifications_active),
              _buildNotificationSettingRow('إشعارات المهام', true, Icons.task),
              _buildNotificationSettingRow('إشعارات الرسائل', false, Icons.message),
              _buildNotificationSettingRow('إشعارات النظام', true, Icons.system_update),
              _buildNotificationSettingRow('الإشعارات الصوتية', false, Icons.volume_up),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => _saveNotificationSettings(),
            child: const Text('حفظ'),
          ),
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// إعادة تشغيل النظام
  Future<void> _restartSystem() async {
    final confirmed = await AdminConfirmDialog.show(
      title: 'إعادة تشغيل النظام',
      message: 'هل تريد إعادة تشغيل النظام؟\nسيتم قطع الاتصال مع جميع المستخدمين.',
      confirmText: 'إعادة تشغيل',
      icon: Icons.restart_alt,
      confirmColor: Colors.orange,
    );

    if (confirmed) {
      AdminLoadingDialog.show(message: 'جاري إعادة تشغيل النظام...');
      
      try {
        // استدعاء API لإعادة تشغيل النظام
        await _adminController.restartSystem();
        await _adminController.refreshAllData(); // تحديث البيانات
        
        AdminLoadingDialog.hide();
        AdminMessageDialog.showSuccess(
          title: 'نجح العملية',
          message: 'تم إعادة تشغيل النظام بنجاح',
        );
      } catch (e) {
        AdminLoadingDialog.hide();
        AdminMessageDialog.showError(
          title: 'خطأ',
          message: 'فشل في إعادة تشغيل النظام: $e',
        );
      }
    }
  }

  // ===== طرق مساعدة =====

  /// بناء صف إعداد كلمة المرور
  Widget _buildPasswordSettingRow(String title, String value, IconData icon, VoidCallback onTap) {
    return ListTile(
      leading: Icon(icon, color: Colors.orange),
      title: Text(title),
      subtitle: Text(value, style: const TextStyle(color: Colors.grey)),
      trailing: const Icon(Icons.edit),
      onTap: onTap,
    );
  }

  /// بناء صف إعداد الجلسة
  Widget _buildSessionSettingRow(String title, String value, IconData icon, VoidCallback onTap) {
    return ListTile(
      leading: Icon(icon, color: Colors.blue),
      title: Text(title),
      subtitle: Text(value, style: const TextStyle(color: Colors.grey)),
      trailing: const Icon(Icons.edit),
      onTap: onTap,
    );
  }

  /// تحرير الحد الأدنى لطول كلمة المرور
  void _editPasswordMinLength() {
    Get.snackbar('تحرير', 'تحرير الحد الأدنى لطول كلمة المرور');
  }

  /// تحرير تعقيد كلمة المرور
  void _editPasswordComplexity() {
    Get.snackbar('تحرير', 'تحرير متطلبات تعقيد كلمة المرور');
  }

  /// تحرير انتهاء صلاحية كلمة المرور
  void _editPasswordExpiry() {
    Get.snackbar('تحرير', 'تحرير مدة انتهاء صلاحية كلمة المرور');
  }

  /// تحرير تاريخ كلمات المرور
  void _editPasswordHistory() {
    Get.snackbar('تحرير', 'تحرير عدد كلمات المرور المحفوظة');
  }

  /// تحرير مهلة انتهاء الجلسة
  void _editSessionTimeout() {
    Get.snackbar('تحرير', 'تحرير مهلة انتهاء الجلسة');
  }

  /// تحرير الحد الأقصى للجلسات المتزامنة
  void _editMaxConcurrentSessions() {
    Get.snackbar('تحرير', 'تحرير الحد الأقصى للجلسات المتزامنة');
  }

  /// تحرير تذكر تسجيل الدخول
  void _editRememberLogin() {
    Get.snackbar('تحرير', 'تحرير إعدادات تذكر تسجيل الدخول');
  }

  /// إنهاء جميع الجلسات النشطة
  void _terminateAllSessions() {
    Get.snackbar('إنهاء', 'تم إنهاء جميع الجلسات النشطة');
  }

  /// الحصول على لون نوع السجل
  Color _getLogTypeColor(String action) {
    switch (action.toLowerCase()) {
      case 'create':
      case 'إنشاء':
        return Colors.green;
      case 'update':
      case 'تحديث':
        return Colors.blue;
      case 'delete':
      case 'حذف':
        return Colors.red;
      case 'login':
      case 'تسجيل دخول':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  /// الحصول على أيقونة نوع السجل
  IconData _getLogTypeIcon(String action) {
    switch (action.toLowerCase()) {
      case 'create':
      case 'إنشاء':
        return Icons.add;
      case 'update':
      case 'تحديث':
        return Icons.edit;
      case 'delete':
      case 'حذف':
        return Icons.delete;
      case 'login':
      case 'تسجيل دخول':
        return Icons.login;
      default:
        return Icons.info;
    }
  }

  /// تنسيق التاريخ والوقت
  String _formatDateTime(dynamic dateTime) {
    if (dateTime == null) return 'غير محدد';

    DateTime dt;
    if (dateTime is DateTime) {
      dt = dateTime;
    } else if (dateTime is String) {
      dt = DateTime.tryParse(dateTime) ?? DateTime.now();
    } else if (dateTime is int) {
      dt = DateTime.fromMillisecondsSinceEpoch(dateTime);
    } else {
      return 'تاريخ غير صالح';
    }

    return '${dt.day}/${dt.month}/${dt.year} ${dt.hour}:${dt.minute.toString().padLeft(2, '0')}';
  }

  /// تنسيق حجم الملف
  String _formatFileSize(double bytes) {
    if (bytes < 1024) return '${bytes.toStringAsFixed(0)} B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// عرض تفاصيل السجل
  void _showLogDetails(dynamic log) {
    Get.dialog(
      AlertDialog(
        title: const Text('تفاصيل السجل'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الإجراء: ${log.action}'),
            Text('التفاصيل: ${log.details ?? 'لا يوجد'}'),
            Text('وصف التغيير: ${log.changeDescription ?? 'لا يوجد'}'),
            Text('المستخدم: ${log.userId}'),
            Text('المهمة: ${log.taskId > 0 ? log.taskId : 'عام'}'),
            Text('التاريخ: ${_formatDateTime(log.timestamp)}'),
            if (log.oldValue != null) Text('القيمة القديمة: ${log.oldValue}'),
            if (log.newValue != null) Text('القيمة الجديدة: ${log.newValue}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// تأكيد استعادة النسخة الاحتياطية
  Future<void> _confirmRestoreBackup(dynamic backup) async {
    final confirmed = await AdminConfirmDialog.show(
      title: 'تأكيد الاستعادة',
      message: 'هل تريد استعادة هذه النسخة الاحتياطية؟\n\nتحذير: سيتم استبدال البيانات الحالية!',
      confirmText: 'استعادة',
      icon: Icons.warning,
      confirmColor: Colors.red,
    );

    if (confirmed) {
      AdminLoadingDialog.show(message: 'جاري استعادة النسخة الاحتياطية...');

      try {
        await _adminController.restoreBackup(backup.id);
        await _adminController.refreshAllData();

        AdminLoadingDialog.hide();
        Get.back(); // إغلاق الحوار
        AdminMessageDialog.showSuccess(
          title: 'نجح العملية',
          message: 'تم استعادة النسخة الاحتياطية بنجاح',
        );
      } catch (e) {
        AdminLoadingDialog.hide();
        AdminMessageDialog.showError(
          title: 'خطأ',
          message: 'فشل في استعادة النسخة الاحتياطية: $e',
        );
      }
    }
  }

  // ===== طرق مساعدة إضافية =====

  /// بناء صف معلومات قاعدة البيانات
  Widget _buildDatabaseInfoRow(String label, String value, {bool isStatus = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
          isStatus
              ? Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(value, style: const TextStyle(color: Colors.white, fontSize: 12)),
                )
              : Text(value, style: const TextStyle(color: Colors.grey)),
        ],
      ),
    );
  }

  /// اختبار اتصال قاعدة البيانات
  void _testDatabaseConnection() {
    Get.snackbar('اختبار الاتصال', 'الاتصال بقاعدة البيانات ناجح');
  }

  /// بناء بطاقة إحصائية للاستخدام
  Widget _buildUsageStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color,
          child: Icon(icon, color: Colors.white),
        ),
        title: Text(title),
        trailing: Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ),
    );
  }

  /// بناء ملخص الأخطاء
  Widget _buildErrorSummary() {
    return Row(
      children: [
        Expanded(
          child: _buildErrorSummaryCard('أخطاء اليوم', '3', Colors.red),
        ),
        Expanded(
          child: _buildErrorSummaryCard('تحذيرات', '12', Colors.orange),
        ),
        Expanded(
          child: _buildErrorSummaryCard('معلومات', '45', Colors.blue),
        ),
      ],
    );
  }

  /// بناء بطاقة ملخص الأخطاء
  Widget _buildErrorSummaryCard(String title, String count, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text(
              count,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(title, style: const TextStyle(fontSize: 12)),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة الأخطاء
  Widget _buildErrorList() {
    final errors = [
      {'level': 'خطأ', 'message': 'فشل في الاتصال بقاعدة البيانات', 'time': '10:30'},
      {'level': 'تحذير', 'message': 'استخدام مرتفع للذاكرة', 'time': '09:15'},
      {'level': 'معلومات', 'message': 'تم تسجيل دخول مستخدم جديد', 'time': '08:45'},
    ];

    return ListView.builder(
      itemCount: errors.length,
      itemBuilder: (context, index) {
        final error = errors[index];
        return ListTile(
          leading: CircleAvatar(
            backgroundColor: _getErrorLevelColor(error['level']!),
            child: Icon(
              _getErrorLevelIcon(error['level']!),
              color: Colors.white,
              size: 16,
            ),
          ),
          title: Text(error['message']!),
          subtitle: Text('الوقت: ${error['time']}'),
          trailing: IconButton(
            icon: const Icon(Icons.delete),
            onPressed: () => _deleteError(index),
          ),
        );
      },
    );
  }

  /// الحصول على لون مستوى الخطأ
  Color _getErrorLevelColor(String level) {
    switch (level) {
      case 'خطأ': return Colors.red;
      case 'تحذير': return Colors.orange;
      case 'معلومات': return Colors.blue;
      default: return Colors.grey;
    }
  }

  /// الحصول على أيقونة مستوى الخطأ
  IconData _getErrorLevelIcon(String level) {
    switch (level) {
      case 'خطأ': return Icons.error;
      case 'تحذير': return Icons.warning;
      case 'معلومات': return Icons.info;
      default: return Icons.help;
    }
  }

  /// حذف خطأ
  void _deleteError(int index) {
    Get.snackbar('حذف', 'تم حذف الخطأ');
  }

  /// مسح سجلات الأخطاء
  void _clearErrorLogs() {
    Get.snackbar('مسح', 'تم مسح جميع سجلات الأخطاء');
  }

  /// بناء مقياس الأداء
  Widget _buildPerformanceMetric(String title, String value, IconData icon, Color color) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(icon, color: color),
        title: Text(title),
        trailing: Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ),
    );
  }

  /// بناء صف إعداد التطبيق
  Widget _buildAppSettingRow(String title, String value, IconData icon) {
    return ListTile(
      leading: Icon(icon, color: Colors.blue),
      title: Text(title),
      subtitle: Text(value),
      trailing: const Icon(Icons.edit),
      onTap: () => _editAppSetting(title),
    );
  }

  /// تحرير إعداد التطبيق
  void _editAppSetting(String setting) {
    Get.snackbar('تحرير', 'تحرير $setting');
  }

  /// بناء صف إعداد الإشعارات
  Widget _buildNotificationSettingRow(String title, bool value, IconData icon) {
    return ListTile(
      leading: Icon(icon, color: Colors.orange),
      title: Text(title),
      trailing: Switch(
        value: value,
        onChanged: (newValue) => _toggleNotificationSetting(title, newValue),
      ),
    );
  }

  /// تبديل إعداد الإشعارات
  void _toggleNotificationSetting(String setting, bool value) {
    Get.snackbar('تحديث', '$setting: ${value ? 'مفعل' : 'معطل'}');
  }

  /// حفظ إعدادات الإشعارات
  void _saveNotificationSettings() {
    Get.back();
    Get.snackbar('حفظ', 'تم حفظ إعدادات الإشعارات');
  }
}
